import 'package:ambulancia_app/models/service-update.dart';
import 'package:ambulancia_app/shared/utils/offline-first/last-synchronization-dates/last_synchronization_dates.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MockSharedPreferences extends Fake implements SharedPreferences {
  final Map<String, String> _data = {};

  @override
  Future<bool> setString(String key, String value) async {
    _data[key] = value;
    return true;
  }

  @override
  String? getString(String key) {
    return _data[key];
  }

  @override
  Future<bool> remove(String key) async {
    _data.remove(key);
    return true;
  }
}

void main() {
  group('LastSynchronizationDates', () {
    final mockPrefs = MockSharedPreferences();

    final lastSyncDates = LastSynchronizationDates(mockPrefs);

    test(
        'getRequestServicesDataTime should return an empty list if there is no data stored',
        () async {
      final result = await lastSyncDates.getRequestServicesDataTime();
      expect(result, isEmpty);
    });

    test('saveServiceDateTimeUpdate must add a new service update', () async {
      final serviceUpdate = RequestServiceDataTime(
        serviceName: 'MyService',
        lastUpdate: DateTime(2023, 1, 5, 10, 30),
      );

      await lastSyncDates.saveServiceDateTimeUpdate(
          serviceUpdate: serviceUpdate);

      final savedUpdates = await lastSyncDates.getRequestServicesDataTime();
      expect(
          savedUpdates,
          contains(predicate((dynamic update) =>
              update is RequestServiceDataTime &&
              update.serviceName == serviceUpdate.serviceName &&
              update.lastUpdate == serviceUpdate.lastUpdate)));
    });

    test(
        'getLastDateTimeUpdate should return the last update time for a service',
        () async {
      final serviceUpdate = RequestServiceDataTime(
        serviceName: 'MyService',
        lastUpdate: DateTime(2023, 1, 5, 10, 30),
      );

      await lastSyncDates.saveServiceDateTimeUpdate(
          serviceUpdate: serviceUpdate);

      final lastUpdate = await lastSyncDates.getLastDateTimeUpdate('MyService');
      expect(lastUpdate, equals(serviceUpdate.lastUpdate));
    });

    test('saveServiceDateTimeUpdate must add a new service update', () async {
      final serviceUpdate = RequestServiceDataTime(
        serviceName: 'MyService',
        lastUpdate: DateTime(2023, 1, 5, 10, 30),
      );

      await lastSyncDates.saveServiceDateTimeUpdate(
          serviceUpdate: serviceUpdate);

      final savedUpdates = await lastSyncDates.getRequestServicesDataTime();
      expect(
          savedUpdates,
          contains(predicate((dynamic update) =>
              update is RequestServiceDataTime &&
              update.serviceName == serviceUpdate.serviceName &&
              update.lastUpdate == serviceUpdate.lastUpdate)));
    });

    test('removeAllLastSynchronizationDates must remove the data', () async {
      await mockPrefs.setString('lastSynchronizationDates', 'some data');

      await lastSyncDates.removeAllLastSynchronizationDates();

      final dadosSalvos = await mockPrefs.getString('lastSynchronizationDates');
      expect(dadosSalvos, isNull);
    });
  });
}
