import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:ambulancia_app/models/funcoes_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  FuncoesModel? modelTest;
  Map<String, dynamic>? json;
  setUpAll(
    () {
      modelTest = FuncoesModel(
        codFuncao: 1,
        descFuncao: "Enfermeiro",
        funcionarios: [
          FuncionarioModel(
            codFuncionario: 1,
            nomeFuncionario: "Enfermeiro 1",
          ),
          FuncionarioModel(
            codFuncionario: 2,
            nomeFuncionario: "Enfermeiro 2",
          ),
        ],
      );
      json = {
        "codFuncao": 1,
        "descFuncao": "Enfermeiro",
        "funcionarios": [
          {"codFuncionario": 1, "nomeFuncionario": "Enfermeiro 1"},
          {
            "codFuncionario": 2,
            "nomeFuncionario": "Enfermeiro 2",
            "descFuncao": "Enfermeiro"
          },
        ]
      };
    },
  );

  group(
    "isInstanceOf FuncoesModel model tests",
    () {
      test("Should be return instance of FuncoesModel", () {
        expect(modelTest, isInstanceOf<FuncoesModel>());
      });

      test("Should be return instance of Funcionario", () {
        expect(modelTest?.funcionarios[0], isInstanceOf<FuncionarioModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of FuncoesModel to json", () {
      expect(modelTest!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of FuncoesModel from json", () {
      expect(FuncoesModel.fromJson(json!), isInstanceOf<FuncoesModel>());
    });

    test("Should be return instance of Funcionario to json", () {
      expect(modelTest!.funcionarios[0].toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of Funcionario from json", () {
      expect(FuncionarioModel.fromJson(json!["funcionarios"][0]),
          isInstanceOf<FuncionarioModel>());
    });
  });

  group(
    "isInstanceOf FuncoesModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(json!["codFuncao"], isInstanceOf<int>());
        expect(json!["descFuncao"], isInstanceOf<String>());
        expect(json!["funcionarios"], isInstanceOf<List<dynamic>>());
      });

      test("Should be return type of the json Funcionario", () {
        expect(json!["funcionarios"][0]["codFuncionario"], isInstanceOf<int>());
        expect(json!["funcionarios"][0]["nomeFuncionario"],
            isInstanceOf<String>());
      });
      test("CanÂ´t return if is null", () {
        expect(json!["codFuncao"] == null, false);
        expect(json!["descFuncao"] == null, false);
        expect(json!["funcionarios"] == null, false);
        expect(json!["funcionarios"][0]["codFuncionario"] == null, false);
        expect(json!["funcionarios"][0]["nomeFuncionario"] == null, false);
      });
    },
  );
}
