import 'package:ambulancia_app/models/geolocation-config.model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  GeolocationConfigModel? testGeolocationConfigModel;
  Map<String, dynamic>? jsonGeolocationConfigModel;
  setUpAll(
    () {
      testGeolocationConfigModel =
          GeolocationConfigModel(distanceFilter: 1, timeIntervalSec: 1);
      jsonGeolocationConfigModel = {
        "distanceFilter": 1,
        "timeIntervalSec": 1,
      };
    },
  );

  group(
    "isInstanceOf GeolocationConfigModel model tests",
    () {
      test("Should be return instance of GeolocationConfigModel", () {
        expect(
            testGeolocationConfigModel, isInstanceOf<GeolocationConfigModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of GeolocationConfigModel to json", () {
      expect(testGeolocationConfigModel!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of GeolocationConfigModel from json", () {
      expect(GeolocationConfigModel.fromJson(jsonGeolocationConfigModel!),
          isInstanceOf<GeolocationConfigModel>());
    });
  });

  group(
    "isInstanceOf GeolocationConfigModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(
            jsonGeolocationConfigModel!["distanceFilter"], isInstanceOf<int>());
        expect(jsonGeolocationConfigModel!["timeIntervalSec"],
            isInstanceOf<int>());
      });

      test("Should be return test to STRING", () {
        expect(testGeolocationConfigModel!.toString(), isInstanceOf<String>());
        expect(testGeolocationConfigModel!.toString(),
            jsonGeolocationConfigModel!.toString());
      });
    },
  );
}
