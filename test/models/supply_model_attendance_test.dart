import 'package:ambulancia_app/models/supply_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  SupplyAttendance? testSupplyAttendanceModel;
  Map<String, dynamic>? jsonSupplyAttendanceModel;
  SupplyModel? testSupplyAttendanceModelTrue;
  Map<String, dynamic>? jsonSupplyAttendanceModelTrue;
  setUpAll(
    () {
      testSupplyAttendanceModel = SupplyAttendance(
        codigoMaterial: 1,
        descricao: "cdasd",
        quantidade: 1,
      );
      jsonSupplyAttendanceModel = {
        "codigoMaterial": 1,
        "descricao": "cdasd",
        "quantidade": 1,
      };
      testSupplyAttendanceModelTrue = SupplyModel(
        code: 1,
        description: "cdasd",
      );
      jsonSupplyAttendanceModelTrue = {
        "codigo": 1,
        "descricao": "cdasd",
      };
    },
  );

  group(
    "isInstanceOf SupplyAttendance model tests",
    () {
      test("Should be return instance of SupplyAttendance", () {
        expect(testSupplyAttendanceModel, isInstanceOf<SupplyAttendance>());
      });
      test("Should be return instance of SupplyModel", () {
        expect(testSupplyAttendanceModelTrue, isInstanceOf<SupplyModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of SupplyAttendance to json", () {
      expect(testSupplyAttendanceModel!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of SupplyAttendance from json", () {
      expect(SupplyAttendance.fromJson(jsonSupplyAttendanceModel!),
          isInstanceOf<SupplyAttendance>());
    });
    test("Should be return instance of SupplyModel to json", () {
      expect(testSupplyAttendanceModelTrue!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of SupplyModel from json", () {
      expect(SupplyModel.fromJson(jsonSupplyAttendanceModelTrue!),
          isInstanceOf<SupplyModel>());
    });
  });

  group(
    "isInstanceOf SupplyAttendance json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(
            jsonSupplyAttendanceModel!["codigoMaterial"], isInstanceOf<int>());
        expect(jsonSupplyAttendanceModel!["descricao"], isInstanceOf<String>());
        expect(jsonSupplyAttendanceModel!["quantidade"], isInstanceOf<int>());
      });
    },
  );

  group(
    "isInstanceOf SupplyModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(jsonSupplyAttendanceModelTrue!["codigo"], isInstanceOf<int>());
        expect(jsonSupplyAttendanceModelTrue!["descricao"],
            isInstanceOf<String>());
      });
    },
  );
}
