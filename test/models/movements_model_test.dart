import 'package:ambulancia_app/models/movements_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  MovementsModel? testMovementsModel;
  Map<String, dynamic>? jsonMovementsModel;
  MovementObject? movementObject;

  setUp(() {
    testMovementsModel =
        MovementsModel(attendance: 1, moviments: <MovementObject>[
      MovementObject(
        statusCode: 1,
        movimentDate: '2023-01-31 16:19:15',
        statusName: 'Initial',
      )
    ]);

    jsonMovementsModel = {
      'attendance': 1,
      'moviments': [
        {
          'statusCode': 4,
          'movimentDate': '2023-01-31 16:19:15',
          'statusName': 'Initial',
        }
      ],
      'statusCode': 1,
      'nomeStatus': 'Initial'
    };
    movementObject = MovementObject(
      statusCode: 1,
      movimentDate: '2023-01-31 16:19:15',
      statusName: 'Initial',
    );
  });

  test("should be return instance of MovementsModel", () {
    expect(testMovementsModel, isInstanceOf<MovementsModel>());
  });

  test("should be return instance of MovementsModel from jsonMovementsModel",
      () {
    expect(MovementsModel.fromJson(jsonMovementsModel!),
        isInstanceOf<MovementsModel>());
  });

  test("test function formated sucess date MovementObject", () {
    expect(movementObject!.dataMovimentacaoFormated, "16:19\n31/01/2023");
  });

  test("test function falhed failure date MovementObject", () {
    movementObject!.movimentDate = "31/01/2023 12:00";
    expect(movementObject!.dataMovimentacaoFormated, "");
  });
}
