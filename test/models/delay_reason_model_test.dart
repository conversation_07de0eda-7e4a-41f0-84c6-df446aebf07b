import 'package:ambulancia_app/models/delay_reason_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DelayReasonModel', () {
    test('fromJson() deve retornar um objeto DelayReasonModel válido', () {
      final json = {
        'delayReasonCode': 1,
        'delayReasonName': 'test',
      };

      final model = DelayReasonModel.fromJson(json);

      expect(model.delayReasonCode, 1);
      expect(model.delayReasonName, 'test');
    });

    test('toJson() deve retornar um mapa JSON válido', () {
      final model = DelayReasonModel(
        delayReasonCode: 1,
        delayReasonName: 'test',
      );

      final json = model.toJson();

      expect(json['delayReasonCode'], 1);
      expect(json['delayReasonName'], 'test');
    });
  });
}
