import 'package:ambulancia_app/models/address-infos.dart';
import 'package:ambulancia_app/models/adress_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  AddressInfos? testAddressInfosModel;
  Map<String, dynamic>? jsonAddressInfosModel;
  setUpAll(
    () {
      testAddressInfosModel = AddressInfos(
        1,
        1,
        null,
        DateTime.now().toString(),
      );
      jsonAddressInfosModel = {
        "lat": 1.0,
        "lng": 1.0,
        "address": null,
        "createdAt": "2021-09-01T00:00:00.000Z",
      };
    },
  );

  group(
    "isInstanceOf AddressInfos model tests",
    () {
      test("Should be return instance of AddressInfos", () {
        expect(testAddressInfosModel, isInstanceOf<AddressInfos>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of AddressInfos to json", () {
      expect(testAddressInfosModel!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of AddressInfos from json", () {
      expect(AddressInfos.fromJson(jsonAddressInfosModel!),
          isInstanceOf<AddressInfos>());
    });

    test("Should be return instance of AddressInfos from json", () {
      final jsonAddressInfosModel = {
        "lat": 1.0,
        "lng": 1.0,
        "address": {
          "numero": "123",
          "tipoLogradouro": "Rua",
          "bairro": "Centro",
          "cidade": "São Paulo",
          "CEP": "01000-000",
          "complemento": "Apto 101",
          "logradouro": "Paulista",
          "uf": "SP",
          "tipoEndereco": "ATTENDANCE"
        },
        "createdAt": "2021-09-01T00:00:00.000Z",
      };

      expect(AddressInfos.fromJson(jsonAddressInfosModel).address,
          isInstanceOf<AddressModel>());
    });
  });

  group(
    "isInstanceOf AddressInfos json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(jsonAddressInfosModel!["lat"], isInstanceOf<double>());
        expect(jsonAddressInfosModel!["lng"], isInstanceOf<double>());
        expect(
            jsonAddressInfosModel!["address"], isInstanceOf<AddressModel?>());
        expect(jsonAddressInfosModel!["createdAt"], isInstanceOf<String>());
      });

      test("Should be return validation of createdAt", () {
        expect(DateTime.parse(jsonAddressInfosModel!["createdAt"]),
            isInstanceOf<DateTime>());
      });
    },
  );
}
