import 'package:ambulancia_app/models/adress_model.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  AttendanceModel? testAttendanceModel;
  Map<String, dynamic>? jsonAttendanceModel;
  AddressModel? testAddressModel;
  Map<String, dynamic>? jsonAdressModel;

  setUpAll(
    () {
      jsonAttendanceModel = {
        "numAtendimento": 183780,
        "codUnimed": 63,
        "codVeiculo": "14",
        "codTipoCliente": "1",
        "nomeTipoCliente": "UNIMED URGENTE",
        "codTipoAtendimento": "C4",
        "nomeTipoAtendimento": "MINIMA URGENCIA",
        "codMotivoNaoAtendimento": "4",
        "descricaoMotivoNaoAtendimento": "REMOCAO SIMPLES",
        "codStatus": 6,
        "nomeStatus": "SAÍDA ORIGEM",
        "unimedCarteira": 63,
        "codigoCarteira": 2006392563,
        "dvCarteira": "0",
        "idade": 24,
        "sintomaAcontecendo": "TESTE",
        "sintomaComecou": "TESTE",
        "sintomaHistorico": "TESTE",
        "sintomaQuando": "TESTE",
        "observacaoMedicoRegulador": "TESTE",
        "dataAtendimento": "2023-01-18 14:28:39",
        "equipe": [
          {
            "codFuncionario": 3281,
            "nomeFuncionario": "ORISMAR VELOSO LOIOLA",
            "codFuncao": 24,
            "descFuncao": "MOTORISTA SOCORRISTA"
          },
          {
            "codFuncionario": 9663,
            "nomeFuncionario": "JOSE ERNANDO MESQUITA MOTA",
            "codFuncao": 1,
            "descFuncao": "AUXILIAR DE ENFERMAGEM"
          }
        ],
        "nome": "DAVI BEZERRA N GOMES",
        "rg": "20079542411",
        "cpf": "60866109323",
        "dataNascimento": "1998-01-25 00:00:00",
        "enderecoAtendimento": {
          "numero": "77",
          "tipoLogradouro": "AV",
          "logradouro": "DOM MANUEL",
          "bairro": "CENTRO",
          "cidade": "FORTALEZA",
          "CEP": "60060090",
          "complemento": "Casa",
          "uf": "CE"
        },
        "enderecoDestino": {
          "numero": "950",
          "tipoLogradouro": "R",
          "logradouro": "ANDRADE FURTADO",
          "bairro": "COCO",
          "cidade": "FORTALEZA",
          "CEP": "60192070",
          "complemento": "APTO 501"
        },
        "remocao": "S",
        "codigoDestinoPaciente": "1",
        "descricaoDestinoPaciente": "HOSPITAL SAO RAIMUNDO",
        "materiais": [
          {"codigoMaterial": 1, "descricao": "AAS", "quantidade": 50}
        ]
      };
      testAttendanceModel = AttendanceModel.fromJson(jsonAttendanceModel!);

      jsonAdressModel = {
        "numero": "950",
        "tipoLogradouro": "R",
        "logradouro": "ANDRADE FURTADO",
        "bairro": "COCO",
        "cidade": "FORTALEZA",
        "CEP": "60192070",
        "complemento": "APTO 501"
      };

      testAddressModel = AddressModel(
        numero: '950',
        tipoLogradouro: "R",
        logradouro: "ANDRADE FURTADO",
        bairro: "COCO",
        cidade: "FORTALEZA",
        cep: "60192070",
        complemento: "APTO 501",
      );
    },
  );

  test("should be return instance of AddressModel", () {
    expect(testAddressModel, isInstanceOf<AddressModel>());
  });

  test("should be return instance of AddressModel from jsonAdressModel", () {
    expect(
        AddressModel.fromJson(jsonAdressModel!), isInstanceOf<AddressModel>());
  });

  test("test if the service address is valid return true", () {
    expect(testAttendanceModel!.enderecoAtendimento!.isValid(), isTrue);
  });

  test("test if the service address is invalid return false", () {
    testAttendanceModel!.enderecoAtendimento!.cidade = null;
    expect(testAttendanceModel!.enderecoAtendimento!.isValid(), isFalse);
  });

  test("test if the destination address is valid return true", () {
    expect(testAttendanceModel!.enderecoDestino!.isValid(), isTrue);
  });
  test("test if the destination address is valid return false", () {
    testAttendanceModel!.enderecoAtendimento!.cidade = null;
    expect(testAttendanceModel!.enderecoDestino!.isValid(), isTrue);
  });

  test(
      "test to check the type of service address, if it is a service address, if it is a return true",
      () {
    testAttendanceModel!.enderecoAtendimento?.tipoEndereco = 'ATTENDANCE';
    expect(testAttendanceModel!.enderecoAtendimento?.isAttendance, isTrue);
  });

  test(
      "test to check the type of service address, if it is a service address, if it is a return false",
      () {
    testAttendanceModel!.enderecoAtendimento?.tipoEndereco = null;
    expect(testAttendanceModel!.enderecoAtendimento?.isAttendance, isFalse);
  });

  test(
      "test to check the type of address if it is for the destination, if it is return true",
      () {
    testAttendanceModel!.enderecoDestino?.tipoEndereco = 'DESTINY';
    expect(testAttendanceModel!.enderecoDestino?.isDestiny, isTrue);
  });

  test(
      "test to check the type of address if it is for the destination, if it is return false",
      () {
    expect(testAttendanceModel!.enderecoDestino?.isAttendance, isFalse);
  });
}
