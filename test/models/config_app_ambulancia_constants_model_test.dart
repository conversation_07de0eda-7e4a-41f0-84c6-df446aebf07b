import 'package:ambulancia_app/models/config_app_ambulancia_constants_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ConfigAppAmbulanciaConstants', () {
    test('fromJson should parse JSON correctly', () {
      final json = {
        'nervous': {
          'glasgowComaScaleMin': 3,
          'glasgowComaScaleMax': 15,
        },
        'cardiovascular': {
          'heartRateMin': 60,
          'heartRateMax': 100,
          'respiratoryRateMin': 12,
          'respiratoryRateMax': 20,
          'bloodPressureAMin': 90,
          'bloodPressureAMax': 120,
          'bloodPressureBMin': 60,
          'bloodPressureBMax': 80,
          'oxygenSaturationMin': 95,
          'oxygenSaturationMax': 100,
          'bloodGlucoseMin': 70,
          'bloodGlucoseMax': 140,
          'temperatureMin': 36.5,
          'temperatureMax': 37.5,
        },
        'imageQuality': {
          'maxHeightDefault': 1080,
          'maxQualityDefault': 90,
        },
      };

      final config = ConfigAppAmbulanciaConstants.fromJson(json);

      expect(config.nervous.glasgowComaScaleMin, 3);
      expect(config.nervous.glasgowComaScaleMax, 15);

      expect(config.cardiovascular.heartRateMin, 60);
      expect(config.cardiovascular.heartRateMax, 100);
      expect(config.cardiovascular.respiratoryRateMin, 12);
      expect(config.cardiovascular.respiratoryRateMax, 20);
      expect(config.cardiovascular.bloodPressureAMin, 90);
      expect(config.cardiovascular.bloodPressureAMax, 120);
      expect(config.cardiovascular.bloodPressureBMin, 60);
      expect(config.cardiovascular.bloodPressureBMax, 80);
      expect(config.cardiovascular.oxygenSaturationMin, 95);
      expect(config.cardiovascular.oxygenSaturationMax, 100);
      expect(config.cardiovascular.bloodGlucoseMin, 70);
      expect(config.cardiovascular.bloodGlucoseMax, 140);
      expect(config.cardiovascular.temperatureMin, 36.5);
      expect(config.cardiovascular.temperatureMax, 37.5);

      expect(config.imageQuality.maxHeightDefault, 1080);
      expect(config.imageQuality.maxQualityDefault, 90);
    });

    test('Nervous.fromJson should throw if required fields are missing', () {
      final json = {
        'glasgowComaScaleMin': 3,
      };

      expect(() => Nervous.fromJson(json), throwsA(isA<TypeError>()));
    });

    test('Cardiovascular.fromJson should throw if required fields are missing',
        () {
      final json = {
        'heartRateMin': 60,
        'heartRateMax': 100,
      };

      expect(() => Cardiovascular.fromJson(json), throwsA(isA<TypeError>()));
    });

    test('ImageQuality.fromJson should throw if required fields are missing',
        () {
      final json = {
        'maxHeightDefault': 1080,
      };

      expect(() => ImageQuality.fromJson(json), throwsA(isA<TypeError>()));
    });
  });
}
