import 'package:ambulancia_app/models/reason_pre_hospital_discharge_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ReasonPreHospitalDischargeModel', () {
    test('fromJson deve criar uma instância corretamente', () {
      final json = {
        'codeReason': 10,
        'nameReason': 'Alta médica',
      };

      final model = ReasonPreHospitalDischargeModel.fromJson(json);

      expect(model.codeReason, 10);
      expect(model.nameReason, 'Alta médica');
    });

    test('toJson deve retornar um mapa correto', () {
      final model = ReasonPreHospitalDischargeModel(
        codeReason: 20,
        nameReason: 'Transferência',
      );

      final json = model.toJson();

      expect(json['codeReason'], 20);
      expect(json['nameReason'], 'Transferência');
    });
  });
}
