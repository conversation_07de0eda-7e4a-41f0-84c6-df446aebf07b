import 'package:ambulancia_app/models/conduct_model.dart';
import 'package:ambulancia_app/models/image_model.dart';
import 'package:ambulancia_app/shared/utils/photo.constants.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  ConductModel? testConductModel;
  Map<String, dynamic>? jsonConductModel;

  setUpAll(() {
    testConductModel = ConductModel(
      numAtendimento: 123,
      observacaoConduta: 'observacaoConduta',
      codDiagnostico: 'codDiagnostico',
      descricaoDiagnostico: 'descricaoDiagnostico',
      descricaoTerapeutica: 'descricaoTerapeutica',
      attachments: <ImageModel>[
        ImageModel(
          uniurgMotivoAnexoEnum:
              int.parse(photoReasonAnnexEnum.anexo_conduta.value),
          fileName: 'anexo_c12322122.png',
          fileBase64: "base64",
          imgNumber: 44123,
          isNewAttachment: true,
          isCache: true,
        ),
      ],
      operatorImage: ImageModel(
        uniurgMotivoAnexoEnum: int.parse(photoReasonAnnexEnum.conduta.value),
        fileName: 'c_123.png',
        fileBase64: "base64",
        imgNumber: 0,
      ),
    );

    jsonConductModel = {
      "numAtendimento": 123,
      "observacaoConduta": "observacaoConduta",
      "codDiagnostico": "codDiagnostico",
      "descricaoDiagnostico": "descricaoDiagnostico",
      "descricaoTerapeutica": "descricaoTerapeutica",
      "attachments": [
        {
          "uniurgMotivoAnexoEnum": 1,
          "fileName": "anexo_c12322122.png",
          "fileBase64": "base64",
          "imgNumber": 44123,
          "isNewAttachment": true,
          "isCache": true
        }
      ],
      "operatorImage": {
        "uniurgMotivoAnexoEnum": 2,
        "fileName": "c_123.png",
        "fileBase64": "base64",
        "imgNumber": 0
      }
    };
  });

  test("should be return instance of ConductModel", () {
    expect(testConductModel, isInstanceOf<ConductModel>());
  });

  test("should be return instance of ConductModel from jsonConductModel", () {
    expect(
        ConductModel.fromJson(jsonConductModel!), isInstanceOf<ConductModel>());
  });

  test("Should be return instance of ConductModel to json", () {
    expect(testConductModel!.toJson(), isInstanceOf<Map<String, dynamic>>());
  });
}
