import 'package:ambulancia_app/models/attachment_img_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AttachmentImageModel', () {
    final attachmentImageData = {
      'nomeArquivo': 'test_file.png',
      'arquivoBase64': 'base64_encoded_data',
    };

    test('from<PERSON><PERSON> creates a valid AttachmentImageModel instance', () {
      final attachmentImageModel =
          AttachmentImageModel.fromJson(attachmentImageData);

      expect(attachmentImageModel.nomeArquivo, equals('test_file.png'));
      expect(attachmentImageModel.arquivoBase64, equals('base64_encoded_data'));
    });

    test('to<PERSON><PERSON> returns a valid Map', () {
      final attachmentImageModel = AttachmentImageModel(
        nomeArquivo: 'test_file.png',
        arquivoBase64: 'base64_encoded_data',
      );

      final jsonMap = attachmentImageModel.toJson();

      expect(jsonMap['nomeArquivo'], equals('test_file.png'));
      expect(jsonMap['arquivoBase64'], equals('base64_encoded_data'));
    });
  });
}
