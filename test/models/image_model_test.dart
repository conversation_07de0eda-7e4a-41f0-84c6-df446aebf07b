import 'package:ambulancia_app/models/image_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ImageModel', () {
    final imageModelData = {
      'uniurgMotivoAnexoEnum': 1,
      'fileName': 'test_image.png',
      'fileBase64': 'base64_encoded_data',
      'imgNumber': 123,
      'isNewAttachment': true,
    };

    test('from<PERSON><PERSON> creates a valid ImageModel instance', () {
      final imageModel = ImageModel.fromJson(imageModelData);

      expect(imageModel.uniurgMotivoAnexoEnum, equals(1));
      expect(imageModel.fileName, equals('test_image.png'));
      expect(imageModel.fileBase64, equals('base64_encoded_data'));
      expect(imageModel.imgNumber, equals(123));
      expect(imageModel.isNewAttachment, isTrue);
      expect(imageModel.isCache, isFalse);
    });

    test('to<PERSON><PERSON> returns a valid Map', () {
      final imageModel = ImageModel(
        uniurgMotivoAnexoEnum: 2,
        fileName: 'another_image.png',
        fileBase64: 'another_base64_encoded_data',
        imgNumber: 456,
        isNewAttachment: false,
      );

      final jsonMap = imageModel.toJson();

      expect(jsonMap['uniurgMotivoAnexoEnum'], equals(2));
      expect(jsonMap['fileName'], equals('another_image.png'));
      expect(jsonMap['fileBase64'], equals('another_base64_encoded_data'));
    });
  });
}
