import 'package:ambulancia_app/models/user_model.dart';
import 'package:ambulancia_app/models/vehicle_model.dart';
import 'package:ambulancia_app/shared/utils/string_utils.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  UserCredentials? testUserCredentialsModel;
  Map<String, dynamic>? jsonUserCredentialsModel;
  LoginDataModel? testLoginDataModel;
  Map<String, dynamic>? jsonLoginDataModel;
  UserModel? testUserModel;
  Map<String, dynamic>? jsonUserModel;
  setUpAll(
    () {
      testUserCredentialsModel = UserCredentials(
        user: "user!.",
        password: "password",
      );
      jsonUserCredentialsModel = {"user": "user", "password": "password"};
      testLoginDataModel = LoginDataModel(
        userCredentials: testUserCredentialsModel!,
        vehicleModel: VehicleModel(
          codVeiculo: "dsa-ds",
          nomeVeiculo: "ambulancia",
        ),
      );
      jsonLoginDataModel = {
        "userCredentials": {"user": "user", "password": "password"},
        "vehicleModel": {"codVeiculo": "dsa-ds", "nomeVeiculo": "ambulancia"}
      };
      testUserModel = UserModel(
        cpf: "04157908163",
        nome: "nome",
        telefone: "123456789",
        email: "<EMAIL>",
        cnh: "123456789",
        coren: "123456789",
      );
      jsonUserModel = {
        "cpf": "04157908163",
        "nome": "Angel",
        "telefone": "123456789",
        "email": "<EMAIL>",
        "cnh": "123456789",
        "coren": "123456789",
      };
    },
  );

  group(
    "isInstanceOf UserCredentials model tests",
    () {
      test("Should be return instance of UserCredentials", () {
        expect(testUserCredentialsModel, isInstanceOf<UserCredentials>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of UserCredentials to json", () {
      expect(testUserCredentialsModel!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of UserCredentials from json", () {
      expect(UserCredentials.fromJson(jsonUserCredentialsModel!),
          isInstanceOf<UserCredentials>());
    });
  });

  group(
    "isInstanceOf UserCredentials json to model type test",
    () {
      test("User formatted", () {
        expect(testUserCredentialsModel!.userClean,
            jsonUserCredentialsModel!["user"].replaceAll(RegExp(r'^|\D'), ''));
      });
      test("Can´t return if is null", () {
        expect(jsonUserCredentialsModel!["user"] == null, false);
        expect(jsonUserCredentialsModel!["password"] == null, false);
      });
      test("Should be return type of the json", () {
        expect(jsonUserCredentialsModel!["user"], isInstanceOf<String>());
        expect(jsonUserCredentialsModel!["password"], isInstanceOf<String>());
      });
    },
  );

  group(
    "isInstanceOf LoginDataModel model tests",
    () {
      test("Should be return instance of LoginDataModel", () {
        expect(testLoginDataModel, isInstanceOf<LoginDataModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of LoginDataModel to json", () {
      expect(
          testLoginDataModel!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of LoginDataModel from json", () {
      expect(LoginDataModel.fromJson(jsonLoginDataModel!),
          isInstanceOf<LoginDataModel>());
    });
  });

  group(
    "isInstanceOf LoginDataModel json2 to model type test",
    () {
      test("Can´t return if is null", () {
        expect(jsonLoginDataModel!["userCredentials"] == null, false);
        expect(jsonLoginDataModel!["vehicleModel"] == null, false);
      });
      test("Should be return type of the json2", () {
        expect(jsonLoginDataModel!["userCredentials"], isInstanceOf<Map>());
        expect(jsonLoginDataModel!["vehicleModel"], isInstanceOf<Map>());
      });
    },
  );

  group(
    "isInstanceOf UserModel model tests",
    () {
      test("Should be return instance of UserModel", () {
        expect(testUserModel, isInstanceOf<UserModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of UserModel to json", () {
      expect(testUserModel!.toJson(), isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of UserModel from json", () {
      expect(UserModel.fromJson(jsonUserModel!), isInstanceOf<UserModel>());
    });
  });

  group(
    "isInstanceOf LoginDataModel json3 to model2 type test",
    () {
      test("Can´t return if is null", () {
        expect(jsonUserModel!["cpf"] == null, false);
        expect(jsonUserModel!["nome"] == null, false);
        expect(jsonUserModel!["telefone"] == null, false);
        expect(jsonUserModel!["email"] == null, false);
        expect(jsonUserModel!["cnh"] == null, false);
        expect(jsonUserModel!["coren"] == null, false);
      });
      test("Should be return type of the json3", () {
        expect(jsonUserModel!["cpf"], isInstanceOf<String>());
        expect(jsonUserModel!["nome"], isInstanceOf<String>());
        expect(jsonUserModel!["telefone"], isInstanceOf<String>());
        expect(jsonUserModel!["email"], isInstanceOf<String>());
        expect(jsonUserModel!["cnh"], isInstanceOf<String>());
        expect(jsonUserModel!["coren"], isInstanceOf<String>());
      });

      test("Should validateCPF", () {
        expect(StringUtils.validateCpf(jsonUserModel!["cpf"]), true);
      });
      test("Should validateEmail", () {
        expect(StringUtils.validateEmail(jsonUserModel!["email"]), true);
      });
    },
  );
}
