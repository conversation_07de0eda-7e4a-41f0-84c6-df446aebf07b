import 'package:ambulancia_app/models/subscription_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  SubscriptionModel? testSubscriptionModel;
  Map<String, dynamic>? jsonSubscriptionModel;
  setUpAll(
    () {
      testSubscriptionModel = SubscriptionModel(
        sequencial: 1,
        numAtendimento: 1,
        nomeArquivo: "aaaa",
        tipoAnexo: 2,
        descricaoTipoAnexo: "aaaa",
      );
      jsonSubscriptionModel = {
        "sequencial": 1,
        "numAtendimento": 1,
        "nomeArquivo": "aaaa",
        "tipoAnexo": 2,
        "descricaoTipoAnexo": "aaaa",
      };
    },
  );

  group(
    "isInstanceOf SubscriptionModel model tests",
    () {
      test("Should be return instance of SubscriptionModel", () {
        expect(testSubscriptionModel, isInstanceOf<SubscriptionModel>());
      });
    },
  );

  group("Json test", () {
    test("Should be return instance of SubscriptionModel to json", () {
      expect(testSubscriptionModel!.toJson(),
          isInstanceOf<Map<dynamic, dynamic>>());
    });
    test("Should be return instance of SubscriptionModel from json", () {
      expect(SubscriptionModel.fromJson(jsonSubscriptionModel!),
          isInstanceOf<SubscriptionModel>());
    });
  });

  group(
    "isInstanceOf SubscriptionModel json to model type test",
    () {
      test("Should be return type of the json", () {
        expect(jsonSubscriptionModel!["sequencial"], isInstanceOf<int>());
        expect(jsonSubscriptionModel!["numAtendimento"], isInstanceOf<int>());
        expect(jsonSubscriptionModel!["nomeArquivo"], isInstanceOf<String>());
        expect(jsonSubscriptionModel!["tipoAnexo"], isInstanceOf<int>());
        expect(jsonSubscriptionModel!["descricaoTipoAnexo"],
            isInstanceOf<String>());
      });
    },
  );
}
