import 'package:ambulancia_app/models/service-update.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group(
    'ServiceUpdate',
    () {
      test('toMap deve retornar um mapa válido', () {
        final requestServiceDataTime = RequestServiceDataTime(
          serviceName: 'MyServiceExample',
          lastUpdate: DateTime(2023, 1, 5, 10, 30),
        );

        final map = requestServiceDataTime.toMap();

        expect(map, isA<Map<String, dynamic>>());
        expect(map['serviceName'], equals('MyServiceExample'));
        expect(map['lastUpdate'], equals('2023-01-05T10:30:00.000'));
      });

      test(
        'fromMap deve criar um objeto ServiceUpdate válido',
        () {
          final map = {
            'serviceName': 'MyServiceExample',
            'lastUpdate': '2023-01-05T10:30:00.000',
          };

          final RequestServiceDataTime requestServiceDataTime =
              RequestServiceDataTime.fromMap(map);

          expect(requestServiceDataTime, isA<RequestServiceDataTime>());
          expect(
              requestServiceDataTime.serviceName, equals('MyServiceExample'));
          expect(requestServiceDataTime.lastUpdate,
              equals(DateTime(2023, 1, 5, 10, 30)));
        },
      );
    },
  );
}
