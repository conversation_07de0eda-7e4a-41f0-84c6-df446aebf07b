import 'package:ambulancia_app/models/material_expenses_model.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  MaterialExpensesModel? testMaterialExpensesModel;
  Map<String, dynamic>? jsonMaterialExpensesModel;

  setUpAll(() {
    testMaterialExpensesModel =
        MaterialExpensesModel(id: '1', item: '123', quantity: 8);

    jsonMaterialExpensesModel = {'id': '1', 'item': '123', 'quantity': 8};
  });

  test("should be return instance of MaterialExpensesModel", () {
    expect(testMaterialExpensesModel, isInstanceOf<MaterialExpensesModel>());
  });

  test(
      "should be return instance of MaterialExpensesModel from jsonMaterialExpensesModel",
      () {
    expect(MaterialExpensesModel.fromJson(jsonMaterialExpensesModel!),
        isInstanceOf<MaterialExpensesModel>());
  });

  test("Should be return instance of MaterialExpensesModel to json", () {
    expect(testMaterialExpensesModel!.toJson(),
        isInstanceOf<Map<String, dynamic>>());
  });
}
