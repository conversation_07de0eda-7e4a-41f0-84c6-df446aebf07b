import 'package:flutter_test/flutter_test.dart';

Future<Null> initialTeamForAnimation(WidgetTester tester) async {
  await tester.pumpAndSettle(Duration(microseconds: 2000));
  await Future.delayed(Duration(milliseconds: 6000));
  await tester.pumpAndSettle(Duration(microseconds: 2000));
  await Future.delayed(Duration(milliseconds: 6000));
  await tester.pumpAndSettle(Duration(microseconds: 2000));
  await Future.delayed(Duration(milliseconds: 6000));
  await tester.pumpAndSettle(Duration(microseconds: 2000));
  await Future.delayed(Duration(milliseconds: 6000));
  await tester.pumpAndSettle(Duration(microseconds: 2000));
  await Future.delayed(Duration(milliseconds: 6000));
}
