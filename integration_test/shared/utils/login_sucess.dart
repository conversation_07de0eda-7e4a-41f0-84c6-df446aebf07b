import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'initial_team_for_animation.dart';

Future<Null> loginSucess(
    WidgetTester tester, String user, String password) async {
  await initialTeamForAnimation(tester);

  //Selecionar um veiculo
  final Finder dropdownVehicle = find.byKey(const Key('login_vehicle')).last;
  await tester.tap(dropdownVehicle);
  await tester.pumpAndSettle();

  final Finder selectDropdownItem = find.text('UTI 16 RIH 4E85').last;
  await tester.tap(selectDropdownItem);
  await tester.pumpAndSettle();

  // Digitar usuário com cpf invalido
  final Finder textFieldUsername = find.byKey(Key('login_username'));
  await tester.enterText(textFieldUsername, user);
  await tester.pumpAndSettle();

  //Digitar senha de acesso
  final Finder textFieldPassword = find.byKey(Key('login_password'));
  await tester.enterText(textFieldPassword, password);
  await tester.pumpAndSettle();

  //Clicar no botão de login
  final Finder elevatedButtonLogin = find.byKey(Key("login_button"));
  await tester.tap(elevatedButtonLogin);
  await tester.pumpAndSettle();
}
