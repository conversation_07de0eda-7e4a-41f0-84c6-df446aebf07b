import 'package:ambulancia_app/main.dart' as app;
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../../shared/text/messages/messages_information.dart';
import '../../../shared/utils/login_sucess.dart';

void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  testWidgets(
    "Exibir lista de atendimentos da ambulancia selecionada",
    (WidgetTester tester) async {
      await app.main();
      await tester.pumpAndSettle();

      await loginSucess(tester, '60866109323', '123456');

      final Finder listViewAttendance = find.byKey(Key('attendance_list'));

      if (listViewAttendance.evaluate().isNotEmpty) {
        expect(listViewAttendance, findsOneWidget);
      } else {
        final Finder messageEmptyListViewAttendance =
            find.text(textEmptyListViewAttendance);

        expect(messageEmptyListViewAttendance, findsOneWidget);
      }
    },
  );
}
