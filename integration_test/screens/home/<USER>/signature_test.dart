import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../shared/text/button/text_button.dart';

Future<Null> signatureTest(WidgetTester tester) async {
  final Finder buttonSignature = find.text(textButtonSignature).last;
  await tester.tap(buttonSignature);
  await tester.pumpAndSettle();

  await tester.drag(find.byKey(ValueKey("signature")), Offset(0, 200));
  await tester.pumpAndSettle(Duration(milliseconds: 200));

  await tester.drag(find.byKey(ValueKey("signature")), Offset(0, -200));
  await tester.pumpAndSettle(Duration(milliseconds: 200));

  await tester.drag(find.byKey(ValueKey("signature")), Offset(200, 0));
  await tester.pumpAndSettle(Duration(milliseconds: 200));

  await tester.drag(find.byKey(ValueKey("signature")), Offset(-200, 0));
  await tester.pumpAndSettle(Duration(milliseconds: 200));

  final Finder buttonSaveSignature = find.text(textButtonSave).last;
  await tester.tap(buttonSaveSignature);
  await tester.pumpAndSettle();

  final Finder buttonCloseAlertSignature = find.text(textButtonClose).last;
  await tester.tap(buttonCloseAlertSignature);
  await tester.pumpAndSettle();
}
