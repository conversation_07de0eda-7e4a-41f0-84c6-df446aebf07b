import 'package:ambulancia_app/main.dart' as app;
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-check.dart';

import '../../../../shared/text/button/text_button.dart';
import '../../../../shared/text/messages/messages_information.dart';
import '../../../../shared/utils/login_sucess.dart';
import '../../utils/take_a_picture_test.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('Preencher e enviar avaliação clínica',
      (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    // Login
    await loginSucess(tester, '60866109323', '123456');
    await tester.pumpAndSettle();

    final Finder selectItemListAttendaceAnswerButton =
        find.text('ATENDER').first;
    await tester.tap(selectItemListAttendaceAnswerButton);
    await tester.pumpAndSettle();

    // Confirmar atendimento
    final Finder buttonConfirmAtendance = find.text(textButtonConfirm);
    await tester.tap(buttonConfirmAtendance);
    await tester.pumpAndSettle();

    // Navegar pelos passos
    final Finder stepItemOne = find.byKey(const Key('item_step_0'));
    await tester.tap(stepItemOne);
    await tester.pumpAndSettle(Duration(seconds: 1));

    final Finder stepItemTwo = find.byKey(const Key('item_step_1'));
    await tester.tap(stepItemTwo);
    await tester.pumpAndSettle(Duration(seconds: 1));

    final Finder buttonConfirm = find.text('CONFIRMO');
    await tester.tap(buttonConfirm);
    await tester.pumpAndSettle(Duration(seconds: 1));

    // Navegar para a avaliação clínica
    final Finder menuItemOne = find.byKey(const Key('menu_item_1'));
    await tester.tap(menuItemOne);
    await tester.pumpAndSettle(Duration(seconds: 1));

    // Preencher sinais vitais
    await _preencherSinaisVitais(tester);

    // Preencher escala de dor
    await _preencherEscalaDor(tester);

    // Preencher avaliação clínica
    await _preencherAvaliacaoClinica(tester);

    // Salvar formulário
    await _salvarFormulario(tester);

    // Verificar sucesso
    final Finder messageSucessSaveFormClinicalEvaluation =
        find.text(textSucessSaveFormClinicalEvaluation);
    expect(messageSucessSaveFormClinicalEvaluation, findsOneWidget);
  });
}

Future<void> _preencherSinaisVitais(WidgetTester tester) async {
  // Preencher frequência cardíaca
  final Finder textInputFc = find.byKey(const Key('input_text_fc'));
  await tester.enterText(textInputFc, '80');
  await tester.pumpAndSettle();

  // Preencher frequência respiratória
  final Finder textInputFr = find.byKey(const Key('input_text_fr'));
  await tester.enterText(textInputFr, '18');
  await tester.pumpAndSettle();

  // Preencher pressão arterial
  final Finder textInputPa = find.byKey(const Key('input_text_pa'));
  await tester.enterText(textInputPa, '120/80');
  await tester.pumpAndSettle();

  // Preencher saturação
  final Finder textInputSpo2 = find.byKey(const Key('input_text_spo2'));
  await tester.enterText(textInputSpo2, '98');
  await tester.pumpAndSettle();

  // Selecionar ritmo
  final Finder selectRhythms = find.byKey(const Key('rhythms_Regular'));
  await tester.tap(selectRhythms);
  await tester.pumpAndSettle();
}

Future<void> _preencherAvaliacaoClinica(WidgetTester tester) async {
  // Preencher sistema cardiovascular
  await _preencherSistemaCardiovascular(tester);

  // Scroll para baixo para acessar mais campos
  await _scrollDown(tester);

  // Preencher sistema respiratório
  await _preencherSistemaRespiratorio(tester);

  // Scroll para baixo para acessar mais campos
  await _scrollDown(tester);

  // Preencher função renal
  await _preencherFuncaoRenal(tester);

  // Scroll para baixo para acessar mais campos
  await _scrollDown(tester);

  // Preencher sistema nervoso central
  await _preencherSistemaNervosoCentral(tester);

  // Scroll para baixo para acessar mais campos
  await _scrollDown(tester);

  // Preencher sistema gastrointestinal
  await _preencherSistemaGastrointestinal(tester);

  // Scroll para baixo para acessar mais campos
  await _scrollDown(tester);

  // Preencher pele
  await _preencherPele(tester);

  // Scroll para baixo para acessar mais campos
  await _scrollDown(tester);

  // Preencher acesso venoso
  await _preencherAcessoVenoso(tester);

  // Scroll para baixo para acessar mais campos
  await _scrollDown(tester);

  // Preencher informações cirúrgicas
  await _preencherInformacoesCirurgicas(tester);
}

Future<void> _scrollDown(WidgetTester tester) async {
  final listFinder = find.byKey(const Key('list_cards_content'));
  await tester.drag(listFinder, const Offset(0.0, -300));
  await tester.pumpAndSettle();
}

Future<void> _preencherSistemaCardiovascular(WidgetTester tester) async {
  // Encontrar e selecionar opções no sistema cardiovascular
  final Finder cardiovascularOption = find.byType(ClinicalInputCheck).first;
  if (cardiovascularOption.evaluate().isNotEmpty) {
    await tester.tap(cardiovascularOption);
    await tester.pumpAndSettle();
  } else {
    print('Opção cardiovascular não encontrada');
  }
}

Future<void> _preencherSistemaRespiratorio(WidgetTester tester) async {
  // Tentar encontrar opções do sistema respiratório
  final Finder respiratoryOptions = find.byType(ClinicalInputCheck);
  if (respiratoryOptions.evaluate().length > 5) {
    await tester.tap(respiratoryOptions.at(5));
    await tester.pumpAndSettle();
  } else {
    print('Opção respiratória não encontrada');
  }
}

Future<void> _preencherFuncaoRenal(WidgetTester tester) async {
  // Tentar encontrar opções de função renal
  final Finder renalOptions = find.byType(ClinicalInputCheck);
  if (renalOptions.evaluate().length > 10) {
    await tester.tap(renalOptions.at(10));
    await tester.pumpAndSettle();
  } else {
    print('Opção de função renal não encontrada');
  }
}

Future<void> _preencherSistemaNervosoCentral(WidgetTester tester) async {
  // Tentar encontrar opções do sistema nervoso central
  final Finder nervousSystemOptions = find.byType(ClinicalInputCheck);
  if (nervousSystemOptions.evaluate().length > 15) {
    await tester.tap(nervousSystemOptions.at(15));
    await tester.pumpAndSettle();
  } else {
    print('Opção do sistema nervoso central não encontrada');
  }
}

Future<void> _preencherSistemaGastrointestinal(WidgetTester tester) async {
  // Tentar encontrar opções do sistema gastrointestinal
  final Finder gastrointestinalOptions = find.byType(ClinicalInputCheck);
  if (gastrointestinalOptions.evaluate().length > 20) {
    await tester.tap(gastrointestinalOptions.at(20));
    await tester.pumpAndSettle();
  } else {
    print('Opção do sistema gastrointestinal não encontrada');
  }
}

Future<void> _preencherPele(WidgetTester tester) async {
  // Tentar encontrar opções de pele
  final Finder skinOptions = find.byType(ClinicalInputCheck);
  if (skinOptions.evaluate().length > 25) {
    await tester.tap(skinOptions.at(25));
    await tester.pumpAndSettle();
  } else {
    print('Opção de pele não encontrada');
  }
}

Future<void> _preencherAcessoVenoso(WidgetTester tester) async {
  // Tentar encontrar opções de acesso venoso
  final Finder venousAccessOptions = find.byType(ClinicalInputCheck);
  if (venousAccessOptions.evaluate().length > 30) {
    await tester.tap(venousAccessOptions.at(30));
    await tester.pumpAndSettle();
  } else {
    print('Opção de acesso venoso não encontrada');
  }
}

Future<void> _preencherInformacoesCirurgicas(WidgetTester tester) async {
  // Tentar encontrar opções de informações cirúrgicas
  final Finder surgicalOptions = find.byType(ClinicalInputCheck);
  if (surgicalOptions.evaluate().length > 35) {
    await tester.tap(surgicalOptions.at(35));
    await tester.pumpAndSettle();
  } else {
    print('Opção de informações cirúrgicas não encontrada');
  }
}

Future<void> _salvarFormulario(WidgetTester tester) async {
  // Scroll até o final para encontrar o botão de salvar
  await _scrollDown(tester);

  // Encontrar e clicar no botão de salvar
  final Finder saveButton = find.text(textButtonSave);
  if (saveButton.evaluate().isNotEmpty) {
    await tester.tap(saveButton);
    await tester.pumpAndSettle();

    // Tirar foto (se necessário)
    await takeAPictureTest(tester);
    await tester.pumpAndSettle();
  } else {
    print('Botão de salvar não encontrado');

    // Tentar encontrar o botão por outro método
    final Finder alternativeSaveButton =
        find.byKey(Key('botao_salvar_avaliacao_clinica'));
    if (alternativeSaveButton.evaluate().isNotEmpty) {
      await tester.tap(alternativeSaveButton);
      await tester.pumpAndSettle();

      // Tirar foto (se necessário)
      await takeAPictureTest(tester);
      await tester.pumpAndSettle();
    } else {
      print('Nenhum botão de salvar encontrado');
    }
  }
}

// Nova função para preencher a escala de dor
Future<void> _preencherEscalaDor(WidgetTester tester) async {
  // Selecionar nível de dor (usando a opção "DOR MODERADA" - valor 5)
  final Finder painOption = find.text('DOR MODERADA');
  if (painOption.evaluate().isNotEmpty) {
    await tester.tap(painOption);
    await tester.pumpAndSettle();
  } else {
    // Alternativa: tentar encontrar pelo radio button
    final List<Finder> radioButtons = List.generate(
        5,
        (index) => find.byWidgetPredicate(
            (widget) => widget is Radio && (widget.value as int?) == 5));

    for (var radioButton in radioButtons) {
      if (radioButton.evaluate().isNotEmpty) {
        await tester.tap(radioButton);
        await tester.pumpAndSettle();
        break;
      }
    }
  }

  // Selecionar localizações da dor
  final List<String> locaisDor = ['CABEÇA', 'TÓRAX OU PEITO'];

  for (var local in locaisDor) {
    final Finder painLocation = find.text(local);
    if (painLocation.evaluate().isNotEmpty) {
      await tester.tap(painLocation);
      await tester.pumpAndSettle();
    }
  }

  // Selecionar "OUTROS" e preencher o campo de texto
  final Finder outrosOption = find.text('OUTROS');
  if (outrosOption.evaluate().isNotEmpty) {
    await tester.tap(outrosOption);
    await tester.pumpAndSettle();

    // Preencher o campo de texto para outros locais de dor
    final Finder otherPainLocationField = find.byType(TextField).last;
    await tester.enterText(otherPainLocationField, 'Dor no ombro direito');
    await tester.pumpAndSettle();
  }
}
