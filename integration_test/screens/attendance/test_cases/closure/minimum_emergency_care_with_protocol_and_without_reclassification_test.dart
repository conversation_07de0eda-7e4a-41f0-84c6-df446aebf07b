import 'package:ambulancia_app/main.dart' as app;
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../../../shared/text/button/text_button.dart';
import '../../../../shared/utils/login_sucess.dart';
import '../../../home/<USER>/take_a_picture_test.dart';
import 'const.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets(
      "Atendimento de Mínima urgência com protocolo e sem reclassificação",
      (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await loginSucess(tester, '62411482302', '123456');

    final Finder selectItemListAttendaceAnswerButton =
        find.byIcon(Icons.power_settings_new_rounded).first;
    await tester.tap(selectItemListAttendaceAnswerButton);
    await tester.pumpAndSettle();

    final Finder buttonConfirmAtendance = find.text(textButtonConfirm);
    await tester.tap(buttonConfirmAtendance);
    await tester.pumpAndSettle();

    final Finder stepItem = find.byKey(Key('item_step_0'));
    await tester.tap(stepItem);
    await tester.pumpAndSettle(Duration(seconds: 4));

    for (int i = 1; i <= 5; i++) {
      final Finder stepItem = find.byKey(Key('item_step_$i'));
      await tester.tap(stepItem);
      await tester.pumpAndSettle(Duration(seconds: 4));

      final Finder buttonConfirm = find.text('${textButtonIConfirm}').last;
      await tester.tap(buttonConfirm);
      await tester.pumpAndSettle(Duration(seconds: 4));
    }

    await tester.pumpAndSettle(Duration(seconds: 4));
    final Finder menuitemTwo = find.byKey(const Key('menu_item_2'));
    await tester.tap(menuitemTwo);
    await tester.pumpAndSettle(Duration(seconds: 4));

    final Finder textFieldCid = find.byKey(const Key('text_field_cid'));
    await tester.enterText(textFieldCid, 'a');
    await tester.pumpAndSettle(Duration(seconds: 9));

    final itemFinder = find.byKey(ValueKey("cids_0"));
    await tester.tap(itemFinder);
    await tester.pumpAndSettle(Duration(seconds: 3));

    final Finder textFormFiledDescription = find.byKey(ValueKey("description"));
    await tester.enterText(textFormFiledDescription, 'Teste descrição');
    await tester.pumpAndSettle(Duration(seconds: 3));

    final Finder textFormFiledObservation = find.byKey(ValueKey("observation"));
    await tester.enterText(textFormFiledObservation, 'Teste observação');
    await tester.pumpAndSettle(Duration(seconds: 3));

    final Finder buttonSaveConduct = find.text(textButtonSave);
    await tester.tap(buttonSaveConduct);
    await tester.pumpAndSettle();

    await takeAPictureTest(tester);

    final Finder messageAlertConductSaveForm =
        find.text(messageConductSavedSuccess);
    expect(messageAlertConductSaveForm, findsOneWidget);

    final Finder buttonCloseAlert = find.text(textButtonClose).last;
    await tester.tap(buttonCloseAlert);
    await tester.pumpAndSettle(Duration(seconds: 3));

    await tester.tap(menuitemTwo);
    await tester.pumpAndSettle(Duration(seconds: 4));

    final Finder buttonEndService = find.byKey(const Key('button_end_service'));
    await tester.tap(buttonEndService);
    await tester.pumpAndSettle();

    await tester.pumpAndSettle(Duration(seconds: 4));
    final Finder unimedSelectInkWell = find.byKey(Key('unimed-select'));
    await tester.pumpAndSettle(Duration(seconds: 5));
    await tester.tap(unimedSelectInkWell);
    await tester.pumpAndSettle(Duration(seconds: 2));

    final Finder firstOption = find.text("1 - ECG COM SUPRA DE ST");
    await tester.tap(firstOption);
    await tester.pumpAndSettle();

    final Finder continueClosureButton = find.text("Continuar encerramento");
    await tester.tap(continueClosureButton);
    await tester.pumpAndSettle();

    final Finder buttonCloseReclassificationtAttendanceDefault =
        find.text(close);
    await tester.tap(buttonCloseReclassificationtAttendanceDefault);
    await tester.pumpAndSettle();

    await takeAPictureTest(tester);

    final Finder messageAlertEndService = find.text(messageServiceEndedSuccess);
    expect(messageAlertEndService, findsOneWidget);
  });
}
