import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:ambulancia_app/main.dart' as app;

import '../../../shared/text/button/text_button.dart';
import '../../../shared/utils/initial_team_for_animation.dart';

void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets("Realizar logout com sucesso", (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await initialTeamForAnimation(tester);

    String selectVehicle = "UTI 15 POQ1G22";

    //Selecionar um veiculo
    final Finder dropdownVehicle = find.byKey(const Key('login_vehicle')).last;
    await tester.tap(dropdownVehicle);
    await tester.pumpAndSettle();

    final Finder selectDropdownItem = find.text(selectVehicle).last;
    await tester.tap(selectDropdownItem);
    await tester.pumpAndSettle();

    // Digitar usuário com cpf invalido
    final Finder textFieldUsername = find.byKey(const Key('login_username'));
    await tester.enterText(textFieldUsername, '60866109323');
    await tester.pumpAndSettle();

    //Digitar senha de acesso
    final Finder textFieldPassword = find.byKey(const Key('login_password'));
    await tester.enterText(textFieldPassword, '123456');
    await tester.pumpAndSettle();

    //Clicar no botão de login
    final Finder elevatedButtonLogin = find.byKey(Key("login_button"));
    await tester.tap(elevatedButtonLogin);
    await tester.pumpAndSettle();

    // Widget de texto do veiculo selecionado
    final Finder textVehicleSelected = find.text("Veículo $selectVehicle");

    expect(textVehicleSelected, findsOneWidget);

    //Clicar no botão de logout
    final Finder stackButtonLogout = find.byKey(Key('home_button_logout'));
    await tester.tap(stackButtonLogout);
    await tester.pumpAndSettle();

    //Escolher opção de sair da lista de opções
    final Finder selectDropdownItemLogout = find.text(textButtonLogout);
    await tester.tap(selectDropdownItemLogout);
    await tester.pumpAndSettle();

    //Escolher opção de sair da lista de opções
    final Finder elevatedButtonConfirmLogout = find.text(textButtonLogout);
    await tester.tap(elevatedButtonConfirmLogout);
    await tester.pumpAndSettle();

    final Finder pageLoginKey = find.byKey(Key("login_page"));

    expect(pageLoginKey, findsOneWidget);
  });
}
