<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="br.com.unimedfortaleza.ambulancia.ambulancia_app">
         <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
         <uses-permission android:name="android.permission.INTERNET" />
         <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
         <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
         <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
         <uses-permission android:name="android.permission.CAMERA" />
         <uses-permission android:name="android.permission.WAKE_LOCK" />
         <uses-permission android:name="android.permission.VIBRATE"/>
         <queries>
          <intent>
              <action android:name="android.intent.action.VIEW" />
              <category android:name="android.intent.category.BROWSABLE" />
              <data android:scheme="https" />
          </intent>
         </queries>  

    <application
        android:name="${applicationName}"
        android:label="Unimed Urgente"
        android:icon="@mipmap/ic_launcher" 
        android:requestLegacyExternalStorage="true">
        
        <meta-data android:name="com.google.android.geo.API_KEY"
               android:value="AIzaSyDfjW_0iMC2qAuGizO8WSzzgHiLUq9nQEk"/>
        
        <activity
            android:name=".MainActivity"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">

            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
       
            <meta-data
              android:name="io.flutter.embedding.android.SplashScreenDrawable"
              android:resource="@drawable/launch_background"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
          
              <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <data android:scheme="https" />
              </intent-filter>
          
        </activity>
      
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
    </application>
</manifest>
