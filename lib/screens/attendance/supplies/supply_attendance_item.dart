import 'package:ambulancia_app/bloc/supply/supply_cubit.dart';
import 'package:ambulancia_app/bloc/supply/supply_state.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/supply_model.dart';
import 'package:ambulancia_app/screens/attendance/supplies/circular_button.dart';
import 'package:ambulancia_app/shared/formatters.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class SupplyAttendanceItem extends StatefulWidget {
  final SupplyAttendance supply;
  final AttendanceModel? attendance;

  const SupplyAttendanceItem({Key? key, required this.supply, this.attendance})
      : super(key: key);

  @override
  _SupplyAttendanceItemState createState() => _SupplyAttendanceItemState();
}

class _SupplyAttendanceItemState extends State<SupplyAttendanceItem> {
  TextEditingController quantityController = TextEditingController();
  final FocusNode _quantityFocus = FocusNode();

  @override
  void dispose() {
    quantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 10.0),
      child: Container(
        decoration: BoxDecoration(
          color: AmbulanceColors.greenLight2,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(ConstantsTheme.padding),
            topRight: Radius.circular(ConstantsTheme.padding),
            bottomLeft: Radius.circular(ConstantsTheme.padding),
          ),
        ),
        padding: EdgeInsets.all(10),
        child: Row(
          children: [
            Expanded(
              flex: 5,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Tipo',
                      style: TextStyle(
                          fontSize: 16, color: AmbulanceColors.greenDark)),
                  SizedBox(height: ConstantsTheme.padding),
                  Container(
                    padding: EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius:
                          BorderRadius.circular(ConstantsTheme.padding),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text('${widget.supply.descricao}'),
                        ),
                      ],
                    ),
                  )
                ],
              ),
            ),
            SizedBox(width: ConstantsTheme.doublePadding),
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Quantidade',
                    style: TextStyle(
                        fontSize: 16, color: AmbulanceColors.greenDark),
                  ),
                  SizedBox(height: ConstantsTheme.padding),
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius:
                          BorderRadius.circular(ConstantsTheme.padding),
                    ),
                    child: _inputQuantity(),
                  )
                ],
              ),
            ),
            SizedBox(width: ConstantsTheme.doublePadding),
            Column(
              children: [
                SizedBox(height: ConstantsTheme.padding * 2),
                _btnRemove(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _inputQuantity() {
    return Container(
      padding: const EdgeInsets.all(10),
      width: 85,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ConstantsTheme.padding),
      ),
      child: BlocBuilder<SupplyCubit, SupplyState>(builder: (context, state) {
        if (state is UpdatingSupplyState) {
          if (state.supplyCode == widget.supply.codigoMaterial)
            return FittedBox(
                fit: BoxFit.contain,
                child: SpinKitThreeBounce(color: AmbulanceColors.green));
          else
            return _buildTextInput();
        } else
          return _buildTextInput();
      }),
    );
  }

  _buildTextInput() {
    final qtd = widget.supply.quantidade;
    return InkWell(
      child: Text('$qtd'),
      onTap: () {
        showDialog(
            context: context,
            builder: (context) {
              FocusScope.of(context).requestFocus(_quantityFocus);
              return AlertDialog(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Align(
                      alignment: Alignment.topLeft,
                      child: Text(
                        'Ex:1',
                        style: TextStyle(
                            fontSize: 20, fontWeight: FontWeight.bold),
                      ),
                    ),
                    SizedBox(height: 20),
                    TextFormField(
                      focusNode: _quantityFocus,
                      controller: quantityController,
                      inputFormatters:
                          FormatterField.inputNumberSuppliesFormatter,
                      keyboardType: TextInputType.number,
                      autovalidateMode: AutovalidateMode.always,
                      onFieldSubmitted: (value) => _onSave(value),
                      decoration: InputDecoration(
                        hintText: '$qtd',
                      ),
                    ),
                    SizedBox(height: 20),
                    ElevatedButton(
                      child: Text(I18nHelper.translate(context, 'common.save')),
                      onPressed: () => _onSave(quantityController.text),
                    )
                  ],
                ),
              );
            });
      },
    );
  }

  _onSave(String value) {
    if (int.tryParse(value)! <= 0) return;
    Navigator.pop(context);
    context.read<SupplyCubit>().updateSupply(
          attendanceNumber: widget.attendance!.numAtendimento,
          quantity: int.parse(value),
          supply: widget.supply,
        );
  }

  Widget _btnRemove() {
    return BlocBuilder<SupplyCubit, SupplyState>(
      builder: (context, state) {
        if (state is RemovingSupplyState) {
          if (state.supplyCode == widget.supply.codigoMaterial)
            return FittedBox(
                fit: BoxFit.cover,
                child: SpinKitThreeBounce(color: Colors.green));
          else
            return CircularButton(
                color: Colors.red, icon: Icons.delete, onPressed: () => {});
        } else
          return CircularButton(
            color: Colors.red,
            icon: Icons.delete,
            onPressed: () => context.read<SupplyCubit>().removeSupply(
                  attendanceNumber: widget.attendance!.numAtendimento,
                  supplyCode: widget.supply.codigoMaterial,
                ),
          );
      },
    );
  }
}
