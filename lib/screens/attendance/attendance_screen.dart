import 'dart:async';

import 'package:ambulancia_app/bloc/attendance/attendance_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/closse_attendance_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/subscription/cubit/subscription_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_list/attendance_list_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_team/attendance_team_cubit.dart';
import 'package:ambulancia_app/bloc/auth/auth_cubit.dart';
import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/attendance_body.dart';
import 'package:ambulancia_app/screens/attendance/attendance_header/attendance_header.dart';
import 'package:ambulancia_app/shared/api/websocket.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/services/geolocation.service.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/alert_localization_permission/alert_localization_permission.dart';
import 'package:ambulancia_app/shared/widgets/background_unfocus/background_unfocus.dart';
import 'package:ambulancia_app/shared/widgets/header/header.dart';
import 'package:ambulancia_app/shared/widgets/snack.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../../bloc/conduct/conduct_cubit.dart';
import '../../bloc/permissions_force/permission_cubit.dart';
import '../../bloc/permissions_force/permission_cubit_state.dart';
import '../permissions_screen/checkpermission_screen.dart';

class AttendanceScreen extends StatefulWidget {
  final AttendanceModel attendance;

  AttendanceScreen({Key? key, required this.attendance}) : super(key: key);
  @override
  _AttendanceScreenState createState() => _AttendanceScreenState();
}

class _AttendanceScreenState extends State<AttendanceScreen> {
  final UnimedLogger logger = UnimedLogger(className: 'AttendanceScreen');
  final webSocketApi = Locator.instance.get<WebSocketApi>();

  @override
  void initState() {
    WakelockPlus.enable();

    startGpsMonitoring();
    context
        .read<AttendanceCubit>()
        .initAttendance(numAtendimento: widget.attendance.numAtendimento);
    context
        .read<ClinicEvaluationCubit>()
        .loadClinicEvaluation('${widget.attendance.numAtendimento}');
    context.read<SubscriptionCubit>().verifySubscription(
        widget.attendance.numAtendimento, widget.attendance.codStatus);
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _requestAllPermissions().then((permissionsGranted) {
        _initSocketGeolocation();
      });
    });
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    stopGpsMonitoring();
    debugPrint('=========TO NO DISPOSE======');
    super.dispose();
  }

  void _refreshListAttendance() {
    final AttendanceModel currentAttendance =
        context.read<AttendanceCubit>().currentAttendance();
    context
        .read<AttendanceListCubit>()
        .updateAttendanceLocalList(attendance: currentAttendance);
  }

  Timer? _gpsCheckTimer;
  int _secondsCounter = 0;

  void startGpsMonitoring() {
    print('Iniciado timer Gps monitoring');
    _gpsCheckTimer = Timer.periodic(Duration(seconds: 50), (timer) async {
      _secondsCounter += 50;
      final locationStatus = await Permission.location.status;

      if (locationStatus != PermissionStatus.denied ||
          locationStatus != PermissionStatus.permanentlyDenied) {
        bool resultGps = await context.read<PermissionCubit>().isGpsEnabled();
        print('gps monitor ($_secondsCounter s)');
        if (!resultGps) {
          context.read<PermissionCubit>().gpsDeativated();
        }
      }
    });
  }

  stopGpsMonitoring() {
    _gpsCheckTimer?.cancel();
    print('stop  GpsMonitoring');
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) => _refreshListAttendance(),
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        resizeToAvoidBottomInset: false,
        appBar: AmbulanceHeader(
          arrowBack: true,
        ),
        body: MultiBlocListener(
          listeners: [
            BlocListener<CloseAttendanceCubit, CloseAttendanceState>(
              listener: (context, state) {
                if (state is CloseAttendanceSuccesState) {
                  _refreshListAttendance();
                }
              },
            ),
            BlocListener<PermissionCubit, PermissionCubitState>(
              listener: (context, state) {
                if (state is PermissionAppDeniedState) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => CheckPermissionsPage()),
                  );
                }
              },
            ),
            BlocListener<ClinicEvaluationCubit, ClinicEvaluationState>(
                listener: (context, state) {
              if (state is ErrorSendClinicEvaluationState) {
                ScaffoldMessenger.of(context).showSnackBar(
                  Snack.success(
                    state.message,
                    duration: Duration(seconds: 1),
                  ),
                );
              }
            }),
            BlocListener<ConductCubit, ConductState>(
              listener: (context, state) {
                if (state is SuccessSendConductState) {
                  context.read<AttendanceCubit>().initAttendance(
                      numAtendimento: widget.attendance.numAtendimento);
                }
              },
            ),
          ],
          child: _buildAttendanceScreen(context),
        ),
      ),
    );
  }

  Widget _buildAttendanceScreen(context) {
    return BlocConsumer<AttendanceCubit, AttendanceState>(
      buildWhen: (previous, current) {
        if (current is LoadErrorAttendanceState) {
          showDialog(
            barrierDismissible: false,
            context: context,
            builder: (context) => PopScope(
              canPop: false,
              child: AmbulanciaAlertDialog(
                textWidget: Text(current.message),
                onPressed: () =>
                    Navigator.popUntil(context, (route) => route.isFirst),
              ),
            ),
          );
        }
        return true;
      },
      listener: (context, state) {
        if (state is LoadedAttendanceState) {
          if (AttendanceStatus.statusApi2StatusApp(
                      state.attendance.codStatus) ==
                  -1 &&
              state.attendance.codStatus != AttendanceStatus.DESPACHO) {
            return Alert.open(
              context,
              text:
                  "O status do atendimento está inválido.\n N° do status: ${state.attendance.codStatus}\n Descrição: ${state.attendance.nomeStatus}",
              callbackClose: () {
                Navigator.of(context).pop();
              },
            );
          }
        }
      },
      builder: (context, state) {
        if (state is LoadedAttendanceState) {
          return BackgroundUnfocus(
            child: Container(
              margin: EdgeInsets.all(ConstantsTheme.padding),
              child: RefreshIndicator(
                key: widget.key,
                onRefresh: () async {
                  context.read<AttendanceCubit>().initAttendance(
                      numAtendimento: widget.attendance.numAtendimento);
                  context.read<AttendanceTeamCubit>().getAttendanceTeam(
                      codUnimed: widget.attendance.codUnimed);
                },
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(vertical: 15),
                  child: Column(
                    children: [
                      AttendanceHeader(attendance: state.attendance),
                      SizedBox(height: ConstantsTheme.padding),
                      AttendanceBody(
                        attendance: state.attendance,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        } else if (state is LoadingAttendanceState) {
          return SpinKitCircle(
            color: AmbulanceColors.green,
          );
        }
        return Container();
      },
    );
  }

  _initSocketGeolocation() {
    Locator.instance.get<GeolocationService>().requestPermission().then((_) {
      final credentials = BlocProvider.of<AuthCubit>(context).credentials;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        webSocketApi.connectWebsocket(
            userCredentials: credentials!,
            ack: (geolocationModel) async {
              logger.d(
                  '_initSocketGeolocation - geolocationModel: ${geolocationModel.toString()}');

              await Locator.instance
                  .get<GeolocationService>()
                  .listenPositionStream(
                    context: context,
                    geolocationConfigModel: geolocationModel!,
                    attendance: widget.attendance,
                  );
            });
      });
    }).whenComplete(
      () async {
        PermissionStatus permission = await Permission.location.status;
        if (permission == PermissionStatus.denied) {
          AlertLocalizationPermission.grantPermission(context: context);
        }
      },
    );
  }

  Future<bool> _requestAllPermissions() async {
    bool storageGranted = await _requestStoragePermission();

    logger.d('requestAllPermissions storageGranted: $storageGranted');
    return storageGranted;
  }

  Future<bool> _requestStoragePermission() async {
    var storageStatus = await Permission.storage.status;
    if (storageStatus.isGranted) {
      return true;
    }

    var result = await Permission.storage.request();
    return result.isGranted;
  }
}
