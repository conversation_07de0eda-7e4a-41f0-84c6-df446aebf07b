import 'dart:async';

import 'package:ambulancia_app/bloc/attendance/attendance_route/attendance_route_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_address/attendance_address_cubit.dart';
import 'package:ambulancia_app/models/address-infos.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
// import 'package:ambulancia_app/shared/utils/maps.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
// import 'package:flutter/foundation.dart';
// import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:map_launcher/map_launcher.dart';
import 'package:url_launcher/url_launcher.dart';

const TYPE_ADDRESS_ATTENDANCE = 'ATTENDANCE';

// TODO: Perform a refactoring

class AttendanceAdressRoute extends StatefulWidget {
  final AttendanceModel? attendance;
  final int? codStatusCurrent;
  const AttendanceAdressRoute(
      {Key? key, required this.attendance, this.codStatusCurrent})
      : super(key: key);
  @override
  _AttendanceAdressRouteState createState() => _AttendanceAdressRouteState();
}

class _AttendanceAdressRouteState extends State<AttendanceAdressRoute>
    with WidgetsBindingObserver {
  Completer<GoogleMapController> _controllerComplete = Completer();
  late GoogleMapController _controller;

  final Set<Polyline> _polylines = Set<Polyline>();
  final _baseTransalate = 'addressRoute';

  Map<MarkerId, Marker> _markers = <MarkerId, Marker>{};

  BitmapDescriptor? _pinAmbulance;
  BitmapDescriptor? _pinPerson;
  BitmapDescriptor? _pinHospital;

  late LatLng attendanceCoordenates;
  late LatLng destinyCoordenates;
  Position? currentPosition;

  bool loading = false;
  AddressInfos? addressLocation;

  @override
  void initState() {
    // MapsUtil.getBitmapDescriptor(ICONS.AMBULANCE)
    //     .then((value) => _pinAmbulance = value);

    // MapsUtil.getBitmapDescriptor(ICONS.PERSON)
    //     .then((value) => _pinPerson = value);

    // MapsUtil.getBitmapDescriptor(ICONS.HOSPITAL)
    //     .then((value) => _pinHospital = value);

    super.initState();
    WidgetsBinding.instance.addObserver(this);
    context.read<AttendanceAddressCubit>().getAddresses(widget.attendance!);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AttendanceAddressCubit, AttendanceAddressState>(
        buildWhen: (previousState, state) {
      if (state is AdressErrorState) {
        _alertError(state.message,
            callbackClose: () => Navigator.of(context).pop());
      }

      if (state is LoadedAddressState) {
        if (verifyDestiny()) {
          addressLocation =
              context.read<AttendanceAddressCubit>().addressesDestiny;
        } else {
          addressLocation =
              context.read<AttendanceAddressCubit>().addressesAtendance;
        }
        // if (addressLocation != null)
        //   context.read<AttendanceRouteCubit>().searchAttendaceRoute(
        //       LatLng(addressLocation!.lat, addressLocation!.lng));
      }
      return true;
    }, builder: (context, state) {
      if (state is SendingAddressState || state is LoadingAddressState) {
        return Center(
            child: SpinKitCircle(
          color: AmbulanceColors.green,
        ));
      } else if (state is LoadedAddressState) {
        return ListView(
          shrinkWrap: true,
          children: [
            Text(
              "Informações da rota",
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 22,
                  color: AmbulanceColors.grayDark2),
            ),
            // _divider(),
            //TODO: Map temporarily removed due to rendering error
            // _map(),
            // _divider(),
            _infos(),
          ],
        );
      } else {
        return Container();
      }
    });
  }

  Widget _map() {
    return BlocBuilder<AttendanceRouteCubit, AttendanceRouteState>(
      builder: (context, state) {
        if (state is LoadedAttendanceRouteState) {
          final points =
              BlocProvider.of<AttendanceRouteCubit>(context).routeCoords!;
          _polylines.add(
            Polyline(
              color: Colors.blue,
              polylineId: PolylineId('route1'),
              visible: true,
              points: points,
              width: 4,
              startCap: Cap.roundCap,
              endCap: Cap.buttCap,
            ),
          );
        }

        return Container(
          height: 500,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10.0),
            child: GoogleMap(
              initialCameraPosition: CameraPosition(
                target: LatLng(-3.7305240631103516, -38.5148155247689),
                zoom: 13.0,
              ),
              polylines: _polylines,
              markers: Set<Marker>.of(_markers.values),
              gestureRecognizers: Set()
                ..add(
                    Factory<PanGestureRecognizer>(() => PanGestureRecognizer()))
                ..add(Factory<ScaleGestureRecognizer>(
                    () => ScaleGestureRecognizer()))
                ..add(
                    Factory<TapGestureRecognizer>(() => TapGestureRecognizer()))
                ..add(Factory<VerticalDragGestureRecognizer>(
                    () => VerticalDragGestureRecognizer())),
              onMapCreated: _onMapCreated,
            ),
          ),
        );
      },
    );
  }

  Widget _infos() {
    return BlocConsumer<AttendanceRouteCubit, AttendanceRouteState>(
        listener: (context, state) {
      if (state is LoadedAttendanceRouteState) {
        _addMarkers(context.read<AttendanceAddressCubit>().addresses,
            state.currentPosition!);

        currentPosition = state.currentPosition;
        loading = false;
      } else if (state is LoadingAttendanceRouteState) {
        loading = true;
      }
    }, builder: (context, state) {
      if (state is LoadingAttendanceRouteState) {
        loading = true;
        return Column(
          children: [
            Text(I18nHelper.translate(context, '$_baseTransalate.loadRoute')),
            SpinKitThreeBounce(
              color: AmbulanceColors.green,
            )
          ],
        );
      } else if (state is ErrorAttendanceRouteState) {
        return Text(
            I18nHelper.translate(context, '$_baseTransalate.failedRoute'));
      } else if (state is LoadedAttendanceRouteState) {
        return Column(
          children: [
            _routeInfo(state.distance!, state.duration!, state.prevision),
            _divider(),
            _options(context)
          ],
        );
      } else
        return Container();
    });
  }

  Widget _routeInfo(double distance, double duration, String? prevision) {
    return Container(
        decoration: BoxDecoration(
          color: AmbulanceColors.greenLight,
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: Column(
          children: [
            SizedBox(height: ConstantsTheme.padding),
            Container(
                decoration: BoxDecoration(
                  color: AmbulanceColors.purple,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(ConstantsTheme.padding),
                    topRight: Radius.circular(ConstantsTheme.padding),
                    bottomLeft: Radius.circular(ConstantsTheme.padding),
                  ),
                ),
                child: Padding(
                  padding:
                      EdgeInsets.only(top: 10, left: 32, right: 32, bottom: 10),
                  child: Column(
                    children: [
                      Text(
                        '$prevision',
                        style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 28),
                      ),
                      Text(
                        I18nHelper.translate(
                            context, '$_baseTransalate.expectedArrival'),
                        style: TextStyle(
                            color: AmbulanceColors.orange, fontSize: 12),
                      )
                    ],
                  ),
                )),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '${duration.toStringAsFixed(0)} min',
                  style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                ),
                Column(
                  children: [
                    Container(
                      height: 20,
                      child: Image.asset('assets/images/ambulance_outline.png'),
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 10),
                      child: Container(
                        width: 200,
                        decoration: BoxDecoration(
                            border: Border(
                          top: BorderSide(
                            color: AmbulanceColors.grayDark,
                            width: 1.0,
                          ),
                        )),
                      ),
                    )
                  ],
                ),
                Text(
                  '${distance.toStringAsFixed(1)} km',
                  style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            SizedBox(height: ConstantsTheme.padding),
          ],
        ));
  }

  Widget _divider() {
    return SizedBox(height: ConstantsTheme.doublePadding);
  }

  Widget _options(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.0),
              border: Border.all(
                color: AmbulanceColors.redClose,
              ),
            ),
            child: TextButton(
              style: TextButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: AmbulanceColors.redClose,
                padding: EdgeInsets.symmetric(vertical: 16.0),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 50),
                child: Text(
                  I18nHelper.translate(context, '$_baseTransalate.closed'),
                  style: TextStyle(
                    fontSize: 20,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ),
        Container(
          width: ConstantsTheme.doublePadding,
        ),
        Expanded(
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AmbulanceColors.greenLight3,
              padding: EdgeInsets.symmetric(vertical: 16.0),
            ),
            onPressed: _showPointInMap,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 50),
              child: Text(
                I18nHelper.translate(context, '$_baseTransalate.open'),
                style: TextStyle(fontSize: 20),
              ),
            ),
          ),
        )
      ],
    );
  }

  void _onMapCreated(GoogleMapController controller) {
    debugPrint('tracking ambulance onMapCreated');
    setState(() => _controller = controller);

    _controllerComplete.complete(controller);
  }

  Future<void> _addMarkers(
      List<AddressInfos> address, Position currentPosition) async {
    debugPrint('tracking ambulance _createMarkers');
    List<LatLng> points = List<LatLng>.empty(growable: true);
    address.forEach((point) {
      if (point.address!.isAttendance) {
        attendanceCoordenates = LatLng(point.lat, point.lng);
      } else if (point.address!.isDestiny && verifyDestiny()) {
        destinyCoordenates = LatLng(point.lat, point.lng);
      }

      final markerId = MarkerId(point.address!.tipoEndereco!);
      _markers[markerId] = Marker(
        markerId: markerId,
        position: LatLng(point.lat, point.lng),
        icon: point.address!.isAttendance ? _pinPerson! : _pinHospital!,
      );
      points.add(LatLng(point.lat, point.lng));
    });
    final markerId = MarkerId('ambulance');
    _markers[markerId] = Marker(
      markerId: markerId,
      position: LatLng(currentPosition.latitude, currentPosition.longitude),
      icon: _pinAmbulance!,
    );

    points.add(LatLng(currentPosition.latitude, currentPosition.longitude));

    if (points.length > 0) {
      //LatLngBounds bounds = MapsUtil.getLatLngBounds(points);
      // _controller.animateCamera(CameraUpdate.newLatLngBounds(bounds, 40.0));
    }

    setState(() {});
  }

  Future<void> _showPointInMap() async {
    final List<AvailableMap>? availableMaps = await MapLauncher.installedMaps;

    if (availableMaps!.length > 1)
      _openModalMaps(context, availableMaps);
    else {
      Uri googleUrl = Uri();
      if (verifyDestiny()) {
        googleUrl = Uri.parse(
            'https://www.google.com/maps/dir/?api=1&origin=${currentPosition!.latitude},${currentPosition!.longitude}&destination=${destinyCoordenates.latitude},${destinyCoordenates.longitude}&travelmode=driving');
      } else {
        googleUrl = Uri.parse(
            'https://www.google.com/maps/dir/?api=1&origin=${currentPosition!.latitude},${currentPosition!.longitude}&destination=${attendanceCoordenates.latitude},${attendanceCoordenates.longitude}&travelmode=driving');
      }

      if (await canLaunchUrl(googleUrl)) {
        launchUrl(googleUrl);
      } else {
        throw 'Could not open map.';
      }
    }
  }

  bool verifyDestiny() {
    return ((widget.codStatusCurrent == 0
                    ? widget.attendance!.codStatus
                    : widget.codStatusCurrent)! >=
                AttendanceStatus.CHEGADA_ORIGEM &&
            widget.attendance!.enderecoDestino != null)
        ? true
        : false;
  }

  void _openModalMaps(BuildContext context, List<AvailableMap> availableMaps) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: SingleChildScrollView(
            child: Container(
              child: Wrap(
                children: <Widget>[
                  for (var map in availableMaps)
                    ListTile(
                      onTap: () => {
                        Navigator.of(context).pop(),
                        map.showDirections(
                            destination: verifyDestiny()
                                ? Coords(destinyCoordenates.latitude,
                                    destinyCoordenates.longitude)
                                : Coords(attendanceCoordenates.latitude,
                                    attendanceCoordenates.longitude),
                            origin: Coords(currentPosition!.latitude,
                                currentPosition!.longitude))
                      },
                      title: Text(map.mapName),
                      leading: SvgPicture.asset(
                        map.icon,
                        height: 30.0,
                        width: 30.0,
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _alertError(text, {Function? callbackClose}) {
    Alert.open(context,
        title: I18nHelper.translate(context, '$_baseTransalate.warnings.title'),
        text: text,
        callbackClose: callbackClose);
  }
}
