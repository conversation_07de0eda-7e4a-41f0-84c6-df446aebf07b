import 'package:ambulancia_app/bloc/attendance/signal_protocol/signal_protocol_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/close-attendanceV2.model.dart';
import 'package:ambulancia_app/models/signal_protocol_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_body/conduct/close_attendance/modal_reclassification.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/widgets/modal/modal_photo_medical.dart';
import 'package:ambulancia_app/shared/widgets/unimed_select_loading.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:unimed_select/unimed-select.dart';

class AssistanceProtocolsAttendancePopup extends StatefulWidget {
  final AttendanceModel attendanceModel;
  final CloseAttendanceV2Model? closeAttendanceV2;

  const AssistanceProtocolsAttendancePopup(
      {required this.attendanceModel, this.closeAttendanceV2});

  @override
  _AssistanceProtocolsAttendancePopupState createState() =>
      _AssistanceProtocolsAttendancePopupState();
}

class _AssistanceProtocolsAttendancePopupState
    extends State<AssistanceProtocolsAttendancePopup> {
  final TextEditingController signalProtocolOtherController =
      TextEditingController();
  final TextEditingController signalProtocolSelectedController =
      TextEditingController();
  final GlobalKey<FormState> _formKeyProtocolSelected = GlobalKey<FormState>();

  bool isConfirmProtocolSelected = true;
  SignalProtocolModel? signalProtocolSelected;
  bool isDisabledCloseButton = true;

  final String _baseTranslate = 'assistanceProtocols';

  @override
  void initState() {
    super.initState();
    context.read<SignalProtocolCubit>().getAttendanceListSinalProtocols();
  }

  @override
  Widget build(BuildContext context) {
    String optionOuther =
        I18nHelper.translate(context, '$_baseTranslate.otherProtocol');

    return AlertDialog(
      title: Text(I18nHelper.translate(
          context, '$_baseTranslate.confirmProtocolTitle')),
      actionsAlignment: MainAxisAlignment.spaceAround,
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.75,
        child: BlocConsumer<SignalProtocolCubit, SinalProtocolState>(
          listener: _handleStateChanges,
          builder: (context, state) =>
              _buildContent(state, optionOuther: optionOuther),
        ),
      ),
      actions: [_buildActions()],
    );
  }

  void _handleStateChanges(BuildContext context, SinalProtocolState state) {
    if (state is SetProtocolConfirmationtAttendanceState) {
      isConfirmProtocolSelected = state.value;
    } else if (state is SetSignalProtocolSelected) {
      signalProtocolSelected = state.value;
    }
  }

  Widget _buildContent(SinalProtocolState state,
      {required String optionOuther}) {
    if (state is LoadedSinalProtocolsState ||
        state is SetProtocolConfirmationtAttendanceState ||
        state is SetSignalProtocolSelected ||
        state is LoadingSinalProtocolsState) {
      return Form(
        key: _formKeyProtocolSelected,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProtocolTitle(optionOuther: optionOuther),
            _buildRadioOption("Sim", true),
            _buildRadioOption("Não", false),
            if (isConfirmProtocolSelected) _buildSignalProtocolDropdown(state),
            _buildOtherField(optionOuther: optionOuther),
          ],
        ),
      );
    } else if (state is ErrorLoadSinalProtocolsState) {
      return _buildErrorState(state.message);
    }
    return _loadingListSignalProtocolAttendance();
  }

  Widget _buildProtocolTitle({required String optionOuther}) {
    return RichText(
      text: TextSpan(
        text: I18nHelper.translate(
            context, '$_baseTranslate.eligibleProtocolText'),
        style: TextStyle(fontSize: 19, color: AmbulanceColors.grayDark),
        children: [
          TextSpan(
            text: " " + _getProtocolTitle(optionOuther: optionOuther),
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 19,
              color: AmbulanceColors.grayDark,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRadioOption(String text, bool value) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minLeadingWidth: 0,
      title: Text(text),
      leading: Transform.scale(
        scale: 1.5,
        child: Radio<bool>(
          materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
          value: value,
          groupValue: isConfirmProtocolSelected,
          onChanged: (bool? newValue) {
            context
                .read<SignalProtocolCubit>()
                .setProtocolConfirmationtAttendance(value: newValue!);
            if (!newValue) cleanSignalProtocolSelected();
          },
        ),
      ),
    );
  }

  Widget _buildSignalProtocolDropdown(SinalProtocolState state) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 14),
      child: state is LoadingSinalProtocolsState
          ? UnimedSelectLoading(
              title:
                  I18nHelper.translate(context, '$_baseTranslate.signalsLabel'),
              boxDecoration: _buildBoxDecoration(),
              titleStyle: _buildTitleStyle(),
              loadingColor: AmbulanceColors.greenLight3,
              loadingSize: 24.0,
            )
          : _dropDownItensSignalProtocolAttendance(
              listSignalProtocolAttendanceItens:
                  context.read<SignalProtocolCubit>().getListSinalProtocols(),
            ),
    );
  }

  Widget _buildOtherField({required String optionOuther}) {
    return ((signalProtocolSelected != null &&
                signalProtocolSelected!.nameSignalProtocol == optionOuther) ||
            !isConfirmProtocolSelected)
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                I18nHelper.translate(
                    context, '$_baseTranslate.observationsLabel'),
                style: TextStyle(
                  fontSize: 20,
                  color: AmbulanceColors.grayDark,
                  fontFamily: 'UnimedSans',
                ),
              ),
              SizedBox(height: ConstantsTheme.padding * 0.5),
              TextFormField(
                controller: signalProtocolOtherController,
                validator: (value) => value!.trim().isEmpty
                    ? I18nHelper.translate(
                        context, '$_baseTranslate.requiredFieldError')
                    : null,
                decoration: InputDecoration(
                  filled: true,
                  fillColor: Colors.white,
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey, width: 1),
                    borderRadius: BorderRadius.circular(ConstantsTheme.padding),
                  ),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: Colors.grey, width: 1),
                    borderRadius: BorderRadius.circular(ConstantsTheme.padding),
                  ),
                  contentPadding: EdgeInsets.fromLTRB(15.0, 15.0, 20.0, 15.0),
                ),
                style: TextStyle(color: AmbulanceColors.grayDark),
              ),
            ],
          )
        : Container();
  }

  Widget _buildActions() {
    return BlocConsumer<SignalProtocolCubit, SinalProtocolState>(
      listener: (context, state) {
        if (state is SetProtocolConfirmationtAttendanceState) {
          isDisabledCloseButton = state.value && signalProtocolSelected == null;
        } else if (state is SetSignalProtocolSelected) {
          isDisabledCloseButton = false;
        }
      },
      builder: (context, state) {
        return Visibility(
          visible: state is LoadedSinalProtocolsState ||
              state is SetProtocolConfirmationtAttendanceState ||
              state is SetSignalProtocolSelected ||
              state is LoadingSinalProtocolsState,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildActionButton(
                I18nHelper.translate(
                    context, '$_baseTranslate.continueClosureButton'),
                AmbulanceColors.greenChart,
                _handleContinueClosure,
                isDisabledCloseButton || state is LoadingSinalProtocolsState,
              ),
              _buildActionButton(
                I18nHelper.translate(context, '$_baseTranslate.cancelButton'),
                AmbulanceColors.redClose,
                () => Navigator.pop(context),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActionButton(String text, Color color, VoidCallback onPressed,
      [bool isDisabled = false]) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: color,
        padding: EdgeInsets.symmetric(vertical: 18.0),
      ),
      onPressed: isDisabled ? null : onPressed,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 54),
        child: Text(
          text,
          style: TextStyle(fontSize: 20),
        ),
      ),
    );
  }

  void _handleContinueClosure() {
    if (_formKeyProtocolSelected.currentState!.validate()) {
      if (widget.attendanceModel.solicitaReclassifTipoAtend) {
        _showReclassifyPopup();
      } else {
        _openModalPhotoMedical();
      }
    }
  }

  void _showReclassifyPopup() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return ReclassifyPopup(
          attendanceModel: widget.attendanceModel,
          codProtocoloDoenca: signalProtocolSelected?.codSignalProtocol,
          obsProtocoloDoenca: signalProtocolOtherController.text,
          closeAttendanceV2: widget.closeAttendanceV2?.copyWith(
            codProtocoloDoenca: signalProtocolSelected?.codSignalProtocol,
            obsProtocoloDoenca: signalProtocolOtherController.text,
          ),
        );
      },
    );
  }

  void _openModalPhotoMedical() {
    openModalPhotoMedical(
      context: context,
      attendanceModel: widget.attendanceModel,
      recordType:
          I18nHelper.translate(context, '$_baseTranslate.recordTypeClosure'),
      codProtocoloDoenca: signalProtocolSelected?.codSignalProtocol,
      obsProtocoloDoenca: signalProtocolOtherController.text,
      closeAttendanceV2: widget.closeAttendanceV2?.copyWith(
        codProtocoloDoenca: signalProtocolSelected?.codSignalProtocol,
        obsProtocoloDoenca: signalProtocolOtherController.text,
      ),
    );
  }

  Widget _dropDownItensSignalProtocolAttendance(
      {required List<SignalProtocolModel> listSignalProtocolAttendanceItens}) {
    return UnimedSelect<SignalProtocolModel?>(
      boxDecoration: _buildBoxDecoration(),
      controller: signalProtocolSelectedController,
      showClearButton: false,
      tittleWidget: Text(
        I18nHelper.translate(context, '$_baseTranslate.signalsLabel'),
        style: _buildTitleStyle(),
      ),
      onSelect: (SignalProtocolAttendance) {
        signalProtocolOtherController.clear();
        context.read<SignalProtocolCubit>().setSignalProtocolAttendanceSelected(
              value: SignalProtocolAttendance!,
            );
      },
      items: listSignalProtocolAttendanceItens
          .map(
            (entry) => UnimedSelectItemModel<SignalProtocolModel?>(
              value: SignalProtocolModel(
                codSignalProtocol: entry.codSignalProtocol,
                nameSignalProtocol: entry.nameSignalProtocol,
              ),
              label: "${entry.codSignalProtocol} - ${entry.nameSignalProtocol}",
            ),
          )
          .toList(),
    );
  }

  Widget _buildErrorState(String message) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.15,
      width: MediaQuery.of(context).size.width * 0.75,
      child: Column(
        children: [
          Image.asset(
            'assets/images/roberta/roberta-triste.png',
            height: MediaQuery.of(context).size.height * 0.1,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(message),
              IconButton(
                onPressed: () {
                  context
                      .read<SignalProtocolCubit>()
                      .getAttendanceListSinalProtocols();
                },
                icon: Icon(Icons.refresh, size: 28),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _loadingListSignalProtocolAttendance() {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.13,
      width: MediaQuery.of(context).size.width * 0.75,
      child: Center(
        child: SpinKitThreeBounce(
          size: 40,
          color: AmbulanceColors.green,
        ),
      ),
    );
  }

  BoxDecoration _buildBoxDecoration() {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(ConstantsTheme.padding),
      border: Border.all(color: Colors.black, width: 1),
    );
  }

  TextStyle _buildTitleStyle() {
    return TextStyle(
      color: AmbulanceColors.grayDark,
      fontSize: 20,
    );
  }

  String _getProtocolTitle({required String optionOuther}) {
    final protocol = widget.attendanceModel.uniurgProtocoloDoencas;
    if (protocol?.nomeProtocoloDoenca == optionOuther) {
      return widget.attendanceModel.obsProtocoloDoenca;
    }
    return protocol?.nomeProtocoloDoenca ??
        I18nHelper.translate(context, '$_baseTranslate.notRegisteredProtocol');
  }

  void cleanSignalProtocolSelected() {
    signalProtocolSelected = null;
    signalProtocolSelectedController.clear();
    signalProtocolOtherController.clear();
  }
}
