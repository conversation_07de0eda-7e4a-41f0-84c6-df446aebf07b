import 'package:ambulancia_app/bloc/attendance/attendance_cancel/attendance_cancel_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/attendance_movemets/motive_attendance/motive_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/motive.model.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:unimed_select/unimed-select.dart';

class AttendanceCancel extends StatefulWidget {
  final AttendanceModel? attendance;
  final Function? onCancel;
  final Function? onConfirm;

  AttendanceCancel({this.attendance, this.onCancel, this.onConfirm});

  @override
  _AttendanceCancelState createState() => _AttendanceCancelState();
}

class _AttendanceCancelState extends State<AttendanceCancel> {
  TextEditingController motiveController = TextEditingController();
  TextEditingController motiveOtherController = TextEditingController();
  bool motiveEmpty = true;
  MotiveModel? motiveSelected;
  final _formKeyAttendanceCancel = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();

    motiveController.addListener(() {
      setState(() {
        motiveEmpty = motiveController.text.isEmpty;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MotiveCubit, MotiveState>(
      builder: (context, state) {
        if (state is LoadedMotivesState) {
          return Form(
            key: _formKeyAttendanceCancel,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: ListView(
              shrinkWrap: true,
              children: [
                Text(
                  'Informe o motivo do cancelamento',
                  style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 22,
                      color: AmbulanceColors.grayDark2),
                ),
                _divider(),
                SizedBox(height: ConstantsTheme.padding * 0.5),
                UnimedSelect<MotiveModel>(
                  title: 'Motivos',
                  controller: motiveController,
                  items: state.motives!
                      .map((MotiveModel motive) => UnimedSelectItemModel(
                            label: "${motive.codigo} - ${motive.descricao}",
                            value: motive,
                          ))
                      .toList(),
                  onSelect: (MotiveModel? value) {
                    setState(() => motiveSelected = value);
                  },
                ),
                _otherField(),
                _buildLoading(),
                _divider(),
              ],
            ),
          );
        } else if (state is LoadingMotivesState) {
          return SpinKitThreeBounce(
            color: AmbulanceColors.green,
          );
        } else if (state is ErrorLoadMotivesState) {
          return Column(
            children: [
              Text(state.message),
              Padding(
                padding: const EdgeInsets.only(left: 10, top: 20),
                child: IconButton(
                  color: AmbulanceColors.green,
                  iconSize: 40,
                  icon: Icon(Icons.refresh),
                  onPressed: () {
                    context.read<MotiveCubit>().getAttendanceMotives(
                      codUnimed: widget.attendance!.codUnimed,
                    );
                  },
                ),
              ),
            ],
          );
        } else {
          return Container();
        }
      },
    );
  }

  Widget _otherField() {
    return motiveSelected != null && motiveSelected!.descricao == 'OUTROS'
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _divider(),
              Text(
                'Descreva o motivo *',
                style: TextStyle(fontSize: 14),
              ),
              SizedBox(height: ConstantsTheme.padding * 0.5),
              TextFormField(
                key: Key('attendance_reason_ancellation'),
                textAlign: TextAlign.left,
                textAlignVertical: TextAlignVertical.top,
                keyboardType: TextInputType.text,
                maxLines: 5,
                controller: motiveOtherController,
                textInputAction: TextInputAction.next,
                onFieldSubmitted: (term) {},
                validator: (value) {
                  if (value!.isEmpty)
                    return "campo obrigatório.";
                  else
                    return null;
                },
                decoration: InputDecoration(
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(color: AmbulanceColors.purple),
                  ),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(color: AmbulanceColors.purple),
                  ),
                  contentPadding: EdgeInsets.fromLTRB(15.0, 15.0, 20.0, 15.0),
                ),
                style: TextStyle(color: AmbulanceColors.grayDark),
              )
            ],
          )
        : Container();
  }

  Widget _divider() {
    return SizedBox(height: ConstantsTheme.doublePadding);
  }

  Widget _buildLoading() {
    return BlocBuilder<AttendanceCancelCubit, AttendanceCancelState>(
      buildWhen: (presState, state) {
        if (state is CanceledAttendanceState) {
          if (widget.onConfirm != null) widget.onConfirm!();
        } else if (state is ErroCancelAttendanceState) {
          _alertError(state.message);
        }
        return true;
      },
      builder: (context, state) {
        if (state is CancelingAttendanceState)
          return SpinKitThreeBounce(color: AmbulanceColors.green);
        else
          return _options(context);
      },
    );
  }

  _alertError(text) {
    Alert.open(
      context,
      textButtonClose: 'Fechar',
      title: 'Alerta',
      text: text,
    );
  }

  Widget _options(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            style: TextButton.styleFrom(
              foregroundColor: Colors.white,
              backgroundColor: AmbulanceColors.redClose,
              padding: EdgeInsets.symmetric(vertical: 16.0),
            ),
            onPressed: () {
              if (widget.onCancel != null) widget.onCancel!();
              context.read<AttendanceCancelCubit>().resetStateCancel();
              Navigator.of(context).pop();
            },
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 50),
                child: Text(
                  "CANCELAR",
                  style: TextStyle(
                    fontSize: 20,
                    color: Colors.white,
                  ),
                )),
          ),
          Container(
            width: ConstantsTheme.doublePadding,
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: AmbulanceColors.greenLight3,
              padding: EdgeInsets.symmetric(vertical: 16.0),
            ),
            onPressed: motiveEmpty ? null : _finalizar,
            child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 50),
                child: Text(
                  motiveEmpty ? "ESCREVA O MOTIVO" : "FINALIZAR",
                  style: TextStyle(fontSize: 20),
                )),
          )
        ],
      ),
    );
  }

  _finalizar() {
    if (motiveSelected != null && motiveSelected!.descricao == 'OUTROS') {
      if (_formKeyAttendanceCancel.currentState!.validate()) {
        context.read<AttendanceCancelCubit>().cancelAttendance(
            motiveSelected!, motiveOtherController.text, widget.attendance!);
      }
    } else {
      context.read<AttendanceCancelCubit>().cancelAttendance(
          motiveSelected!, motiveOtherController.text, widget.attendance!);
    }
  }
}
