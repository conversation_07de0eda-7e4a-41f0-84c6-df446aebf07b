import 'dart:convert';
import 'dart:typed_data';

import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';

void showImageDialog(BuildContext context, String base64String) {
  Uint8List uint8List = base64Decode(base64String);

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        content: Image.memory(
          height: MediaQuery.of(context).size.height * 0.8,
          width: MediaQuery.of(context).size.width * 0.8,
          uint8List,
          fit: BoxFit.cover,
        ),
        actions: [
          Center(
            child: TextButton(
              style: TextButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: AmbulanceColors.green,
                padding: EdgeInsets.symmetric(vertical: 16.0),
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 50),
                child: Text(
                  "Fechar".toUpperCase(),
                  style: TextStyle(
                    fontSize: 20,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      );
    },
  );
}
