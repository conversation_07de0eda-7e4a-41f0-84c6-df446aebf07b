import 'package:ambulancia_app/bloc/attendance/subscription/cubit/subscription_cubit.dart';
import 'package:ambulancia_app/bloc/connectivity/connectivity_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/screens/signature/main.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/icons/ambulanceApp_icons.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class AttendanceRegulation extends StatefulWidget {
  final AttendanceModel? attendance;

  AttendanceRegulation({this.attendance});

  @override
  _AttendanceRegulationState createState() => _AttendanceRegulationState();
}

class _AttendanceRegulationState extends State<AttendanceRegulation> {
  final _baseTranslate = 'attendanceScreen.menu.regulation.content';

  final String _baseTranslateProtocols = 'assistanceProtocols';

  @override
  Widget build(BuildContext context) {
    String optionOuther =
        I18nHelper.translate(context, '$_baseTranslateProtocols.otherProtocol');

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _protocolTitleTag(optionOuther: optionOuther),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: _infoItem(
                label: I18nHelper.translate(
                    context, '$_baseTranslate.whatHappened'),
                value: widget.attendance?.sintomaAcontecendo ?? '',
              ),
            ),
            BlocBuilder<ConnectivityCubit, ConnectivityState>(
              builder: (context, connectivitystate) {
                return BlocBuilder<SubscriptionCubit, SubscriptionState>(
                  builder: (context, state) {
                    if (state is LoadingVerifySubscriptionState) {
                      return SpinKitThreeBounce(
                        size: 20,
                        color: AmbulanceColors.green,
                      );
                    }
                    if (state is SuccessSendVerifySubscriptionState) {
                      final conditionSigned =
                          (state.signed == null || state.signed == false);
                      return TextButton(
                          style: TextButton.styleFrom(
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.all(8.0),
                              backgroundColor: (conditionSigned &&
                                      (conditionSigned &&
                                          state.codStatus! >= 5 &&
                                          state.codStatus! != 9 &&
                                          (connectivitystate
                                              is ConnectivityOnlineState)))
                                  ? AmbulanceColors.green
                                  : AmbulanceColors.grayDark),
                          onPressed: (conditionSigned &&
                                  state.codStatus! >= 5 &&
                                  state.codStatus! != 9 &&
                                  (connectivitystate
                                      is ConnectivityOnlineState))
                              ? () {
                                  _openModal(
                                    SignaturePadScreen(
                                      serviceNumber:
                                          widget.attendance!.numAtendimento,
                                      status: state.codStatus,
                                    ),
                                    context,
                                  );
                                }
                              : null,
                          child: Text(
                            I18nHelper.translate(
                                context,
                                conditionSigned
                                    ? '$_baseTranslate.signature'
                                    : '$_baseTranslate.signed'),
                            style: TextStyle(color: Colors.white),
                          ));
                    } else if (state is ErrorVerifySubscriptionState) {
                      return Column(
                        children: [
                          Text(
                            state.message,
                            style: TextStyle(color: Colors.red),
                          ),
                          IconButton(
                              icon: Icon(Icons.refresh, color: unimedGreen),
                              onPressed: () => {
                                    context
                                        .read<SubscriptionCubit>()
                                        .verifySubscription(
                                            widget.attendance!.numAtendimento,
                                            widget.attendance!.codStatus)
                                  })
                        ],
                      );
                    }
                    return Container();
                  },
                );
              },
            )
          ],
        ),
        _infoItem(
          label:
              "${I18nHelper.translate(context, '$_baseTranslate.observable')}: ",
          value: widget.attendance?.observacaoProcedimento ?? '',
        ),
        _infoItem(
          label: I18nHelper.translate(context, '$_baseTranslate.when'),
          value: widget.attendance?.sintomaQuando ?? '',
        ),
        _infoItem(
          label: I18nHelper.translate(context, '$_baseTranslate.howStart'),
          value: widget.attendance?.sintomaComecou ?? '',
        ),
        _infoItem(
          label: I18nHelper.translate(context, '$_baseTranslate.historic'),
          value: widget.attendance?.sintomaHistorico ?? '',
        ),
        _infoItem(
          label: I18nHelper.translate(context, '$_baseTranslate.hypothesis'),
          value: widget.attendance?.hipoteseDiagnostica ?? '',
        ),
        _divider(),
        _medicalRegulation(context),
        _divider(),
        Text(widget.attendance?.observacaoMedicoRegulador ?? '',
            textAlign: TextAlign.justify, style: TextStyle(fontSize: 16)),
      ],
    );
  }

  void _openModal(Widget child, context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Assinatura"),
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius)),
          insetPadding: EdgeInsets.only(
            top: ConstantsTheme.doublePadding * 3,
            bottom: ConstantsTheme.doublePadding,
            left: ConstantsTheme.doublePadding,
            right: ConstantsTheme.doublePadding,
          ),
          content: Container(
            width: MediaQuery.of(context).size.width * 0.75,
            child: child,
            height: MediaQuery.of(context).size.height * 0.56,
          ),
        );
      },
    );
  }

  Widget _medicalRegulation(context) {
    const _hWidget = 50.0;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: _hWidget,
          padding: EdgeInsets.symmetric(
              vertical: ConstantsTheme.padding,
              horizontal: ConstantsTheme.doublePadding),
          color: AmbulanceColors.greenLight,
          child: Text(
            I18nHelper.translate(context, '$_baseTranslate.medicalRegulation'),
            style: TextStyle(
              color: AmbulanceColors.green,
              fontSize: 16,
            ),
          ),
        ),
        SizedBox(width: ConstantsTheme.padding * .33),
        Container(
          height: _hWidget,
          padding: EdgeInsets.all(ConstantsTheme.padding),
          decoration: BoxDecoration(
              color: AmbulanceColors.purpleSecondary,
              borderRadius: BorderRadius.only(
                bottomRight: Radius.circular(ConstantsTheme.borderRadius),
                topRight: Radius.circular(ConstantsTheme.borderRadius),
              )),
          child: Icon(
            AmbulanceIcons.doctor,
            color: Colors.white,
            size: 30,
          ),
        ),
        SizedBox(width: ConstantsTheme.padding),
      ],
    );
  }

  Widget _divider() {
    return SizedBox(height: ConstantsTheme.doublePadding);
  }

  Widget _infoItem({String? label, String? value}) {
    if (value == '') return Container();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label!,
          style: TextStyle(fontSize: 18, color: AmbulanceColors.greenLight3),
        ),
        SizedBox(height: ConstantsTheme.padding * 0.5),
        Text(
          value!,
          style: TextStyle(fontSize: 18),
        ),
        _divider(),
      ],
    );
  }

  Widget _protocolTitleTag({required String optionOuther}) {
    return Visibility(
      visible: widget.attendance != null &&
          widget.attendance!.uniurgProtocoloDoencas != null,
      child: Container(
        width: MediaQuery.of(context).size.width,
        margin: EdgeInsets.only(bottom: ConstantsTheme.padding),
        padding: EdgeInsets.symmetric(
            horizontal: ConstantsTheme.doublePadding,
            vertical: ConstantsTheme.padding),
        color: AmbulanceColors.orange,
        child: Center(
            child: RichText(
          text: TextSpan(
            text: I18nHelper.translate(
                    context, '$_baseTranslateProtocols.eligibleProtocolText')
                .toUpperCase(),
            style: TextStyle(
                color: Colors.white, fontWeight: FontWeight.bold, fontSize: 16),
            children: [
              TextSpan(
                text: " " +
                    _getProtocolTitle(optionOuther: optionOuther).toUpperCase(),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        )),
      ),
    );
  }

  String _getProtocolTitle({required String optionOuther}) {
    if (widget.attendance != null) {
      final UniurgProtocoloDoencas? protocol =
          widget.attendance!.uniurgProtocoloDoencas;
      if (protocol != null) {
        if (protocol.nomeProtocoloDoenca == optionOuther) {
          return widget.attendance!.obsProtocoloDoenca;
        }
        return protocol.nomeProtocoloDoenca ??
            I18nHelper.translate(
                context, '$_baseTranslateProtocols.notRegisteredProtocol');
      }
    }
    return I18nHelper.translate(
        context, '$_baseTranslateProtocols.notRegisteredProtocol');
  }
}
