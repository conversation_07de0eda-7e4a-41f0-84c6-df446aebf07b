import 'package:ambulancia_app/bloc/attendance/attendance_cubit.dart';
import 'package:ambulancia_app/bloc/attendance/attendance_movemets/attendance_movements_cubit.dart';
import 'package:ambulancia_app/bloc/new_service/cubit/is_button_save_disabled.dart';
import 'package:ambulancia_app/bloc/new_service/cubit/new_service_able_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/text_field_custom.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class NewService extends StatefulWidget {
  NewService({
    super.key,
    required this.statusAttendance,
    required this.attendance,
  });

  final int statusAttendance;
  final AttendanceModel attendance;

  @override
  State<NewService> createState() => _NewServiceState();
}

class _NewServiceState extends State<NewService> {
  @override
  void initState() {
    super.initState();
    context.read<NewServiceAbleCubit>().updateButtonNewServiceAble(
          serviceStatus: widget.statusAttendance,
        );
    _textFieldNewNumberAttendanceController.addListener(() {
      context.read<IsButtonSaveDisabledNewServiceAbleCubit>().visibleButtonSave(
            disable: _textFieldNewNumberAttendanceController.text.isNotEmpty,
          );
    });
  }

  final GlobalKey<FormState> _formNewAttendance = GlobalKey<FormState>();
  TextEditingController _textFieldNewNumberAttendanceController =
      TextEditingController();

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<AttendanceMovementsCubit, AttendanceMovementsState>(
      listener: (context, state) {
        if (state is UpdatedMovementsState) {
          context.read<NewServiceAbleCubit>().updateButtonNewServiceAble(
                serviceStatus: state.codeStatus,
              );
        }
        if (state is UpdatingMovementsState) {
          context
              .read<NewServiceAbleCubit>()
              .updateButtonNewServiceAble(serviceStatus: 0);
        }

        if (state is ErrorMovementsState) {
          context.read<NewServiceAbleCubit>().updateButtonNewServiceAble(
                serviceStatus: widget.attendance.codStatus,
              );
        }
      },
      builder: (context, state) {
        return BlocBuilder<NewServiceAbleCubit, bool>(
          builder: (context, state) {
            return Container(
              width: MediaQuery.of(context).size.width,
              height: 70,
              padding: const EdgeInsets.all(15),
              child: ElevatedButton(
                style: ButtonStyle(
                  backgroundColor: WidgetStatePropertyAll(
                    state == true
                        ? AmbulanceColors.green
                        : AmbulanceColors.grayDark,
                  ),
                ),
                onPressed: (state == false)
                    ? null
                    : () {
                        _dialogNewAttendance(context);
                      },
                child: Text(
                  widget.statusAttendance == AttendanceStatus.NOVO_ATENDIMENTO
                      ? "Novo atendimento cadastrado".toUpperCase()
                      : "Novo atendimento".toUpperCase(),
                  style: TextStyle(
                    color: Colors.white,
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Future<void> _dialogNewAttendance(BuildContext context) async {
    _textFieldNewNumberAttendanceController.text = "";
    return showDialog(
      context: context,
      builder: (context) {
        return PopScope(
          canPop: false,
          child:
              BlocConsumer<AttendanceMovementsCubit, AttendanceMovementsState>(
            listener: (context, state) {
              if (state is UpdatedMovementsState) {
                context
                    .read<IsButtonSaveDisabledNewServiceAbleCubit>()
                    .visibleButtonSave(
                      disable: false,
                    );
                Navigator.pop(context);
                context.read<AttendanceCubit>().initAttendance(
                    numAtendimento: widget.attendance.numAtendimento);
                Alert.open(
                  context,
                  title: 'Atenção',
                  text: 'Novo atendimento registrado com sucesso.',
                );
              }
            },
            builder: (context, state) {
              return AlertDialog(
                content: SingleChildScrollView(
                  child: ListBody(
                    children: [
                      Container(
                        width: MediaQuery.of(context).size.width * 0.5,
                        child: Form(
                          key: _formNewAttendance,
                          child: TextFieldCustom(
                            controller: _textFieldNewNumberAttendanceController,
                            keyboardType: TextInputType.number,
                            inputFormatters: <TextInputFormatter>[
                              FilteringTextInputFormatter.digitsOnly,
                              LengthLimitingTextInputFormatter(6),
                            ],
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Campo obrigatório.';
                              }

                              return null;
                            },
                            title: 'Novo Atendimento *',
                            label: 'Digite o número do novo atendimento',
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                actionsAlignment: MainAxisAlignment.spaceAround,
                actions: (state is UpdatingMovementsState)
                    ? [
                        SpinKitThreeBounce(
                          color: AmbulanceColors.green,
                          size: 35,
                        )
                      ]
                    : [
                        Container(
                          height: 50,
                          width: 100,
                          child: BlocBuilder<
                              IsButtonSaveDisabledNewServiceAbleCubit,
                              bool>(builder: (context, state) {
                            return ElevatedButton(
                              child: Text('SALVAR'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AmbulanceColors.greenDark,
                                textStyle: TextStyle(color: Colors.white),
                              ),
                              onPressed: (state == true)
                                  ? () {
                                      if (_formNewAttendance.currentState!
                                          .validate()) {
                                        context
                                            .read<AttendanceMovementsCubit>()
                                            .updateStatusMovement(
                                              indexStatusApp: AttendanceStatus
                                                  .NOVO_ATENDIMENTO,
                                              attendance: widget
                                                  .attendance.numAtendimento
                                                  .toString(),
                                              isNewAttendance: true,
                                              numNewAttendance:
                                                  _textFieldNewNumberAttendanceController
                                                      .text,
                                            );
                                      }
                                    }
                                  : null,
                            );
                          }),
                        ),
                        Container(
                          height: 50,
                          width: 100,
                          child: ElevatedButton(
                            child: Text('FECHAR'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AmbulanceColors.redClose,
                              textStyle: TextStyle(color: Colors.white),
                            ),
                            onPressed: () {
                              _textFieldNewNumberAttendanceController.text = '';
                              Navigator.pop(context);
                            },
                          ),
                        ),
                      ],
              );
            },
          ),
        );
      },
    );
  }
}
