import 'package:ambulancia_app/bloc/attendance/attendance_movemets/attendance_movements_cubit.dart';
import 'package:ambulancia_app/bloc/attendance_team/attendance_team_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:ambulancia_app/models/funcoes_model.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class AttendanceTeamHeader extends StatefulWidget {
  final AttendanceModel? attendance;
  AttendanceTeamHeader({required this.attendance});
  @override
  _AttendanceTeamHeaderState createState() => _AttendanceTeamHeaderState();
}

class _AttendanceTeamHeaderState extends State<AttendanceTeamHeader> {
  int? codMedicoSubstituto;
  int? codEnfermeiroSubstituto;
  final _baseTranslate = 'attendanceScreen';
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _team();
  }

  Widget _team() {
    return BlocBuilder<AttendanceTeamCubit, AttendanceTeamState>(
        buildWhen: (previousState, state) {
      if (state is UpdatedAttendanceTeamState) {}
      return true;
    }, builder: (context, state) {
      if (state is LoadingAttendanceTeamState ||
          state is UpdatingAttendanceTeamState)
        return SpinKitThreeBounce(color: AmbulanceColors.greenDark);
      else if (state is ErrorAttendanceTeamState) {
        return Center(child: Text(state.messsage));
      } else if (state is ErrorUpdatingAttendanceTeamState) {
        return Center(child: Text(state.messsage));
      } else if (state is LoadedAttendanceTeamState) {
        final medicoAtual = widget.attendance!.equipe.firstWhereOrNull(
            (element) => element.codFuncao == CodFuncoes.MEDICO_SOCORRISTA);
        codMedicoSubstituto = medicoAtual?.codFuncionario;
        final enfermeiroAtual = widget.attendance!.equipe.firstWhereOrNull(
            (element) => element.codFuncao == CodFuncoes.ENFERMEIRO);

        codEnfermeiroSubstituto = enfermeiroAtual?.codFuncionario;
        return _listAvailableTeam(
          state.availableDoctors,
          state.availableNurses,
        );
      } else if (state is UpdatedAttendanceTeamState) {
        codMedicoSubstituto = state.lastcodDoctor;
        codEnfermeiroSubstituto = state.lastCodNurse;
        return _listAvailableTeam(
          state.availableDoctors,
          state.availableNurses,
        );
      } else {
        return Container();
      }
    });
  }

  _listAvailableTeam(availableDoctors, availableNurses) {
    return Row(
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15),
            child: _funcionariosFuncao(
              label: I18nHelper.translate(context, '$_baseTranslate.doctor'),
              funcionarios:
                  availableDoctors == null ? [] : availableDoctors.funcionarios,
              codFuncao: availableDoctors == null
                  ? CodFuncoes.MEDICO_SOCORRISTA
                  : availableDoctors.codFuncao,
              codFuncionarioAtual: codMedicoSubstituto,
            ),
          ),
        ),
      ],
    );
  }

  Widget _funcionariosFuncao({
    required String label,
    required List<FuncionarioModel> funcionarios,
    int? codFuncao,
    int? codFuncionarioAtual,
  }) {
    final codStatusApp =
        BlocProvider.of<AttendanceMovementsCubit>(context, listen: true)
            .getCurrentIndexStatusApp();

    final decoration = InputDecoration(
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius),
        borderSide: BorderSide(color: Colors.grey),
      ),
    );

    final FuncionarioModel? funcionario = funcionarios.isNotEmpty
        ? funcionarios.cast<FuncionarioModel?>().firstWhere(
            (element) => element?.codFuncionario == codFuncionarioAtual,
            orElse: () => null)
        : null;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(fontSize: 16),
        ),
        SizedBox(height: ConstantsTheme.padding),
        DropdownButtonFormField(
          value: funcionario,
          hint: funcionarios.isEmpty
              ? Text("Sem itens para ser selecionado")
              : Text("Selecione uma opção"),
          items: funcionarios
              .map<DropdownMenuItem>(
                (funcionario) => DropdownMenuItem(
                  child: Text(
                    funcionario.nomeFuncionario ?? 'Sem nome cadastrado',
                    style: TextStyle(color: AmbulanceColors.grayDark),
                    textScaler: TextScaler.linear(0.9),
                  ),
                  value: funcionario,
                ),
              )
              .toList(),
          onChanged: codStatusApp != -1
              ? null
              : (dynamic value) {
                  if (codFuncao == CodFuncoes.MEDICO_SOCORRISTA)
                    codMedicoSubstituto = value.codFuncionario;
                  else if (codFuncao == CodFuncoes.ENFERMEIRO)
                    codEnfermeiroSubstituto = value.codFuncionario;

                  context.read<AttendanceTeamCubit>().updateAttendanceTeam(
                      attendance: widget.attendance!,
                      codMedicoSubstituto: codMedicoSubstituto,
                      codEnfermeiroSubstituto: codEnfermeiroSubstituto);
                },
          decoration: decoration,
        )
      ],
    );
  }
}
