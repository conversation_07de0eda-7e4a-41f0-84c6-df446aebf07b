import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/cards/%20central-nervous-system.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/cards/%20venous-access.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/cards/cardiovascular-system.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/cards/gastrointestinal-system.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/cards/kidney-function.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/cards/pain-scale.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/cards/respiratory-apparatus.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/cards/skin.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/cards/surgical-information.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/photo.constants.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/background_unfocus/background_unfocus.dart';
import 'package:ambulancia_app/shared/widgets/modal/modal_photo.dart';

import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ClinicalEvaluation extends StatefulWidget {
  final AttendanceModel? attendanceModel;
  ClinicalEvaluation({Key? key, this.attendanceModel}) : super(key: key);

  @override
  _ClinicalEvaluationState createState() => _ClinicalEvaluationState();
}

class _ClinicalEvaluationState extends State<ClinicalEvaluation> {
  Widget? cardsContentCached;
  final _formKey = GlobalKey<FormState>();
  bool _formChanged = false;
  final _baseTranslate = 'evaluationClinicalForm';
  // TODO: Refatora a forma de como acompanhar as mudanças dos checkbox
  void _setFormChanged() {
    _formChanged = true;
  }

  @override
  void initState() {
    super.initState();
    context
        .read<ClinicEvaluationCubit>()
        .loadClinicEvaluation('${widget.attendanceModel!.numAtendimento}');
  }

  @override
  Widget build(BuildContext context) {
    return BackgroundUnfocus(
      child: BlocBuilder<ClinicEvaluationCubit, ClinicEvaluationState>(
        buildWhen: (preState, state) {
          if (state is ErrorLoadClinicEvaluationState) {
            _alertError(MessageException.general,
                callbackClose: () => Navigator.of(context).pop());
          }
          if (state is ErrorSendClinicEvaluationState) {
            _alertError(MessageException.general);
          }
          return true;
        },
        builder: (context, state) {
          if (state is LoadingClinicEvaluationState) {
            return SpinKitThreeBounce(
              color: AmbulanceColors.green,
            );
          } else if (state is LoadedClinicEvaluationState) {
            BlocProvider.of<ClinicEvaluationCubit>(context)
                .requestClinicEvaluation = state.requestClinicEvaluation;

            cardsContentCached = cardsContent(state.clinicEvaluationData);
            return cardsContentCached!;
          } else {
            return cardsContentCached ?? Container();
          }
        },
      ),
    );
  }

  Widget cardsContent(clinicEvaluationCofig) {
    return BlocBuilder<ClinicEvaluationCubit, ClinicEvaluationState>(
        builder: (context, state) {
      return PopScope(
        canPop: state is SavingClinicEvaluationState ? false : true,
        child: AbsorbPointer(
          absorbing: state is SavingClinicEvaluationState ? true : false,
          child: Column(
            children: [
              Expanded(
                child: SingleChildScrollView(
                  key: Key('list_cards_content'),
                  child: Form(
                    key: _formKey,
                    autovalidateMode: AutovalidateMode.always,
                    onChanged: () {
                      _formChanged = true;
                    },
                    child: Column(
                      children: <Widget>[
                        Align(
                          alignment: Alignment.topLeft,
                          child: Text(
                            I18nHelper.translate(
                                context, '$_baseTranslate.title'),
                            style: TextStyle(
                              fontWeight: FontWeight.w900,
                              fontSize: 22,
                              color: AmbulanceColors.grayDark,
                            ),
                          ),
                        ),
                        SizedBox(height: 10),
                        CardiovascularSystem(
                          emmitChangedForParent: _setFormChanged,
                          backgroundColor: AmbulanceColors.beige,
                          textColor: AmbulanceColors.greenDark,
                          clinicEvaluation: clinicEvaluationCofig,
                        ),
                        PainScaleCard(
                          emmitChangedForParent: _setFormChanged,
                          backgroundColor: AmbulanceColors.greenDark,
                          textColor: Colors.white,
                        ),
                        RespiratoryApparatus(
                          emmitChangedForParent: _setFormChanged,
                          backgroundColor: AmbulanceColors.purple,
                          textColor: Colors.white,
                          clinicEvaluation: clinicEvaluationCofig,
                        ),
                        KidneyFunction(
                          emmitChangedForParent: _setFormChanged,
                          backgroundColor: AmbulanceColors.greenDark,
                          textColor: Colors.white,
                          clinicEvaluation: clinicEvaluationCofig,
                        ),
                        CentralNervousSystem(
                          emmitChangedForParent: _setFormChanged,
                          backgroundColor: AmbulanceColors.greenLight,
                          textColor: AmbulanceColors.greenDark,
                          clinicEvaluation: clinicEvaluationCofig,
                        ),
                        GastrointestinalSystem(
                          emmitChangedForParent: _setFormChanged,
                          backgroundColor: AmbulanceColors.grayDark,
                          textColor: Colors.white,
                          clinicEvaluation: clinicEvaluationCofig,
                        ),
                        Skin(
                          emmitChangedForParent: _setFormChanged,
                          backgroundColor: AmbulanceColors.yellow,
                          textColor: AmbulanceColors.greenDark,
                          clinicEvaluation: clinicEvaluationCofig,
                        ),
                        VenousAccess(
                          emmitChangedForParent: _setFormChanged,
                          backgroundColor: AmbulanceColors.lightBlue,
                          textColor: AmbulanceColors.purple,
                          clinicEvaluation: clinicEvaluationCofig,
                        ),
                        SurgicalInformation(
                          emmitChangedForParent: _setFormChanged,
                          backgroundColor: AmbulanceColors.greenDark2,
                          textColor: Colors.white,
                          clinicEvaluation: clinicEvaluationCofig,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              _saveIndicator(),
              Padding(
                padding: const EdgeInsets.only(bottom: 0, top: 15),
                child: saveButton(),
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _saveIndicator() {
    return BlocBuilder<ClinicEvaluationCubit, ClinicEvaluationState>(
      buildWhen: (preState, state) {
        if (state is SuccessSendClinicEvaluationState) {
          _alertSucess(state.message);
        }

        return true;
      },
      builder: (context, state) {
        if (state is SavingClinicEvaluationState) {
          return Column(
            children: [
              Text(
                'Enviando',
                style: TextStyle(fontSize: 20),
              ),
              SpinKitThreeBounce(
                color: AmbulanceColors.green,
              ),
            ],
          );
        } else
          return Container();
      },
    );
  }

  Widget saveButton() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: AmbulanceColors.acceptColor,
            textStyle: TextStyle(color: Colors.white),
          ),
          child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 54, vertical: 20),
              child: Text(
                I18nHelper.translate(context, 'common.save'),
                style: TextStyle(fontSize: 22),
              )),
          onPressed: () {
            if (_formChanged) {
              _saveAndClose();
            } else {
              Navigator.pop(context);
            }
          },
        ),
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: AmbulanceColors.redClose,
            textStyle: TextStyle(color: Colors.white),
          ),
          child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 54, vertical: 20),
              child: Text(
                'FECHAR',
                style: TextStyle(fontSize: 22),
              )),
          onPressed: () {
            FocusManager.instance.primaryFocus?.unfocus();
            if (_formChanged) {
              showDialogClosed(
                  context,
                  I18nHelper.translate(
                      context, '$_baseTranslate.warnings.discardText'));
            } else {
              Navigator.pop(context);
            }
          },
        )
      ],
    );
  }

  void _saveAndClose() {
    FocusManager.instance.primaryFocus?.unfocus();
    if (_formKey.currentState!.validate()) {
      if (BlocProvider.of<ClinicEvaluationCubit>(context)
              .requestClinicEvaluation!
              .ritmos !=
          null) {
        if (_formChanged) {
          openModalPhotoRegister(
              reasonAnnexPhoto: photoReasonAnnexEnum.avaliacao_clinica.value,
              context: context,
              model: widget.attendanceModel,
              recordType:
                  I18nHelper.translate(context, '$_baseTranslate.title'));
        }
      } else {
        _showDialogErrorForm();
      }
    } else {
      _showDialogErrorForm();
    }
  }

  void _alertSucess(text) {
    Alert.open(context,
        title: I18nHelper.translate(context, 'common.sent'),
        text: text, callbackClose: () {
      Navigator.of(context).pop();
    });
  }

  void _alertError(text, {Function? callbackClose}) {
    Alert.open(context,
        title: I18nHelper.translate(context, '$_baseTranslate.warnings.title'),
        text: text,
        callbackClose: callbackClose);
  }

  void showDialogClosed(BuildContext context, String title) {
    Alert.open(context,
        buttonCloseColor: AmbulanceColors.green,
        title: I18nHelper.translate(context, '$_baseTranslate.warnings.title'),
        text: I18nHelper.translate(
            context, '$_baseTranslate.warnings.discardText'),
        textButtonClose: I18nHelper.translate(context, 'common.save'),
        callbackClose: () {
      _saveAndClose();
    }, actions: [
      ElevatedButton(
        style: ElevatedButton.styleFrom(
            backgroundColor: AmbulanceColors.redClose,
            textStyle: TextStyle(
              color: Colors.white,
            )),
        child: Text(I18nHelper.translate(
            context, '$_baseTranslate.warnings.biscardBtn')),
        onPressed: () {
          Navigator.of(context).pop();
          Navigator.of(context).pop();
        },
      ),
    ]);
  }

  _showDialogErrorForm() {
    Alert.open(
      context,
      text: I18nHelper.translate(
          context, '$_baseTranslate.warnings.requiredFields.text'),
      title: I18nHelper.translate(context, '$_baseTranslate.warnings.title'),
    );
  }
}
