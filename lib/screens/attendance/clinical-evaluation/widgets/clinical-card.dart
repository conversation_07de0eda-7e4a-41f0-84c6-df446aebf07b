import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';

//TODO verificar validadores
class ClincalCard extends StatelessWidget {
  final title;
  final cardColor;
  final textColor;
  final child;

  final GlobalKey? formKey;
  ClincalCard(
      {Key? key,
      required this.child,
      this.title = 'Títu<PERSON>',
      this.textColor = Colors.white,
      this.cardColor = AmbulanceColors.greenDark,
      this.formKey})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 5, vertical: 20),
        decoration: BoxDecoration(
          color: cardColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(ConstantsTheme.borderRadius * 2),
            topRight: Radius.circular(ConstantsTheme.borderRadius * 2),
            bottomLeft: Radius.circular(ConstantsTheme.borderRadius * 2),
          ),
        ),
        child: Column(
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w900,
                color: textColor,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Divider(
                height: 10,
                thickness: 1.3,
                color: textColor,
              ),
            ),
            Padding(
              padding: EdgeInsets.all(10),
              child: child,
            ),
          ],
        ),
      ),
    );
  }
}
