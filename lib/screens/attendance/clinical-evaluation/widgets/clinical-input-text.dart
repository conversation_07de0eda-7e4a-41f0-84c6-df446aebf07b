import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';

class ClinicalInputText extends StatelessWidget {
  final TextEditingController? controller;
  final title;
  final textColor;
  final mesure;
  final hintText;
  final double width;
  final Function(String)? onChange;
  final inputFormatters;
  final keyboardType;
  final Function(String?)? validator;
  ClinicalInputText(
      {Key? key,
      this.controller,
      this.hintText = '---',
      this.title = '',
      this.textColor = AmbulanceColors.greenDark,
      this.mesure = '',
      this.width = 65,
      this.onChange,
      this.inputFormatters,
      this.keyboardType,
      this.validator})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: TextStyle(color: textColor, fontSize: 20)),
        SizedBox(height: 5),
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              width: width,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.fromBorderSide(
                  BorderSide(
                    color: textColor,
                    width: 1.4,
                  ),
                ),
                borderRadius: BorderRadius.circular(ConstantsTheme.padding),
              ),
              child: TextFormField(
                validator: validator as String? Function(String?)?,
                autovalidateMode: AutovalidateMode.always,
                inputFormatters: inputFormatters ?? [],
                controller: controller,
                keyboardType: keyboardType ?? TextInputType.text,
                decoration: InputDecoration.collapsed(
                  hintText: hintText ?? '',
                ),
                onChanged: (value) {
                  if (onChange != null) onChange!(value);
                },
              ),
            ),
            SizedBox(width: 5),
            Flexible(
                child: Text(mesure,
                    style: TextStyle(color: textColor, fontSize: 18)))
          ],
        ),
      ],
    );
  }
}
