import 'package:ambulancia_app/bloc/auth/auth_cubit.dart';
import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/models/clinic-evaluation.model.dart';
import 'package:ambulancia_app/models/config_app_ambulancia_constants_model.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-card.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-check.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-text.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-radio-row.dart';
import 'package:ambulancia_app/shared/formatters.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class CardiovascularSystem extends StatefulWidget {
  final backgroundColor;
  final textColor;
  final ResponseClinicEvaluation clinicEvaluation;
  final emmitChangedForParent;
  CardiovascularSystem({
    Key? key,
    required this.clinicEvaluation,
    this.backgroundColor = AmbulanceColors.beige,
    this.textColor = AmbulanceColors.greenDark,
    this.emmitChangedForParent,
  }) : super(key: key);

  @override
  _CardiovascularSystemState createState() => _CardiovascularSystemState();
}

class _CardiovascularSystemState extends State<CardiovascularSystem> {
  late ClinicEvaluationCubit clinicEvaluationCubit;

  bool showPacemakerOptions = false;
  String? rhythmSelected;
  String? paceMarkerType;
  String? circulation;

  TextEditingController fcController = TextEditingController();
  TextEditingController frController = TextEditingController();
  TextEditingController paController = TextEditingController();
  TextEditingController spo2Controller = TextEditingController();
  TextEditingController tempController = TextEditingController();
  TextEditingController dxController = TextEditingController();

  final presureFormatter = MaskTextInputFormatter(mask: '###/###', filter: {
    "#": RegExp(r'[0-9]'),
  });
  var tempFormatter = MaskTextInputFormatter(mask: '##.#', filter: {
    "#": RegExp(r'[0-9]'),
  });
  final _baseTranslate = 'evaluationClinicalForm.cardiovascularSystem';

  @override
  void initState() {
    clinicEvaluationCubit = BlocProvider.of<ClinicEvaluationCubit>(context);

    clinicEvaluationCubit.requestClinicEvaluation!.circulacao ??=
        CECirculacaoCode.NotReferred;

    _iniciatilizeInput();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    ConfigAppAmbulanciaConstants _configAppAmbulanciaConstants =
        context.read<AuthCubit>().configAppAmbulanciaConstants;
    return ClincalCard(
      title: I18nHelper.translate(context, '$_baseTranslate.title'),
      cardColor: widget.backgroundColor,
      textColor: widget.textColor,
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClinicalInputText(
                      key: Key('input_text_fc'),
                      title:
                          I18nHelper.translate(context, '$_baseTranslate.fc'),
                      mesure:
                          I18nHelper.translate(context, '$_baseTranslate.bpm'),
                      validator: verifyRequired,
                      controller: fcController,
                      textColor: widget.textColor,
                      keyboardType: TextInputType.number,
                      inputFormatters:
                          FormatterField.inputNumberSuppliesFormatter,
                      onChange: (value) {
                        // valores passados pelo Marder 2021-21-09
                        var numValue = int.tryParse(value)!;
                        if (numValue >
                            _configAppAmbulanciaConstants
                                .cardiovascular.heartRateMax) {
                          numValue = _configAppAmbulanciaConstants
                              .cardiovascular.heartRateMax;
                          fcController.text = '$numValue';
                        }
                        clinicEvaluationCubit.requestClinicEvaluation!
                            .frequenciaCardiaca = numValue;
                      },
                    ),
                    // 0 a 300
                    legendMinMax(I18nHelper.translate(
                        context, '$_baseTranslate.helpCaption',
                        params: {
                          'minValue': _configAppAmbulanciaConstants
                              .cardiovascular.heartRateMin
                              .toString(),
                          'maxValue': _configAppAmbulanciaConstants
                              .cardiovascular.heartRateMax
                              .toString()
                        }))
                  ],
                ),
              ),
              _rhythms(),
              Flexible(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ClinicalInputText(
                    key: Key('input_text_fr'),
                    title: I18nHelper.translate(context, '$_baseTranslate.fr'),
                    mesure:
                        I18nHelper.translate(context, '$_baseTranslate.mrm'),
                    validator: verifyRequired,
                    controller: frController,
                    keyboardType: TextInputType.number,
                    inputFormatters:
                        FormatterField.inputNumberSuppliesFormatter,
                    textColor: widget.textColor,
                    onChange: (value) {
                      // valores passados pelo Marder 2021-21-09
                      var numValue = int.tryParse(value)!;
                      if (numValue >
                          _configAppAmbulanciaConstants
                              .cardiovascular.respiratoryRateMax) {
                        numValue = _configAppAmbulanciaConstants
                            .cardiovascular.respiratoryRateMax;
                        setState(() {
                          frController.text = '$numValue';
                        });
                      }
                      clinicEvaluationCubit.requestClinicEvaluation!
                          .frequenciaRespiratoria = numValue;
                    },
                  ),
                  // 0 a 60
                  legendMinMax(I18nHelper.translate(
                      context, '$_baseTranslate.helpCaption',
                      params: {
                        'minValue': _configAppAmbulanciaConstants
                            .cardiovascular.respiratoryRateMin
                            .toString(),
                        'maxValue': _configAppAmbulanciaConstants
                            .cardiovascular.respiratoryRateMax
                            .toString()
                      }))
                ],
              )),
            ],
          ),
          SizedBox(height: 30),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClinicalInputText(
                      key: Key('input_text_pa'),
                      title:
                          I18nHelper.translate(context, '$_baseTranslate.pa'),
                      mesure:
                          I18nHelper.translate(context, '$_baseTranslate.mmhg'),
                      validator: verifyRequired,
                      controller: paController,
                      keyboardType: TextInputType.number,
                      textColor: widget.textColor,
                      width: 95,
                      inputFormatters: [presureFormatter],
                      onChange: (value) {
                        final values = value.split('/');
                        int? pa = int.tryParse(values[0]);
                        int? pb = int.tryParse(values.length > 1
                            ? values[1]
                            : _configAppAmbulanciaConstants
                                .cardiovascular.bloodPressureBMin
                                .toString());

                        clinicEvaluationCubit.requestClinicEvaluation!
                            .pressaoArterialBaixa = (pb);
                        clinicEvaluationCubit.requestClinicEvaluation!
                            .pressaoArterialAlta = (pa);

                        // verify correct pressure and update
                        final int maxPa = _configAppAmbulanciaConstants
                            .cardiovascular.bloodPressureAMax;
                        final int maxPb = _configAppAmbulanciaConstants
                            .cardiovascular.bloodPressureBMax;
                        if (value.length > 6 && (pa! > maxPa || pb! > maxPb)) {
                          pa = pa > maxPa ? maxPa : pa;
                          pb = pb! > maxPb ? maxPb : pb;
                          paController.text = '$pa/$pb';
                          clinicEvaluationCubit.requestClinicEvaluation!
                              .pressaoArterialBaixa = (pb);
                          clinicEvaluationCubit.requestClinicEvaluation!
                              .pressaoArterialAlta = (pa);
                        }
                      },
                    ),
                    // 0 a 300
                    legendMinMax(I18nHelper.translate(
                        context, '$_baseTranslate.helpCaption',
                        params: {
                          'minValue': _configAppAmbulanciaConstants
                              .cardiovascular.bloodPressureAMin
                              .toString(),
                          'maxValue': _configAppAmbulanciaConstants
                              .cardiovascular.bloodPressureAMax
                              .toString()
                        }))
                  ],
                ),
              ),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClinicalInputText(
                      key: Key('input_text_spo2'),
                      title:
                          I18nHelper.translate(context, '$_baseTranslate.spo2'),
                      validator: verifyRequired,
                      mesure: '%',
                      controller: spo2Controller,
                      keyboardType: TextInputType.number,
                      inputFormatters:
                          FormatterField.inputNumberSuppliesFormatter,
                      textColor: widget.textColor,
                      onChange: (value) {
                        // valores passados pelo Marder 2021-21-09

                        final int oxygenSaturationMax =
                            _configAppAmbulanciaConstants
                                .cardiovascular.oxygenSaturationMax;

                        var numValue = int.tryParse(value)!;
                        if (numValue > oxygenSaturationMax) {
                          numValue = oxygenSaturationMax;
                          spo2Controller.text = '$numValue';
                        }
                        clinicEvaluationCubit
                            .requestClinicEvaluation!.saturacao = numValue;
                      },
                    ),
                    // 0 a 100
                    legendMinMax(I18nHelper.translate(
                        context, '$_baseTranslate.helpCaption',
                        params: {
                          'minValue': _configAppAmbulanciaConstants
                              .cardiovascular.oxygenSaturationMin
                              .toString(),
                          'maxValue': _configAppAmbulanciaConstants
                              .cardiovascular.oxygenSaturationMax
                              .toString()
                        }))
                  ],
                ),
              ),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClinicalInputText(
                      title: 'Dx',
                      mesure: 'mg/dl',
                      inputFormatters:
                          FormatterField.inputNumberSuppliesFormatter,
                      controller: dxController,
                      keyboardType: TextInputType.number,
                      textColor: widget.textColor,
                      onChange: (value) {
                        final int bloodGlucoseMax =
                            _configAppAmbulanciaConstants
                                .cardiovascular.bloodGlucoseMax;
                        var numValue = int.tryParse(value)!;
                        if (numValue > bloodGlucoseMax) {
                          numValue = bloodGlucoseMax;
                          dxController.text = '$numValue';
                        }
                        clinicEvaluationCubit.requestClinicEvaluation!.dx =
                            numValue;
                      },
                    ),
                    legendMinMax(I18nHelper.translate(
                        context, '$_baseTranslate.helpCaption',
                        params: {
                          'minValue': _configAppAmbulanciaConstants
                              .cardiovascular.bloodGlucoseMin
                              .toString(),
                          'maxValue': _configAppAmbulanciaConstants
                              .cardiovascular.bloodGlucoseMax
                              .toString()
                        }))
                  ],
                ),
              ),
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClinicalInputText(
                      title: 'Temperatura',
                      validator: verifyTemp,
                      mesure: '°C',
                      inputFormatters: [
                        tempFormatter,
                      ],
                      controller: tempController,
                      keyboardType: TextInputType.number,
                      textColor: widget.textColor,
                      onChange: (value) {
                        var numValue = double.tryParse(value)!;

                        final double temperatureMax =
                            _configAppAmbulanciaConstants
                                .cardiovascular.temperatureMax;
                        if (numValue > temperatureMax) {
                          numValue = temperatureMax;
                          tempController.text = '$numValue';
                          setState(() {
                            tempFormatter = MaskTextInputFormatter(
                                mask: '##.#',
                                initialText: '$numValue',
                                filter: {
                                  "#": RegExp(r'[0-9]'),
                                });
                          });
                        }
                        clinicEvaluationCubit
                            .requestClinicEvaluation!.temperatura = '$numValue';
                      },
                    ),
                    legendMinMax(I18nHelper.translate(
                        context, '$_baseTranslate.helpCaption',
                        params: {
                          'minValue': _configAppAmbulanciaConstants
                              .cardiovascular.temperatureMin
                              .toString(),
                          'maxValue': _configAppAmbulanciaConstants
                              .cardiovascular.temperatureMax
                              .toString()
                        }))
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 15),
          Divider(
            height: 15,
            thickness: 1.3,
            color: widget.textColor,
          ),
          SizedBox(height: 15),
          Row(
            children: [
              Flexible(flex: 10, child: _circulation()),
              if (showPacemakerOptions)
                Flexible(flex: 11, child: _paceMarkerOptions()),
            ],
          )
        ],
      ),
    );
  }

  Widget _circulation() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          I18nHelper.translate(context, '$_baseTranslate.circulation'),
          style: TextStyle(
            color: widget.textColor,
          ),
        ),
        //TODO passar essa regra para model
        Row(
            children: widget.clinicEvaluation.circulacao.map((element) {
          if (element.codigo == 'NR') return Container();
          return Flexible(
            child: ClinicalInputCheck(
              label: element.descricao,
              labelColor: widget.textColor,
              forceValue:
                  clinicEvaluationCubit.requestClinicEvaluation!.circulacao ==
                      element.codigo,
              onPresss: (isChecked) {
                setState(() {
                  final currentChoiceIsChecked = clinicEvaluationCubit
                          .requestClinicEvaluation!.circulacao ==
                      element.codigo;

                  if (currentChoiceIsChecked) {
                    clinicEvaluationCubit.requestClinicEvaluation!.circulacao =
                        CECirculacaoCode.NotReferred;
                    clinicEvaluationCubit
                        .requestClinicEvaluation!.tipoMarcaPasso = null;
                  } else {
                    clinicEvaluationCubit.requestClinicEvaluation!.circulacao =
                        element.codigo;
                  }

                  showPacemakerOptions = clinicEvaluationCubit
                          .requestClinicEvaluation!.circulacao ==
                      CECirculacaoCode.PaceMarker;

                  if (clinicEvaluationCubit
                          .requestClinicEvaluation!.circulacao ==
                      CECirculacaoCode.PrecordialPain) {
                    clinicEvaluationCubit
                        .requestClinicEvaluation!.tipoMarcaPasso = null;
                  }
                });
                widget.emmitChangedForParent();
              },
            ),
          );
        }).toList()),
      ],
    );
  }

  Widget _paceMarkerOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          I18nHelper.translate(context, '$_baseTranslate.pacemaker'),
          style: TextStyle(
            color: widget.textColor,
          ),
        ),
        Row(
          children: widget.clinicEvaluation.tipoMarcaPasso
              .map<Widget>((element) => ClinicalRadioRow<String>(
                    value: element.codigo,
                    groupValue: paceMarkerType,
                    text: element.descricao,
                    textColor: widget.textColor,
                    onChanged: (value) => setState(() {
                      paceMarkerType = value;
                      clinicEvaluationCubit
                          .requestClinicEvaluation!.tipoMarcaPasso = value;
                      widget.emmitChangedForParent();
                    }),
                  ))
              .toList(),
        )
      ],
    );
  }

  Widget _rhythms() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          I18nHelper.translate(context, '$_baseTranslate.rhythms'),
          style: TextStyle(
            color: widget.textColor,
          ),
        ),
        Row(
          children: widget.clinicEvaluation.ritmos
              .map<Widget>((element) => ClinicalRadioRow<String>(
                    keyRadio: Key('rhythms_${element.descricao}'),
                    value: element.codigo,
                    groupValue: rhythmSelected,
                    text: element.descricao,
                    textColor: widget.textColor,
                    onChanged: (value) => setState(() {
                      rhythmSelected = value;
                      clinicEvaluationCubit.requestClinicEvaluation!.ritmos =
                          value;
                      widget.emmitChangedForParent();
                    }),
                  ))
              .toList(),
        ),
        rhythmSelected != null
            ? Text("")
            : Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Text(
                    I18nHelper.translate(
                        context, 'filedsValidatorsWarnings.required'),
                    style: TextStyle(color: Colors.red)),
              ),
      ],
    );
  }

  Widget legendMinMax(String text) {
    return Padding(
      padding: const EdgeInsets.only(left: 2, top: 2),
      child: Text(
        text,
        style: TextStyle(
          color: AmbulanceColors.grayDark,
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  _iniciatilizeInput() {
    final tipoMarcaPasso =
        clinicEvaluationCubit.requestClinicEvaluation!.tipoMarcaPasso;

    paceMarkerType = tipoMarcaPasso;
    if (tipoMarcaPasso != null) showPacemakerOptions = true;

    fcController.text = clinicEvaluationCubit
                .requestClinicEvaluation!.frequenciaCardiaca ==
            null
        ? ''
        : '${clinicEvaluationCubit.requestClinicEvaluation!.frequenciaCardiaca}';

    frController.text = clinicEvaluationCubit
                .requestClinicEvaluation!.frequenciaRespiratoria ==
            null
        ? ''
        : '${clinicEvaluationCubit.requestClinicEvaluation!.frequenciaRespiratoria}';

    rhythmSelected = clinicEvaluationCubit.requestClinicEvaluation!.ritmos;

    paController.text = clinicEvaluationCubit
                    .requestClinicEvaluation!.pressaoArterialBaixa ==
                null ||
            clinicEvaluationCubit
                    .requestClinicEvaluation!.pressaoArterialAlta ==
                null
        ? ''
        : '${clinicEvaluationCubit.requestClinicEvaluation!.pressaoArterialAlta}/${clinicEvaluationCubit.requestClinicEvaluation!.pressaoArterialBaixa}';

    spo2Controller.text =
        clinicEvaluationCubit.requestClinicEvaluation!.saturacao == null
            ? ''
            : '${clinicEvaluationCubit.requestClinicEvaluation!.saturacao}';

    dxController.text =
        clinicEvaluationCubit.requestClinicEvaluation!.dx == null
            ? ''
            : '${clinicEvaluationCubit.requestClinicEvaluation!.dx}';

    tempController.text =
        clinicEvaluationCubit.requestClinicEvaluation!.temperatura == null
            ? ''
            : '${clinicEvaluationCubit.requestClinicEvaluation!.temperatura}';
    if (clinicEvaluationCubit.requestClinicEvaluation!.temperatura != null) {
      setState(() {
        tempFormatter = MaskTextInputFormatter(
            mask: '##.#',
            initialText:
                '${clinicEvaluationCubit.requestClinicEvaluation!.temperatura}',
            filter: {
              "#": RegExp(r'[0-9]'),
            });
      });
    }
  }

  String? verifyRequired(value) {
    if (rhythmSelected != 'A') {
      if (value == null || value.isEmpty)
        return I18nHelper.translate(
            context, 'filedsValidatorsWarnings.required');
      else
        return null;
    } else {
      return null;
    }
  }

  String? verifyTemp(value) {
    final double temperatureMin = context
        .read<AuthCubit>()
        .configAppAmbulanciaConstants
        .cardiovascular
        .temperatureMin;
    if (value != null && value.isNotEmpty) {
      if (double.parse(value) < temperatureMin) {
        return 'min:$temperatureMin';
      } else {
        return null;
      }
    } else {
      return null;
    }
  }
}
