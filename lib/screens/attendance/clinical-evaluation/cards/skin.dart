import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/models/clinic-evaluation.model.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-card.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-check.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class Skin extends StatefulWidget {
  final ResponseClinicEvaluation clinicEvaluation;
  final backgroundColor;
  final textColor;
  final Function? emmitChangedForParent;
  Skin({
    Key? key,
    required this.clinicEvaluation,
    this.backgroundColor = AmbulanceColors.beige,
    this.textColor = AmbulanceColors.greenDark,
    this.emmitChangedForParent,
  }) : super(key: key);

  @override
  _SkinState createState() => _SkinState();
}

class _SkinState extends State<Skin> {
  late List<bool> checkValues;
  final _baseTranslate = 'evaluationClinicalForm.skin';
  @override
  void initState() {
    checkValues = List<bool>.filled(widget.clinicEvaluation.pele.length, false);
    super.initState();
  }

  bool checkCurrentValueIput(index) {
    return checkValues[index];
  }

  @override
  Widget build(BuildContext context) {
    return ClincalCard(
      title: I18nHelper.translate(context, '$_baseTranslate.title'),
      cardColor: widget.backgroundColor,
      textColor: widget.textColor,
      child: GridView.builder(
        primary: false,
        shrinkWrap: true,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 3,
        ),
        itemCount: widget.clinicEvaluation.pele.length,
        itemBuilder: (context, index) {
          return ClinicalInputCheck(
            itemIndex: index,
            checkParentValue: checkCurrentValueIput,
            label: widget.clinicEvaluation.pele[index].descricao,
            labelColor: widget.textColor,
            checkColor: widget.textColor,
            forceValue: BlocProvider.of<ClinicEvaluationCubit>(context)
                    .requestClinicEvaluation!
                    .pele ==
                widget.clinicEvaluation.pele[index].codigo,
            onPresss: (isChecked) {
              final length = widget.clinicEvaluation.pele.length;
              setState(() {
                // disable all others in view
                for (int i = 0; i < length; ++i) {
                  checkValues[i] = false;
                }
                checkValues[index] = isChecked!;

                BlocProvider.of<ClinicEvaluationCubit>(context)
                        .requestClinicEvaluation!
                        .pele =
                    isChecked
                        ? widget.clinicEvaluation.pele[index].codigo
                        : null;

                widget.emmitChangedForParent!();
              });
            },
          );
        },
      ),
    );
  }
}
