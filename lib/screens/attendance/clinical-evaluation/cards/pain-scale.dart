import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-check.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

enum PainLevel {
  none,
  mild,
  moderate,
  intense,
  maximum,
}

class PainScaleData {
  final int? selectedPainLevel;
  final List<String> selectedPainLocations;
  final String? otherPainLocation;

  PainScaleData({
    this.selectedPainLevel,
    required this.selectedPainLocations,
    this.otherPainLocation,
  });

  bool isValid() {
    return selectedPainLevel != null;
  }

  @override
  String toString() {
    return '(selectedPainLevel: $selectedPainLevel, selectedPainLocations: $selectedPainLocations, otherPainLocation: $otherPainLocation)';
  }
}

class PainScaleCard extends StatefulWidget {
  final ValueChanged<PainScaleData>? onChanged;
  final backgroundColor;
  final Color textColor;

  final Function? emmitChangedForParent;

  const PainScaleCard({
    super.key,
    this.onChanged,
    this.textColor = Colors.black,
    this.emmitChangedForParent,
    this.backgroundColor = AmbulanceColors.grayLight3,
  });

  @override
  State<PainScaleCard> createState() => _PainScaleCardState();
}

class _PainScaleCardState extends State<PainScaleCard> {
  // Constantes
  static const double _spacing = 8.0;
  static const double _dividerHeight = 24.0;
  static const double _largeSpacing = 24.0;
  static const double _titleFontSize = 20.0;

  int? _selectedPainLevel;
  final TextEditingController _intensityController = TextEditingController();
  final TextEditingController _otherPainLocationController =
      TextEditingController();

  final Map<String, bool> _painLocations = {
    'CABEÇA': false,
    'BARRIGA': false,
    'MEMBROS INFERIORES': false,
    'LOMBAR': false,
    'TÓRAX OU PEITO': false,
    'MEMBROS SUPERIORES': false,
    'DORSO': false,
    'PESCOÇO OU CERVICAL': false,
    'OUTROS': false,
  };

  final Map<int, String> _painLevelDescriptions = {
    0: 'SEM DOR',
    2: 'DOR LIGEIRA',
    5: 'DOR MODERADA',
    8: 'DOR INTENSA',
    10: 'DOR MÁXIMA',
  };

  bool _showOtherLocationField = false;

  @override
  void initState() {
    super.initState();
    _initializePainScale();
  }

  void _updateIntensityText(int level) {
    _intensityController.text = _painLevelDescriptions[level] ?? 'Nível $level';
  }

  void _initializePainScale() {
    // Atualizar o texto de intensidade se um nível de dor foi selecionado
    if (_selectedPainLevel != null) {
      _updateIntensityText(_selectedPainLevel!);
    }

    // Verificar se há dados no Cubit e carregar se disponíveis
    final clinicEvaluationCubit =
        BlocProvider.of<ClinicEvaluationCubit>(context);
    if (clinicEvaluationCubit.requestClinicEvaluation != null) {
      final request = clinicEvaluationCubit.requestClinicEvaluation!;

      // Carregar intensidade de dor
      if (request.intensidadeDor != null && _selectedPainLevel == null) {
        _selectedPainLevel = request.intensidadeDor;
        _updateIntensityText(_selectedPainLevel!);
      }

      // Carregar localizações de dor
      if (request.dorCabeca == 'S') _painLocations['CABEÇA'] = true;
      if (request.dorBarriga == 'S') _painLocations['BARRIGA'] = true;
      if (request.dorMembrosSup == 'S')
        _painLocations['MEMBROS SUPERIORES'] = true;
      if (request.dorMembrosInf == 'S')
        _painLocations['MEMBROS INFERIORES'] = true;
      if (request.dorLombar == 'S') _painLocations['LOMBAR'] = true;
      if (request.dorToraxPeito == 'S') _painLocations['TÓRAX OU PEITO'] = true;
      if (request.dorDorso == 'S') _painLocations['DORSO'] = true;
      if (request.dorPescocoCervical == 'S')
        _painLocations['PESCOÇO OU CERVICAL'] = true;

      // Carregar outros locais de dor
      if (request.dorOutros == 'S') {
        _painLocations['OUTROS'] = true;
        _showOtherLocationField = true;

        if (request.obsDorOutros != null && request.obsDorOutros!.isNotEmpty) {
          _otherPainLocationController.text = request.obsDorOutros!;
        }
      }
    }
  }

  @override
  void dispose() {
    _intensityController.dispose();
    _otherPainLocationController.removeListener(_emitChanges);
    _otherPainLocationController.dispose();
    super.dispose();
  }

  void _emitChanges() {
    final List<String> selectedLocations = _painLocations.entries
        .where((entry) => entry.value)
        .map((entry) => entry.key)
        .toList();

    final painScaleData = PainScaleData(
      selectedPainLevel: _selectedPainLevel,
      selectedPainLocations: selectedLocations,
      otherPainLocation: _showOtherLocationField &&
              _otherPainLocationController.text.isNotEmpty
          ? _otherPainLocationController.text
          : null,
    );

    final clinicEvaluationCubit =
        BlocProvider.of<ClinicEvaluationCubit>(context);

    // Atualizar a escala de dor e intensidade
    if (_selectedPainLevel != null) {
      clinicEvaluationCubit.requestClinicEvaluation!.intensidadeDor =
          _selectedPainLevel;
      clinicEvaluationCubit.requestClinicEvaluation!.escalaDor =
          _painLevelDescriptions[_selectedPainLevel];
    }

    // Atualizar as localizações da dor
    clinicEvaluationCubit.requestClinicEvaluation!.dorCabeca =
        _painLocations['CABEÇA'] == true ? 'S' : 'N';
    clinicEvaluationCubit.requestClinicEvaluation!.dorBarriga =
        _painLocations['BARRIGA'] == true ? 'S' : 'N';
    clinicEvaluationCubit.requestClinicEvaluation!.dorMembrosSup =
        _painLocations['MEMBROS SUPERIORES'] == true ? 'S' : 'N';
    clinicEvaluationCubit.requestClinicEvaluation!.dorMembrosInf =
        _painLocations['MEMBROS INFERIORES'] == true ? 'S' : 'N';
    clinicEvaluationCubit.requestClinicEvaluation!.dorLombar =
        _painLocations['LOMBAR'] == true ? 'S' : 'N';
    clinicEvaluationCubit.requestClinicEvaluation!.dorToraxPeito =
        _painLocations['TÓRAX OU PEITO'] == true ? 'S' : 'N';
    clinicEvaluationCubit.requestClinicEvaluation!.dorDorso =
        _painLocations['DORSO'] == true ? 'S' : 'N';
    clinicEvaluationCubit.requestClinicEvaluation!.dorPescocoCervical =
        _painLocations['PESCOÇO OU CERVICAL'] == true ? 'S' : 'N';
    clinicEvaluationCubit.requestClinicEvaluation!.dorOutros =
        _painLocations['OUTROS'] == true ? 'S' : 'N';

    // Atualizar a observação de outros locais de dor
    if (_painLocations['OUTROS'] == true &&
        _otherPainLocationController.text.isNotEmpty) {
      clinicEvaluationCubit.requestClinicEvaluation!.obsDorOutros =
          _otherPainLocationController.text;
    } else {
      clinicEvaluationCubit.requestClinicEvaluation!.obsDorOutros = null;
    }

    widget.onChanged?.call(painScaleData);

    if (widget.emmitChangedForParent != null) {
      widget.emmitChangedForParent!();
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorGreen = Colors.green.shade700;
    final colorYellow = Colors.yellow.shade600;
    final colorOrange = Colors.orange.shade700;
    final colorRed = Colors.red.shade700;

    return Card(
      color: widget.backgroundColor,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ConstantsTheme.padding)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(colorGreen),
            const Divider(
              color: Colors.white,
              thickness: 1,
              height: _dividerHeight,
            ),
            _buildPainScaleTitle(colorGreen),
            const SizedBox(height: _spacing),
            _buildPainScaleVisuals(
                colorGreen, colorYellow, colorOrange, colorRed),
            const SizedBox(height: _largeSpacing),
            _buildPainScaleOptions(),
            const SizedBox(height: _largeSpacing),
            _buildIntensityField(widget.textColor),
            const SizedBox(height: _largeSpacing),
            _buildPainLocationsSection(),
          ],
        ),
      ),
    );
  }

  // Widget para o cabeçalho
  Widget _buildHeader(Color colorGreen) {
    return Center(
      child: Text(
        'SINAIS VITAIS',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: _titleFontSize,
          color: Colors.white,
        ),
      ),
    );
  }

  // Widget para o título da escala de dor
  Widget _buildPainScaleTitle(Color colorGreen) {
    return Text(
      'ESCALA DE DOR',
      style: TextStyle(
        fontWeight: FontWeight.bold,
        fontSize: _titleFontSize,
        color: Colors.white,
      ),
    );
  }

  // Widget para o campo de intensidade
  Widget _buildIntensityField(Color textColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'INTENSIDADE:',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
        ),
        const SizedBox(height: _spacing),
        Container(
          width: MediaQuery.of(context).size.width * 0.8,
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.fromBorderSide(
              BorderSide(
                color: textColor,
                width: 1.4,
              ),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: TextFormField(
            controller: _intensityController,
            autovalidateMode: AutovalidateMode.always,
            inputFormatters: [],
            decoration: InputDecoration.collapsed(
              hintText: 'SELECIONE NA ESCALA',
              hintStyle: TextStyle(color: Colors.white),
            ),
            readOnly: true,
            style: TextStyle(color: Colors.black),
          ),
        ),
      ],
    );
  }

  // Widget para a seção de localizações de dor
  Widget _buildPainLocationsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'LOCALIZAÇÃO DA DOR:',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: widget.textColor,
          ),
        ),
        const SizedBox(height: _spacing),
        GridView.builder(
          primary: false,
          shrinkWrap: true,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            childAspectRatio: 3,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: _painLocations.length,
          itemBuilder: (context, index) {
            final entry = _painLocations.entries.elementAt(index);
            final bool isOtherOption = entry.key == 'OUTROS';

            return ClinicalInputCheck(
              label: entry.key,
              labelColor: widget.textColor,
              checkColor: widget.backgroundColor,
              forceValue: entry.value,
              onPresss: (isChecked) {
                setState(() {
                  _painLocations[entry.key] = isChecked!;

                  // Se for a opção "OUTRO", controlar a visibilidade do campo de texto
                  if (isOtherOption) {
                    _showOtherLocationField = isChecked;

                    // Se desmarcar, limpar o campo
                    if (!isChecked) {
                      _otherPainLocationController.clear();
                    }
                  }
                });
                _emitChanges();
              },
            );
          },
        ),

        // Campo de texto para "OUTRO" que aparece apenas quando o checkbox está marcado
        if (_showOtherLocationField)
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.fromBorderSide(
                  BorderSide(
                    color: widget.textColor,
                    width: 1.4,
                  ),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextFormField(
                autovalidateMode: AutovalidateMode.always,
                inputFormatters: [],
                controller: _otherPainLocationController,
                keyboardType: TextInputType.text,
                decoration: InputDecoration.collapsed(
                  hintText: 'ESPECIFIQUE O LOCAL DA DOR',
                ),
                onChanged: (value) {
                  _emitChanges();
                },
              ),
            ),
          ),
      ],
    );
  }

  // Widget para as opções da escala de dor (faces, labels e radio buttons)
  Widget _buildPainScaleOptions() {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildPainOption(0, '☺️', 'SEM DOR'),
            _buildPainOption(2, '🙂', 'DOR LIGEIRA'),
            _buildPainOption(5, '😐', 'DOR MODERADA'),
            _buildPainOption(8, '😟', 'DOR INTENSA'),
            _buildPainOption(10, '😫', 'DOR MÁXIMA'),
          ],
        ),
      ],
    );
  }

  // Widget para cada opção de dor (emoji + label + radio button)
  Widget _buildPainOption(int value, String emoji, String label) {
    return Expanded(
      child: Column(
        children: [
          Text(
            emoji,
            style: const TextStyle(
              fontSize: 35,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: widget.textColor,
            ),
            textAlign: TextAlign.center,
          ),
          Transform.scale(
            scale: 1.5,
            child: Radio<int>(
              value: value,
              groupValue: _selectedPainLevel,
              onChanged: (newValue) {
                setState(() {
                  _selectedPainLevel = newValue;
                });
                if (newValue != null) {
                  _updateIntensityText(newValue);
                }
                _emitChanges();
              },
              fillColor: WidgetStateProperty.resolveWith<Color>(
                (Set<WidgetState> states) {
                  if (states.contains(WidgetState.selected)) {
                    return Colors.white; // preenchido quando selecionado
                  }
                  return Colors.white; // borda branca quando não selecionado
                },
              ),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      ),
    );
  }

  // Widget para os visuais da escala de dor
  Widget _buildPainScaleVisuals(
    Color colorGreen,
    Color colorYellow,
    Color colorOrange,
    Color colorRed,
  ) {
    return Container(
      height: 30,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(15),
        gradient: LinearGradient(
          colors: [
            colorGreen,
            colorYellow,
            colorOrange,
            colorRed,
          ],
          stops: const [0.0, 0.3, 0.6, 1.0],
        ),
      ),
    );
  }
}
