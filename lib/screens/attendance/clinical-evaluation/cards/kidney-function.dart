import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/models/clinic-evaluation.model.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-card.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-text.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-check.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-radio-row.dart';
import 'package:ambulancia_app/shared/formatters.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class KidneyFunction extends StatefulWidget {
  final ResponseClinicEvaluation clinicEvaluation;
  final backgroundColor;
  final textColor;
  final emmitChangedForParent;
  KidneyFunction({
    Key? key,
    required this.clinicEvaluation,
    this.backgroundColor = AmbulanceColors.beige,
    this.textColor = AmbulanceColors.greenDark,
    this.emmitChangedForParent,
  }) : super(key: key);

  @override
  _KidneyFunctionState createState() => _KidneyFunctionState();
}

class _KidneyFunctionState extends State<KidneyFunction> {
  bool showDiuresisOptions = false;
  bool diuresisPresentCheck = false;
  bool diuresisAbsentCheck = false;
  final _baseTranslate = 'evaluationClinicalForm.kidneyFunction';
  String? diuresisType;

  RequestClinicEvaluation? requestClinicEvaluation;

  TextEditingController? textVelocidadeDifusao = TextEditingController();

  @override
  void initState() {
    super.initState();

    requestClinicEvaluation =
        BlocProvider.of<ClinicEvaluationCubit>(context).requestClinicEvaluation;

    diuresisType = requestClinicEvaluation!.diurese;

    textVelocidadeDifusao!.text =
        requestClinicEvaluation!.diluicaoVelocidadeInfusao?.toString() ?? '';

    showDiuresisOptions = requestClinicEvaluation!.funcaoRenalDiurese == 'S';
  }

  @override
  Widget build(BuildContext context) {
    return ClincalCard(
      title: I18nHelper.translate(context, '$_baseTranslate.title'),
      cardColor: widget.backgroundColor,
      textColor: widget.textColor,
      child: Column(
        children: [
          GridView.builder(
            primary: false,
            shrinkWrap: true,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 3,
            ),
            itemCount: widget.clinicEvaluation.funacaoRenal.length,
            itemBuilder: (context, index) {
              return ClinicalInputCheck(
                label: widget.clinicEvaluation.funacaoRenal[index].descricao,
                labelColor: widget.textColor,
                checkColor: widget.backgroundColor,
                forceValue: _getValue(
                    widget.clinicEvaluation.funacaoRenal[index].codigo),
                onPresss: (isChecked) {
                  final code =
                      widget.clinicEvaluation.funacaoRenal[index].codigo;
                  setState(() {
                    if (code == CEKidneyFunctionCode.Diuresis) {
                      BlocProvider.of<ClinicEvaluationCubit>(context)
                          .requestClinicEvaluation!
                          .funcaoRenalDiurese = isChecked! ? 'S' : 'N';
                      showDiuresisOptions = isChecked;
                      if (showDiuresisOptions == false) {
                        diuresisType = null;
                        BlocProvider.of<ClinicEvaluationCubit>(context)
                            .requestClinicEvaluation!
                            .diurese = null;
                        BlocProvider.of<ClinicEvaluationCubit>(context)
                            .requestClinicEvaluation!
                            .diluicaoVelocidadeInfusao = null;
                        textVelocidadeDifusao = null;
                      }
                    } else if (code == CEKidneyFunctionCode.Hemodialysis) {
                      BlocProvider.of<ClinicEvaluationCubit>(context)
                          .requestClinicEvaluation!
                          .funcaoRenalHemodialise = isChecked! ? 'S' : 'N';
                    } else if (code == CEKidneyFunctionCode.Normal) {
                      BlocProvider.of<ClinicEvaluationCubit>(context)
                          .requestClinicEvaluation!
                          .funcaoRenalNormal = isChecked! ? 'S' : 'N';
                    }
                  });
                  widget.emmitChangedForParent();
                },
              );
            },
          ),
          if (showDiuresisOptions) diureseOptions()
        ],
      ),
    );
  }

  Widget diureseOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Divider(
          height: 15,
          thickness: 1.3,
          color: widget.textColor,
        ),
        Padding(
          padding: const EdgeInsets.only(left: 15, top: 10),
          child: Text(
            I18nHelper.translate(context, '$_baseTranslate.diuresis'),
            style: TextStyle(
              color: widget.textColor,
            ),
          ),
        ),
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Flexible(
              child: ClinicalRadioRow<String>(
                value: 'P',
                groupValue: diuresisType,
                text: I18nHelper.translate(context, '$_baseTranslate.present'),
                textColor: widget.textColor,
                onChanged: (value) => setState(() {
                  diuresisType = value;
                  BlocProvider.of<ClinicEvaluationCubit>(context)
                      .requestClinicEvaluation!
                      .diurese = value;
                  widget.emmitChangedForParent();
                }),
              ),
            ),
            Flexible(
              child: ClinicalRadioRow<String>(
                value: 'A',
                groupValue: diuresisType,
                text: I18nHelper.translate(context, '$_baseTranslate.absent'),
                textColor: widget.textColor,
                onChanged: (value) => setState(() {
                  diuresisType = value;
                  BlocProvider.of<ClinicEvaluationCubit>(context)
                      .requestClinicEvaluation!
                      .diurese = value;
                  widget.emmitChangedForParent();
                }),
              ),
            ),
            Expanded(
              child: ClinicalInputText(
                mesure: I18nHelper.translate(context, '$_baseTranslate.ml/h'),
                textColor: widget.textColor,
                controller: textVelocidadeDifusao,
                keyboardType: TextInputType.number,
                inputFormatters: FormatterField.inputNumberSuppliesFormatter,
                onChange: (value) {
                  BlocProvider.of<ClinicEvaluationCubit>(context)
                      .requestClinicEvaluation!
                      .diluicaoVelocidadeInfusao = int.tryParse(value);
                  widget.emmitChangedForParent();
                },
              ),
            ),
          ],
        )
      ],
    );
  }

  bool _getValue(String? code) {
    if (code == CEKidneyFunctionCode.Diuresis) {
      return BlocProvider.of<ClinicEvaluationCubit>(context)
              .requestClinicEvaluation!
              .funcaoRenalDiurese ==
          'S';
    } else if (code == CEKidneyFunctionCode.Hemodialysis) {
      return BlocProvider.of<ClinicEvaluationCubit>(context)
              .requestClinicEvaluation!
              .funcaoRenalHemodialise ==
          'S';
    } else if (code == CEKidneyFunctionCode.Normal) {
      return BlocProvider.of<ClinicEvaluationCubit>(context)
              .requestClinicEvaluation!
              .funcaoRenalNormal ==
          'S';
    }
    return false;
  }
}
