import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/models/clinic-evaluation.model.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-card.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-text.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-check.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SurgicalInformation extends StatefulWidget {
  final ResponseClinicEvaluation clinicEvaluation;
  final backgroundColor;
  final textColor;
  final Function? emmitChangedForParent;
  SurgicalInformation({
    Key? key,
    required this.clinicEvaluation,
    this.backgroundColor = AmbulanceColors.beige,
    this.textColor = AmbulanceColors.greenDark,
    this.emmitChangedForParent,
  }) : super(key: key);

  @override
  _SurgicalInformationState createState() => _SurgicalInformationState();
}

class _SurgicalInformationState extends State<SurgicalInformation> {
  int markeds = 0;
  TextEditingController textObservation = TextEditingController();
  bool showObservation = false;
  final _baseTranslate = 'evaluationClinicalForm.surgicalInformation';

  @override
  void initState() {
    super.initState();
    textObservation.text = BlocProvider.of<ClinicEvaluationCubit>(context)
            .requestClinicEvaluation!
            .observacaoAvaliacaoClinica
            ?.toString() ??
        '';
    _countMarkeds();
  }

  @override
  Widget build(BuildContext context) {
    return ClincalCard(
      title: I18nHelper.translate(context, '$_baseTranslate.title'),
      cardColor: widget.backgroundColor,
      textColor: widget.textColor,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GridView.builder(
            primary: false,
            shrinkWrap: true,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 3,
            ),
            itemCount: widget.clinicEvaluation.informacaoCirurgica.length,
            itemBuilder: (context, index) {
              return ClinicalInputCheck(
                label: widget
                    .clinicEvaluation.informacaoCirurgica[index].descricao,
                labelColor: widget.textColor,
                checkColor: widget.backgroundColor,
                forceValue: _getValue(
                    widget.clinicEvaluation.informacaoCirurgica[index].codigo),
                onPresss: (isChecked) {
                  final code =
                      widget.clinicEvaluation.informacaoCirurgica[index].codigo;
                  setState(() {
                    _updateData(code, !_getValue(code) ? 'S' : 'N');
                    if (_getValue(code)) {
                      markeds++;
                    } else
                      markeds--;
                  });
                  widget.emmitChangedForParent!();
                },
              );
            },
          ),
          if (markeds != 0)
            ClinicalInputText(
              title: I18nHelper.translate(context, '$_baseTranslate.comments'),
              textColor: widget.textColor,
              controller: textObservation,
              width: MediaQuery.of(context).size.width * 0.8,
              inputFormatters: [LengthLimitingTextInputFormatter(200)],
              onChange: (text) {
                BlocProvider.of<ClinicEvaluationCubit>(context)
                    .requestClinicEvaluation!
                    .observacaoAvaliacaoClinica = text;
                widget.emmitChangedForParent!();
              },
            )
        ],
      ),
    );
  }

  _updateData(String? code, String value) {
    RequestClinicEvaluation? requestClinicEvaluation =
        BlocProvider.of<ClinicEvaluationCubit>(context).requestClinicEvaluation;

    switch (code) {
      case "1":
        requestClinicEvaluation!.posOperatorio = value;
        break;
      case "2":
        requestClinicEvaluation!.feridaOperatoria = value;
        break;
      case "3":
        requestClinicEvaluation!.infeccaoFeridaOperatoria = value;
        break;
      case "4":
        requestClinicEvaluation!.drenos = value;
        break;
      case "5":
        requestClinicEvaluation!.estomas = value;
        break;
      case "6":
        requestClinicEvaluation!.examesLaboratoriais = value;
        break;
      case "7":
        requestClinicEvaluation!.examesImagem = value;
        break;
      default:
        break;
    }
  }

  bool _getValue(String? code) {
    RequestClinicEvaluation? requestClinicEvaluation =
        BlocProvider.of<ClinicEvaluationCubit>(context).requestClinicEvaluation;

    bool value = false;

    switch (code) {
      case "1":
        value = requestClinicEvaluation!.posOperatorio == 'S';
        break;
      case "2":
        value = requestClinicEvaluation!.feridaOperatoria == 'S';
        break;
      case "3":
        value = requestClinicEvaluation!.infeccaoFeridaOperatoria == 'S';
        break;
      case "4":
        value = requestClinicEvaluation!.drenos == 'S';
        break;
      case "5":
        value = requestClinicEvaluation!.estomas == 'S';
        break;
      case "6":
        value = requestClinicEvaluation!.examesLaboratoriais == 'S';
        break;
      case "7":
        value = requestClinicEvaluation!.examesImagem == 'S';
        break;
      default:
        break;
    }
    return value;
  }

  _countMarkeds() {
    widget.clinicEvaluation.informacaoCirurgica.forEach((element) {
      if (_getValue(element.codigo)) markeds++;
    });
  }
}
