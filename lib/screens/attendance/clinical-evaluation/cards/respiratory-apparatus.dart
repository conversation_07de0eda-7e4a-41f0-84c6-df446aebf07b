import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/models/clinic-evaluation.model.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-card.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-text.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-check.dart';
import 'package:ambulancia_app/shared/formatters.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RespiratoryApparatus extends StatefulWidget {
  final ResponseClinicEvaluation? clinicEvaluation;
  final backgroundColor;
  final textColor;
  final emmitChangedForParent;

  RespiratoryApparatus({
    Key? key,
    required this.clinicEvaluation,
    this.backgroundColor = AmbulanceColors.beige,
    this.textColor = AmbulanceColors.greenDark,
    this.emmitChangedForParent,
  }) : super(key: key);

  @override
  _RespiratoryApparatusState createState() => _RespiratoryApparatusState();
}

class _RespiratoryApparatusState extends State<RespiratoryApparatus> {
  bool showDuration = false;
  RequestClinicEvaluation? requestClinicEvaluation;

  TextEditingController textDias = TextEditingController();
  TextEditingController textHoras = TextEditingController();

  final _baseTranslate = 'evaluationClinicalForm.respiratoryApparatus';

  @override
  void initState() {
    super.initState();

    requestClinicEvaluation =
        BlocProvider.of<ClinicEvaluationCubit>(context).requestClinicEvaluation;

    verifyField();

    showDuration = requestClinicEvaluation!.ventilacaoMecanica == 'S';

    debugPrint('${widget.clinicEvaluation!.viasAereasVentilacao}');
  }

  @override
  Widget build(BuildContext context) {
    return ClincalCard(
      title: I18nHelper.translate(context, '$_baseTranslate.title'),
      cardColor: widget.backgroundColor,
      textColor: widget.textColor,
      child: Column(
        children: [
          Align(
            alignment: Alignment.topLeft,
            child: Text(
              I18nHelper.translate(context, '$_baseTranslate.subTitle'),
              style: TextStyle(
                fontSize: 15,
                color: widget.textColor,
              ),
            ),
          ),
          SizedBox(height: 10),
          GridView.builder(
            primary: false,
            shrinkWrap: true,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              childAspectRatio: 3,
            ),
            itemCount: widget.clinicEvaluation!.viasAereasVentilacao.length,
            itemBuilder: (context, index) {
              return ClinicalInputCheck(
                key: Key('clinical_inputCheck_$index'),
                label: widget
                    .clinicEvaluation!.viasAereasVentilacao[index].descricao,
                labelColor: widget.textColor,
                checkColor: widget.backgroundColor,
                forceValue: _getValue(widget
                    .clinicEvaluation!.viasAereasVentilacao[index].codigo),
                onPresss: (isChecked) {
                  final code = widget
                      .clinicEvaluation!.viasAereasVentilacao[index].codigo;
                  setState(() {
                    _updateData(code, !_getValue(code) ? 'S' : 'N');

                    if (code ==
                        CEAirwaysVentilationCode.MechanicalVentilation) {
                      showDuration = isChecked!;
                      textDias.text = "";
                      textHoras.text = "";
                    }
                  });
                  widget.emmitChangedForParent();
                },
              );
            },
          ),
          if (showDuration) _duration()
        ],
      ),
    );
  }

  Widget _duration() {
    return Column(
      children: [
        Divider(
          height: 15,
          thickness: 1.3,
          color: widget.textColor,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Flexible(
              flex: 4,
              child: Padding(
                padding: const EdgeInsets.only(right: 30, top: 15),
                child: Text(
                  I18nHelper.translate(
                      context, '$_baseTranslate.durationVentilation'),
                  style: TextStyle(
                    color: widget.textColor,
                    fontSize: 20,
                  ),
                ),
              ),
            ),
            Flexible(
              flex: 1,
              child: ClinicalInputText(
                title: I18nHelper.translate(context, '$_baseTranslate.days'),
                textColor: widget.textColor,
                keyboardType: TextInputType.number,
                inputFormatters: FormatterField.inputNumberSuppliesFormatter,
                controller: textDias,
                onChange: (value) {
                  requestClinicEvaluation!.duracaoVentilacaoQtdeDias =
                      int.tryParse(value);
                },
              ),
            ),
            Flexible(
              flex: 2,
              child: ClinicalInputText(
                title: I18nHelper.translate(context, '$_baseTranslate.hours'),
                textColor: widget.textColor,
                controller: textHoras,
                keyboardType: TextInputType.number,
                inputFormatters: [
                  ...FormatterField.simpleNumberFormatter,
                  LengthLimitingTextInputFormatter(2)
                ],
                onChange: (value) {
                  requestClinicEvaluation!.duracaoVentilacaoQtdeHorasDia =
                      int.tryParse(value);
                },
              ),
            )
          ],
        ),
      ],
    );
  }

  _updateData(String? code, String value) {
    if (code == CEAirwaysVentilationCode.TrachealIntubation) {
      requestClinicEvaluation!.intubacaoTraqueal = value;
    } else if (code == CEAirwaysVentilationCode.NasalCatheter) {
      requestClinicEvaluation!.cateterNasal = value;
    } else if (code == CEAirwaysVentilationCode.AmbienteAr) {
      requestClinicEvaluation!.arAmbiente = value;
    } else if (code == CEAirwaysVentilationCode.VenturiMask) {
      requestClinicEvaluation!.mascaraVenturi = value;
    } else if (code == CEAirwaysVentilationCode.OxygenMask) {
      requestClinicEvaluation!.oxigenioPorMascara = value;
    } else if (code == CEAirwaysVentilationCode.Tracheostomy) {
      requestClinicEvaluation!.traqueostomia = value;
    } else if (code == CEAirwaysVentilationCode.MechanicalVentilation) {
      requestClinicEvaluation!.ventilacaoMecanica = value;
    }
  }

  bool _getValue(String? code) {
    if (code == CEAirwaysVentilationCode.TrachealIntubation) {
      return requestClinicEvaluation!.intubacaoTraqueal == 'S';
    } else if (code == CEAirwaysVentilationCode.NasalCatheter) {
      return requestClinicEvaluation!.cateterNasal == 'S';
    } else if (code == CEAirwaysVentilationCode.AmbienteAr) {
      return requestClinicEvaluation!.arAmbiente == 'S';
    } else if (code == CEAirwaysVentilationCode.VenturiMask) {
      return requestClinicEvaluation!.mascaraVenturi == 'S';
    } else if (code == CEAirwaysVentilationCode.OxygenMask) {
      return requestClinicEvaluation!.oxigenioPorMascara == 'S';
    } else if (code == CEAirwaysVentilationCode.Tracheostomy) {
      return requestClinicEvaluation!.traqueostomia == 'S';
    } else if (code == CEAirwaysVentilationCode.MechanicalVentilation) {
      return requestClinicEvaluation!.ventilacaoMecanica == 'S';
    }
    return false;
  }

  void verifyField() {
    textDias.text =
        requestClinicEvaluation!.duracaoVentilacaoQtdeDias?.toString() ?? '';

    textHoras.text =
        requestClinicEvaluation!.duracaoVentilacaoQtdeHorasDia?.toString() ??
            '';
  }

  String? verifyRequired(value) {
    if (value == null || value.isEmpty)
      return I18nHelper.translate(context, 'filedsValidatorsWarnings.required');
    else
      return null;
  }
}
