import 'package:ambulancia_app/bloc/clinic-evaluation/clinic_evaluation_cubit.dart';
import 'package:ambulancia_app/models/clinic-evaluation.model.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-card.dart';
import 'package:ambulancia_app/screens/attendance/clinical-evaluation/widgets/clinical-input-check.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class GastrointestinalSystem extends StatefulWidget {
  final ResponseClinicEvaluation clinicEvaluation;
  final backgroundColor;
  final textColor;
  final Function? emmitChangedForParent;
  GastrointestinalSystem({
    Key? key,
    required this.clinicEvaluation,
    this.backgroundColor = AmbulanceColors.beige,
    this.textColor = AmbulanceColors.greenDark,
    this.emmitChangedForParent,
  }) : super(key: key);

  @override
  _GastrointestinalSystemState createState() => _GastrointestinalSystemState();
}

class _GastrointestinalSystemState extends State<GastrointestinalSystem> {
  late List<bool> checkValues;
  final _baseTranslate = 'evaluationClinicalForm.gastrointestinalSystem';
  @override
  void initState() {
    checkValues = List<bool>.filled(
        widget.clinicEvaluation.sistemaGastrointestinal.length, false);
    super.initState();
  }

  bool checkCurrentValueIput(index) {
    return checkValues[index];
  }

  @override
  Widget build(BuildContext context) {
    return ClincalCard(
      title: I18nHelper.translate(context, '$_baseTranslate.title'),
      cardColor: widget.backgroundColor,
      textColor: widget.textColor,
      child: GridView.builder(
        primary: false,
        shrinkWrap: true,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 3,
        ),
        itemCount: widget.clinicEvaluation.sistemaGastrointestinal.length,
        itemBuilder: (context, index) {
          return ClinicalInputCheck(
            itemIndex: index,
            checkParentValue: checkCurrentValueIput,
            label: widget
                .clinicEvaluation.sistemaGastrointestinal[index].descricao,
            labelColor: widget.textColor,
            checkColor: widget.backgroundColor,
            forceValue: BlocProvider.of<ClinicEvaluationCubit>(context)
                    .requestClinicEvaluation!
                    .sistemaGastrointestinal ==
                widget.clinicEvaluation.sistemaGastrointestinal[index].codigo,
            onPresss: (isChecked) {
              final length =
                  widget.clinicEvaluation.sistemaGastrointestinal.length;

              setState(() {
                // disable all others in view
                for (int i = 0; i < length; ++i) checkValues[i] = false;
                checkValues[index] = isChecked!;
                BlocProvider.of<ClinicEvaluationCubit>(context)
                        .requestClinicEvaluation!
                        .sistemaGastrointestinal =
                    isChecked
                        ? widget.clinicEvaluation.sistemaGastrointestinal[index]
                            .codigo
                        : null;
              });
              widget.emmitChangedForParent!();
            },
          );
        },
      ),
    );
  }
}
