import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:ambulancia_app/bloc/attendance/subscription/cubit/subscription_cubit.dart';
import 'package:ambulancia_app/bloc/signature_pad/signature_cubit.dart';
import 'package:ambulancia_app/bloc/signature_pad/signature_state.dart';
import 'package:ambulancia_app/shared/api/signature.api.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/utils/photo.constants.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/shared/widgets/only_recognizer.dart';
import 'package:ambulancia_app/shared/widgets/text_field_custom.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_signature_pad/flutter_signature_pad.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class SignaturePadScreen extends StatefulWidget {
  final serviceNumber;
  final status;

  SignaturePadScreen({Key? key, this.serviceNumber, this.status})
      : super(key: key);

  @override
  _SignaturePadScreenState createState() => _SignaturePadScreenState();
}

class _SignaturePadScreenState extends State<SignaturePadScreen> {
  final service = Locator.instance.get<SignatureApi>();
  ByteData _img = ByteData(0);
  var color = Colors.red;
  var strokeWidth = 5.0;
  final _sign = GlobalKey<SignatureState>();
  int _signInterect = 0;
  String _pathSignatureFile = "";
  final _baseController = 'signaturePad';

  final GlobalKey<FormState> _formSignature = GlobalKey<FormState>();

  TextEditingController _nameClient = TextEditingController();
  TextEditingController _cpfOrRgOrCrmClient = TextEditingController();

  final _baseTranslate = 'attendanceScreen.menu.regulation.content';

  @override
  void initState() {
    super.initState();
    _nameClient.addListener(_validateForm);
    _cpfOrRgOrCrmClient.addListener(_validateForm);
  }

  @override
  void dispose() {
    _nameClient.dispose();
    _cpfOrRgOrCrmClient.dispose();
    super.dispose();
  }

  void _validateForm() {
    setState(() {
      _formSignature.currentState!.validate();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: <Widget>[
        Form(
          key: _formSignature,
          autovalidateMode: AutovalidateMode.always,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              TextFieldCustom(
                controller: _nameClient,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Campo obrigatório.';
                  }
                  if (value.replaceAll(' ', '').length < 3) {
                    return 'O campo deve ter no mínimo 3 caracteres.';
                  }
                  if (value.length > 32) {
                    return 'Campo inválido, o campo deve ter no máximo 32 caracteres.';
                  }
                  if (value.trim().isEmpty) {
                    return 'O campo está vazio.';
                  }

                  return null;
                },
                title: 'Adicione o nome e o sobrenome do assinante *',
                label: 'Ex: João Lucas',
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp("[a-zA-ZÀ-ú ]")),
                ],
              ),
              SizedBox(
                height: 10,
              ),
              TextFieldCustom(
                controller: _cpfOrRgOrCrmClient,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Campo obrigatório.';
                  }
                  if (value.replaceAll(' ', '').length < 3) {
                    return 'O campo deve ter no mínimo 3 caracteres.';
                  }
                  if (value.length > 14) {
                    return 'Campo inválido, o campo deve ter no máximo 14 caracteres.';
                  }
                  return null;
                },
                inputFormatters: [
                  FilteringTextInputFormatter.allow(
                    RegExp('[0-9]'),
                  )
                ],
                title: 'Adicione o CPF, RG, ou o CRM do assinante *',
                label: 'Ex: 000000000000',
              ),
            ],
          ),
        ),
        SizedBox(
          height: 10,
        ),
        Text(
          "${I18nHelper.translate(context, '$_baseTranslate.signInSpace')}:",
        ),
        SizedBox(
          height: 10,
        ),
        Expanded(
          child: BlocBuilder<SignatureCubit, SignaturePadState>(
            buildWhen: (previous, state) {
              if (state is SuccessSendSignature) {
                context
                    .read<SubscriptionCubit>()
                    .verifySubscription(widget.serviceNumber, 0);
                _alertSucess(state.message);
              } else if (state is ErrorSendSignaturePadState) {
                _alertError(state.message);
              }
              return true;
            },
            builder: (context, state) {
              if (state is LoadingSendSignature) {
                return SpinKitCircle(
                  color: AmbulanceColors.green,
                );
              }

              return Container(
                key: ValueKey("signature"),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: OnlyOnePointerRecognizerWidget(
                    child: Signature(
                      color: color,
                      key: _sign,
                      onSign: () {
                        final SignatureState? sign = _sign.currentState;
                        if (sign != null) {
                          if (sign.points.length > 0) {
                          this.setState(() {
                            _signInterect = sign.points.length;
                          });
                        }
                        }
                      },
                      backgroundPainter: _WatermarkPaint("2.0", "2.0"),
                      strokeWidth: strokeWidth,
                    ),
                  ),
                ),
                color: Colors.black12,
              );
            },
          ),
        ),
        _img.buffer.lengthInBytes == 0
            ? Container()
            : Column(
                children: [
                  _pathSignatureFile != ""
                      ? SizedBox(
                          width: 200,
                          child: Image.file(File(_pathSignatureFile)))
                      : Container(),
                ],
              ),
        Container(
          width: double.infinity,
          margin: EdgeInsets.only(top: 15),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              BlocListener<SignatureCubit, SignaturePadState>(
                listener: (previous, state) {
                  if (state is LoadingSendSignature) {
                    this.setState(() {
                      _signInterect = 0;
                    });
                  }
                },
                child: TextButton(
                    style: TextButton.styleFrom(
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 54, vertical: 20),
                        backgroundColor: AmbulanceColors.acceptColor),
                    onPressed: _signInterect != 0 &&
                            _formSignature.currentState!.validate() == true
                        ? () async {
                            if (_formSignature.currentState!.validate()) {
                              final SignatureState? sign = _sign.currentState;
                             if(sign != null){
                               final image = await sign.getData();
                              var data = await (image.toByteData(
                                  format: ui.ImageByteFormat.png));
                              final encoded =
                                  base64.encode(data!.buffer.asUint8List());
                              this.setState(
                                () {
                                  _img = data;
                                  service.saveImage(
                                      base64String: encoded,
                                      serviceNumber: widget.serviceNumber);
                                  service
                                      .getFilePath(widget.serviceNumber)
                                      .then(
                                    (value) {
                                      _pathSignatureFile = value;
                                      Timer(
                                        Duration(milliseconds: 100),
                                        () {
                                          context
                                              .read<SignatureCubit>()
                                              .saveSignature(
                                                serviceNumber: widget
                                                    .serviceNumber
                                                    .toString(),
                                                signatureClient:
                                                    _pathSignatureFile,
                                                reasonAnnexPhoto:
                                                    photoReasonAnnexEnum
                                                        .assinatura.value,
                                                cpfOrRgOrCrmClient:
                                                    _cpfOrRgOrCrmClient.text,
                                                nameClient:
                                                    _nameClient.text.trim(),
                                              );
                                        },
                                      );
                                    },
                                  );
                                },
                              );
                             }
                            }
                          }
                        : null,
                    child: Text(
                      I18nHelper.translate(context, '$_baseController.save'),
                      style: TextStyle(fontSize: 22),
                    )),
              ),
              TextButton(
                  style: TextButton.styleFrom(
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 54, vertical: 20),
                      backgroundColor: AmbulanceColors.redClose),
                  onPressed: () {


                  final SignatureState? sign = _sign.currentState;
                  if(sign != null){
                      sign.clear();
                  }

                    if (_signInterect != 0) {
                      setState(() {
                        _img = ByteData(0);
                        _signInterect = 0;
                      });
                    } else {
                      Navigator.pop(context);
                    }
                  },
                  child: Text(
                    _signInterect == 0
                        ? I18nHelper.translate(
                            context, '$_baseController.close')
                        : I18nHelper.translate(
                            context, '$_baseController.clean'),
                    style: TextStyle(fontSize: 22),
                  )),
            ],
          ),
        )
      ],
    );
  }

  void _alertSucess(text) {
    Alert.open(context,
        title: I18nHelper.translate(
            context, '$_baseController.warnings.titleSucess'),
        text: text,
        callbackClose: () => {
              context
                  .read<SubscriptionCubit>()
                  .verifySubscription(widget.serviceNumber, widget.status),
              Navigator.pop(context)
            });
  }

  void _alertError(text) {
    Alert.open(context,
        title: I18nHelper.translate(
            context, '$_baseController.warnings.titleFailed'),
        text: text,
        callbackClose: () => Navigator.pop(context));
  }
}

class _WatermarkPaint extends CustomPainter {
  final String price;
  final String watermark;

  _WatermarkPaint(this.price, this.watermark);

  @override
  void paint(ui.Canvas canvas, ui.Size size) {
    // canvas.drawCircle(Offset(size.width / 2, size.height / 2), 10.8,
    //     Paint()..color = Colors.blue);
  }

  @override
  bool shouldRepaint(_WatermarkPaint oldDelegate) {
    return oldDelegate != this;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is _WatermarkPaint &&
          runtimeType == other.runtimeType &&
          price == other.price &&
          watermark == other.watermark;

  @override
  int get hashCode => price.hashCode ^ watermark.hashCode;
}
