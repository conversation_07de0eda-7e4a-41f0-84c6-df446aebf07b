import 'package:ambulancia_app/bloc/splash_screen/cubit/splash_screen_cubit.dart';
import 'package:ambulancia_app/screens/login/main.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
// import 'package:ambulancia_app/screens/signature/main.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:splash_unimed/splash_unimed.dart';

class SplashScreen extends StatefulWidget {
  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark.copyWith(
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.dark));
      context
          .read<SplashScreenCubit>()
          .verifyContinuedAplication(context: context);
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.light,
      child: Scaffold(
          backgroundColor: AmbulanceColors.greenDark,
          body: BlocBuilder<SplashScreenCubit, SplashScreenState>(
            buildWhen: (previous, current) {
              if (current is InterruptedStartupSplashScreen) {
                Alert.open(context, text: current.message, callbackClose: () {
                  SystemNavigator.pop();
                });
              }
              return true;
            },
            builder: (context, state) {
              if (state is ContinuedStartupSplashScreen) {
                return SplashPage(
                  next: (context) => LoginScreen(),
                  backgroundColor: AmbulanceColors.greenDark,
                );
              } else {
                return SizedBox();
              }
            },
          )),
    );
  }
}
