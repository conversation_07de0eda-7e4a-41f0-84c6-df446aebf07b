import 'package:ambulancia_app/bloc/attendance_list/attendance_list_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/last-synchronization-dates/last_synchronization_dates_cubit.dart';
import 'package:ambulancia_app/bloc/team/team_cubit.dart';
import 'package:ambulancia_app/bloc/vehicle/vehicle_cubit.dart';
import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/utils/constants.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class TeamHearder extends StatefulWidget {
  @override
  _TeamHeaderState createState() => _TeamHeaderState();
}

class _TeamHeaderState extends State<TeamHearder> {
  bool connected = true;
  final _baseTranlaste = 'home.header.teamHeader';
  TextEditingController _medicalController = TextEditingController();
  TextEditingController _nursicalController = TextEditingController();

  late String codVeiculo;

  @override
  void initState() {
    codVeiculo =
        BlocProvider.of<VehicleCubit>(context).selectedVehicle.codVeiculo!;
    context.read<TeamCubit>().getTeamVehicle(
        codUnimed: Constants.COD_UNIMED, codVeiculo: codVeiculo);
    _medicalController.text = '';
    _nursicalController.text = '';
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(FocusNode());
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          ConstantsTheme.boxShadow,
        ],
      ),
      child: Container(
        padding: EdgeInsets.all(ConstantsTheme.doublePadding),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius),
        ),
        child: Column(
          children: [
            _card(),
            _textLastUpdateOfTheListing(),
          ],
        ),
      ),
    );
  }

  Widget _card() {
    return Container(
      padding: EdgeInsets.all(ConstantsTheme.doublePadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                I18nHelper.translate(context, '$_baseTranlaste.title'),
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.w800),
              ),
              IconButton(
                  onPressed: () {
                    context.read<TeamCubit>().getTeamVehicle(
                          codUnimed: Constants.COD_UNIMED,
                          codVeiculo: codVeiculo,
                        );
                  },
                  icon: Icon(
                    Icons.refresh,
                    size: 28,
                  )),
            ],
          ),
          SizedBox(height: 20),
          BlocBuilder<TeamCubit, TeamState>(
            builder: (context, state) {
              if (state is LoadingTeamState)
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [SpinKitThreeBounce(color: AmbulanceColors.green)],
                );
              else if (state is LoadedTeamState) {
                return _funcionarioLabel(
                    motoristaSocrorrista: state.motoristaSocrorrista,
                    tecnicoEnfermagem: state.tecnicoEnfermagem);
              } else if (state is ErrorTeamState) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Column(
                      children: [
                        IconButton(
                            icon: Icon(Icons.warning,
                                color: AmbulanceColors.greenDark),
                            iconSize: 40,
                            onPressed: () {}),
                        Text(
                          state.message,
                          style: TextStyle(fontSize: 18),
                        ),
                      ],
                    ),
                  ],
                );
              } else
                return Container();
            },
          )
        ],
      ),
    );
  }

  OutlineInputBorder inputBorder(Color borderColor) {
    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius),
      borderSide: BorderSide(color: borderColor),
    );
  }

  Widget _funcionarioLabel(
      {FuncionarioModel? motoristaSocrorrista,
      FuncionarioModel? tecnicoEnfermagem}) {
    final style = TextStyle(fontSize: 16);
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(I18nHelper.translate(context, '$_baseTranlaste.conductor')),
              Text(
                  motoristaSocrorrista?.nomeFuncionario ??
                      I18nHelper.translate(
                          context, '$_baseTranlaste.NoAssociatedEmployees'),
                  style: style),
            ],
          ),
        ),
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(I18nHelper.translate(context, '$_baseTranlaste.nursing')),
              Text(
                  tecnicoEnfermagem?.nomeFuncionario ??
                      I18nHelper.translate(
                          context, '$_baseTranlaste.NoAssociatedEmployees'),
                  style: style)
            ],
          ),
        )
      ],
    );
  }

  Widget _textLastUpdateOfTheListing() {
    return BlocBuilder<AttendanceListCubit, AttendanceListState>(
        builder: (context, stateList) {
      return BlocBuilder<LastSynchronizationDatesCubit,
          LastSynchronizationDatesState>(
        builder: (context, state) {
          if (state is LastSynchronizationDatesLoaded) {
            return Visibility(
              visible: stateList is LoadedAttendancesListState,
              child: Container(
                width: double.infinity,
                color: Theme.of(context).colorScheme.surface,
                child: Padding(
                  padding: EdgeInsets.only(
                    top: ConstantsTheme.doublePadding * 1.2,
                  ),
                  child: Text(
                    I18nHelper.translate(context,
                            '$_baseTranlaste.LastUpdateOfTheServiceList') +
                        ': '
                            '${state.lastSynchronizationDate}',
                    style: TextStyle(
                      fontSize: 15,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            );
          }
          return SizedBox.shrink();
        },
      );
    });
  }
}
