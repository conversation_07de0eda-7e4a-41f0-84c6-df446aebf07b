import 'package:ambulancia_app/bloc/attendance_team/attendance_team_cubit.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:ambulancia_app/models/funcoes_model.dart';
import 'package:ambulancia_app/screens/attendance/attendance_screen.dart';
import 'package:ambulancia_app/shared/screen-transitions/fade.transition.dart';
import 'package:ambulancia_app/shared/widgets/buttons/unimed_buttons.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class DialogUpdateTeam extends StatefulWidget {
  final AttendanceModel attendance;
  const DialogUpdateTeam({Key? key, required this.attendance})
      : super(key: key);

  @override
  _DialogUpdateTeamState createState() => _DialogUpdateTeamState();
}

class _DialogUpdateTeamState extends State<DialogUpdateTeam> {
  int? codMedicoSubstituto;
  int? codEnfermeiroSubstituto;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BlocBuilder<AttendanceTeamCubit, AttendanceTeamState>(
          builder: (context, state) {
            return Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Equipe',
                  style: TextStyle(fontSize: 25, fontWeight: FontWeight.bold),
                ),
                IconButton(
                  onPressed: state is LoadingAttendanceTeamState ||
                          state is UpdatingAttendanceTeamState
                      ? null
                      : () {
                          context.read<AttendanceTeamCubit>().getAttendanceTeam(
                                codUnimed: widget.attendance.codUnimed,
                              );
                        },
                  icon: Icon(Icons.refresh, size: 26),
                )
              ],
            );
          },
        ),
        SizedBox(height: ConstantsTheme.quadruplePadding),
        _team(),
      ],
    );
  }

  void _btnConfirm(availableDoctors, availableNurses) {
    context.read<AttendanceTeamCubit>().updateAttendanceTeam(
          attendance: widget.attendance,
          codMedicoSubstituto: codMedicoSubstituto,
          codEnfermeiroSubstituto: codEnfermeiroSubstituto,
        );
  }

  Widget _team() {
    return BlocBuilder<AttendanceTeamCubit, AttendanceTeamState>(
        buildWhen: (previousState, state) {
      if (state is UpdatedAttendanceTeamState) {
        Navigator.pop(context);
        Navigator.push(context,
            FadeRoute(page: AttendanceScreen(attendance: widget.attendance)));
      }
      return true;
    }, builder: (context, state) {
      if (state is LoadingAttendanceTeamState ||
          state is UpdatingAttendanceTeamState) {
        return SpinKitThreeBounce(color: AmbulanceColors.greenDark);
      } else if (state is ErrorAttendanceTeamState) {
        return Center(child: Text(state.messsage));
      } else if (state is ErrorUpdatingAttendanceTeamState) {
        return Center(child: Text(state.messsage));
      } else if (state is LoadedAttendanceTeamState) {
        return Column(
          children: [
            _listAvailableTeam(state.availableDoctors, state.availableNurses),
            SizedBox(height: ConstantsTheme.quadruplePadding),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Flexible(
                  child: UnimedFlatButton(
                    borderColor: AmbulanceColors.redClose,
                    text: 'VOLTAR',
                    textColor: Colors.white,
                    color: AmbulanceColors.redClose,
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
                SizedBox(width: ConstantsTheme.padding),
                Flexible(
                  child: UnimedFlatButton(
                    text: 'CONFIRMAR',
                    onPressed: () => _btnConfirm(
                        state.availableDoctors, state.availableNurses),
                  ),
                )
              ],
            )
          ],
        );
      } else
        return Container();
    });
  }

  _listAvailableTeam(availableDoctors, availableNurses) {
    final codMedicoAtual = widget.attendance.equipe.firstWhereOrNull(
        (element) => element.codFuncao == CodFuncoes.MEDICO_SOCORRISTA);
    codMedicoSubstituto = codMedicoAtual?.codFuncionario;

    final codEnfermeiroAtual = widget.attendance.equipe.firstWhereOrNull(
        (element) => element.codFuncao == CodFuncoes.ENFERMEIRO);
    codEnfermeiroSubstituto = codEnfermeiroAtual?.codFuncionario;

    return _funcionariosFuncao(
      label: 'Médico(a)',
      funcionarios:
          availableDoctors == null ? [] : availableDoctors.funcionarios,
      codFuncao: availableDoctors == null
          ? CodFuncoes.MEDICO_SOCORRISTA
          : availableDoctors.codFuncao,
      codFuncionarioAtual: codMedicoSubstituto,
    );
  }

  Widget _funcionariosFuncao({
    required String label,
    required List<FuncionarioModel> funcionarios,
    int? codFuncao,
    int? codFuncionarioAtual,
  }) {
    final FuncionarioModel? funcionario = funcionarios.isNotEmpty
        ? funcionarios.cast<FuncionarioModel?>().firstWhere(
            (element) => element?.codFuncionario == codFuncionarioAtual,
            orElse: () => null)
        : null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label),
        SizedBox(height: ConstantsTheme.padding),
        DropdownButtonFormField(
          value: funcionario,
          hint: funcionarios.isEmpty
              ? Text("Sem itens para ser selecionado")
              : Text("Selecione uma opção"),
          items: funcionarios.isNotEmpty
              ? funcionarios
                  .map<DropdownMenuItem>(
                    (funcionario) => DropdownMenuItem(
                      child: Text(
                        funcionario.nomeFuncionario ?? 'Nenhum',
                        style: TextStyle(color: AmbulanceColors.grayDark),
                        textScaler: TextScaler.linear(0.9),
                      ),
                      value: funcionario,
                    ),
                  )
                  .toList()
              : [],
          onChanged: (dynamic value) {
            if (codFuncao == CodFuncoes.MEDICO_SOCORRISTA)
              codMedicoSubstituto = value.codFuncionario;
            else if (codFuncao == CodFuncoes.ENFERMEIRO)
              codEnfermeiroSubstituto = value.codFuncionario;
          },
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(ConstantsTheme.borderRadius),
              borderSide: BorderSide(color: Colors.grey),
            ),
          ),
        )
      ],
    );
  }
}
