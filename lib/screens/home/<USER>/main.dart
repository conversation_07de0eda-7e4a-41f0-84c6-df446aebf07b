import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:flutter/material.dart';

import 'actions/attendance_actions.dart';
import 'attendance_card.dart';

class AttendanceItem extends StatefulWidget {
  final AttendanceModel attendance;
  AttendanceItem({required this.attendance});

  @override
  _AttendanceItemState createState() => _AttendanceItemState();
}

class _AttendanceItemState extends State<AttendanceItem> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 10),
      child: Row(
        children: [
          Opacity(
            opacity: _getOpacity(),
            child: AttendanceCard(attendance: widget.attendance),
          ),
          SizedBox(width: 1),
          AttendanceActions(attendance: widget.attendance),
        ],
      ),
    );
  }

  double _getOpacity() {
    return (widget.attendance.codStatus == AttendanceStatus.ENCERRADO ||
            widget.attendance.codStatus == AttendanceStatus.CANCELADO ||
            _unmappedStatusCheck())
        ? 0.5
        : 1;
  }

  bool _unmappedStatusCheck() {
    return (AttendanceStatus.statusApi2StatusApp(widget.attendance.codStatus) ==
            -1 &&
        widget.attendance.codStatus != AttendanceStatus.DESPACHO);
  }
}
