import 'dart:async';
import 'dart:ui';

import 'package:ambulancia_app/bloc/connectivity/connectivity_cubit.dart';
import 'package:ambulancia_app/bloc/offline_first/offline_first_cubit.dart';
import 'package:ambulancia_app/screens/main.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:flutter/material.dart';

Future<void> main() async {
  runZonedGuarded<Future<void>>(() async {
    WidgetsFlutterBinding.ensureInitialized();

    OfflineFirstCubit _offlineFirstCubit = OfflineFirstCubit();
    ConnectivityCubit _connectivityCubit = ConnectivityCubit();

    await Firebase.initializeApp();

    FirebaseCrashlytics.instance.setCustomKey(
        'environment', const String.fromEnvironment('environment'));

    FlutterError.onError = (FlutterErrorDetails errorDetails) {
      if (kDebugMode) return;

      FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
    };

    PlatformDispatcher.instance.onError = (error, stack) {
      if (kDebugMode) return false;
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };

    Timer.periodic(Duration(minutes: 5), (timer) async {
      await _connectivityCubit.verifyConnection();
      _offlineFirstCubit.syncDatabase(
        connectedToTheInternet:
            _connectivityCubit.state is ConnectivityOnlineState ? true : false,
      );
    });

    runApp(AmbulanciaUnimed(
      offlineFirstCubit: _offlineFirstCubit,
      connectivityCubit: _connectivityCubit,
    ));
  }, (error, stackTrace) {
    if (!kDebugMode) {
      FirebaseCrashlytics.instance.recordError(error, stackTrace);
    }
  });
}
