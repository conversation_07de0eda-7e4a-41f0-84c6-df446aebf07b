class DestinyModel {
  late List<DestinyObject> destinies;

  DestinyModel(List listJson) {
    if (listJson.isEmpty) destinies = [];
    destinies = listJson
        .map<DestinyObject>(
            (destinyJson) => DestinyObject.fromJson(destinyJson))
        .toList();
  }
}

class DestinyObject {
  int? codUnimed;
  String? patientDestinationCode;
  String? patientDestinationName;

  DestinyObject(
      {this.codUnimed, this.patientDestinationCode, this.patientDestinationName});

  DestinyObject.fromJson(Map<String, dynamic> json) {
    codUnimed = json['codUnimed'];
    patientDestinationCode = json['patientDestinationCode'];
    patientDestinationName = json['patientDestinationName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['codUnimed'] = this.codUnimed;
    data['patientDestinationCode'] = this.patientDestinationCode;
    data['patientDestinationName'] = this.patientDestinationName;
    return data;
  }
}
