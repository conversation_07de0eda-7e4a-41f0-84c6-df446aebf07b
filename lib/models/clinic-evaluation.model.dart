import 'package:json_annotation/json_annotation.dart';

part 'clinic-evaluation.model.g.dart';

class CERitmosCode {
  static const String Regular = 'R';
  static const String Irregular = 'I';
  static const String Absent = 'A';
}

class CECirculacaoCode {
  static const String PrecordialPain = 'DP';
  static const String PaceMarker = 'MP';
  static const String NotReferred = 'NR';
}

class CEAirwaysVentilationCode {
  static const String TrachealIntubation = 'IT';
  static const String VenturiMask = 'MV';
  static const String Tracheostomy = 'TR';
  static const String OxygenMask = 'OM';
  static const String MechanicalVentilation = 'VM';
  static const String AmbienteAr = 'AA';
  static const String NasalCatheter = 'CN';
}

class CEVenousAccessCode {
  static const String VasopressorAmines = 'AV';
}

class CEKidneyFunctionCode {
  static const String Diuresis = 'D';
  static const String Hemodialysis = 'H';
  static const String Normal = 'N';
}

class CESurgicalInformation {}

@JsonSerializable(anyMap: true, explicitToJson: true)
class ResponseClinicEvaluation {
  final List<Activity> acessoVenoso;
  final List<Activity> circulacao;
  final List<Activity> funacaoRenal;
  final List<Activity> pele;
  final List<Activity> pupilas;
  final List<Activity> ritmos;
  final List<Activity> sinaisLaterizacao;
  final List<Activity> sistemaGastrointestinal;
  final List<Activity> tipoMarcaPasso;
  final List<Activity> viasAereasVentilacao;
  final List<Activity> informacaoCirurgica;
  final List<Activity> glasgow;
  final List<Activity> sistemaVenosoCentral;
  final int? intensidadeDor;
  final String? dorCabeca;
  final String? dorBarriga;
  final String? dorMembrosSup;
  final String? dorMembrosInf;
  final String? dorLombar;
  final String? dorToraxPeito;
  final String? dorDorso;
  final String? dorPescocoCervical;
  final String? dorOutros;
  final String? obsDorOutros;

  ResponseClinicEvaluation({
    required this.acessoVenoso,
    required this.circulacao,
    required this.funacaoRenal,
    required this.pele,
    required this.pupilas,
    required this.ritmos,
    required this.sinaisLaterizacao,
    required this.sistemaGastrointestinal,
    required this.tipoMarcaPasso,
    required this.viasAereasVentilacao,
    required this.informacaoCirurgica,
    required this.glasgow,
    required this.sistemaVenosoCentral,
    this.intensidadeDor,
    this.dorCabeca,
    this.dorBarriga,
    this.dorMembrosSup,
    this.dorMembrosInf,
    this.dorLombar,
    this.dorToraxPeito,
    this.dorDorso,
    this.dorPescocoCervical,
    this.dorOutros,
    this.obsDorOutros,
  });
  factory ResponseClinicEvaluation.fromJson(Map json) =>
      _$ResponseClinicEvaluationFromJson(json);
  Map<String, dynamic> toJson() => _$ResponseClinicEvaluationToJson(this);
}

@JsonSerializable(anyMap: true, explicitToJson: true)
class Activity {
  @JsonKey(
    fromJson: _toString,
  )
  final String? codigo;
  final String? descricao;

  static _toString(dynamic value) => value.toString();
  Activity({this.codigo, this.descricao});
  factory Activity.fromJson(Map json) => _$ActivityFromJson(json);
  Map<String, dynamic> toJson() => _$ActivityToJson(this);
}

@JsonSerializable(anyMap: true, explicitToJson: true)
class RequestClinicEvaluation {
  String? temperatura;
  int? dx;
  int? numAtendimento;
  int? frequenciaCardiaca;
  int? frequenciaRespiratoria;
  int? pressaoArterialAlta;
  int? pressaoArterialBaixa;
  int? saturacao;
  String? ritmos;
  String? circulacao;
  String? tipoMarcaPasso;
  String? acessoVenoso;
  int? diluicaoVelocidadeInfusao;
  String? aminasVasopressorasQuais;
  String? pupilas;
  int? glasgow;
  String? sinaisLaterizacao;
  String? pele;
  String? posOperatorio;
  String? feridaOperatoria;
  String? infeccaoFeridaOperatoria;
  String? drenos;
  String? estomas;
  String? examesLaboratoriais;
  String? examesImagem;
  String? sistemaGastrointestinal;
  int? duracaoVentilacaoQtdeDias;
  int? duracaoVentilacaoQtdeHorasDia;
  String? observacaoAvaliacaoClinica;
  String? funcaoRenalHemodialise;
  String? funcaoRenalDiurese;
  String? funcaoRenalNormal;
  String? diurese;
  String? intubacaoTraqueal;
  String? mascaraVenturi;
  String? traqueostomia;
  String? cateterNasal;
  String? oxigenioPorMascara;
  String? ventilacaoMecanica;
  String? arAmbiente;
  String? escalaDor;
  int? intensidadeDor;
  String? dorCabeca;
  String? dorBarriga;
  String? dorMembrosSup;
  String? dorMembrosInf;
  String? dorLombar;
  String? dorToraxPeito;
  String? dorDorso;
  String? dorPescocoCervical;
  String? dorOutros;
  String? obsDorOutros;

  RequestClinicEvaluation({
    this.numAtendimento,
    this.frequenciaCardiaca,
    this.frequenciaRespiratoria,
    this.pressaoArterialAlta,
    this.pressaoArterialBaixa,
    this.saturacao,
    this.ritmos,
    this.circulacao,
    this.tipoMarcaPasso,
    this.acessoVenoso,
    this.diluicaoVelocidadeInfusao,
    this.aminasVasopressorasQuais,
    this.pupilas,
    this.glasgow,
    this.sinaisLaterizacao,
    this.pele,
    this.posOperatorio,
    this.feridaOperatoria,
    this.infeccaoFeridaOperatoria,
    this.drenos,
    this.estomas,
    this.examesLaboratoriais,
    this.examesImagem,
    this.sistemaGastrointestinal,
    this.duracaoVentilacaoQtdeDias,
    this.duracaoVentilacaoQtdeHorasDia,
    this.observacaoAvaliacaoClinica,
    this.funcaoRenalHemodialise,
    this.funcaoRenalDiurese,
    this.funcaoRenalNormal,
    this.diurese,
    this.intubacaoTraqueal,
    this.mascaraVenturi,
    this.traqueostomia,
    this.cateterNasal,
    this.oxigenioPorMascara,
    this.ventilacaoMecanica,
    this.arAmbiente,
    this.escalaDor,
    this.intensidadeDor,
    this.dorCabeca,
    this.dorBarriga,
    this.dorMembrosSup,
    this.dorMembrosInf,
    this.dorLombar,
    this.dorToraxPeito,
    this.dorDorso,
    this.dorPescocoCervical,
    this.dorOutros,
    this.obsDorOutros,
  });

  factory RequestClinicEvaluation.fromJson(Map json) =>
      _$RequestClinicEvaluationFromJson(json);
  Map<String, dynamic> toJson() => _$RequestClinicEvaluationToJson(this);
}
