class RequestServiceDataTime {
  final String serviceName;
  DateTime lastUpdate;

  RequestServiceDataTime({required this.serviceName, required this.lastUpdate});

  Map<String, dynamic> toMap() {
    return {
      'serviceName': serviceName,
      'lastUpdate': lastUpdate.toIso8601String(),
    };
  }

  factory RequestServiceDataTime.fromMap(Map<String, dynamic> map) {
    return RequestServiceDataTime(
      serviceName: map['serviceName'],
      lastUpdate: DateTime.parse(map['lastUpdate']),
    );
  }
}
