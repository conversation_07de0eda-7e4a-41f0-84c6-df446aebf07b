// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserCredentials _$UserCredentialsFromJson(Map<String, dynamic> json) =>
    UserCredentials(
      user: json['user'] as String?,
      password: json['password'] as String?,
    );

Map<String, dynamic> _$UserCredentialsToJson(UserCredentials instance) =>
    <String, dynamic>{
      'user': instance.user,
      'password': instance.password,
    };

LoginDataModel _$LoginDataModelFromJson(Map<String, dynamic> json) =>
    LoginDataModel(
      userCredentials: json['userCredentials'] == null
          ? null
          : UserCredentials.fromJson(
              json['userCredentials'] as Map<String, dynamic>),
      vehicleModel: json['vehicleModel'] == null
          ? null
          : VehicleModel.fromJson(json['vehicleModel'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$LoginDataModelToJson(LoginDataModel instance) =>
    <String, dynamic>{
      'userCredentials': instance.userCredentials?.toJson(),
      'vehicleModel': instance.vehicleModel?.toJson(),
    };

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
      cpf: json['cpf'] as String?,
      nome: json['nome'] as String?,
      telefone: json['telefone'] as String?,
      email: json['email'] as String?,
      cnh: json['cnh'] as String?,
      coren: json['coren'] as String?,
    );

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
      'cpf': instance.cpf,
      'nome': instance.nome,
      'telefone': instance.telefone,
      'email': instance.email,
      'cnh': instance.cnh,
      'coren': instance.coren,
    };
