enum AttendanceTimelineStepsEnum {
  start,
  originArrival,
  originDeparture,
  destinationArrival,
  destinationDeparture,
  baseArrival,
  closure,
  newAttendance,
  canceled
}

class AttendanceStatus {
  static const int INICIO = 1;
  static const int REGULACAO = 2;
  static const int DESPACHO = 3;
  static const int SAIDA_BASE = 4; //<PERSON><PERSON> da <PERSON>
  static const int CHEGADA_ORIGEM = 5; // Chegada à Origem
  static const int SAIDA_ORIGEM = 6; // Saída da Origem
  static const int CHEGADA_DESTINO = 7; // Chegada ao Destino
  static const int SAIDA_DESTINO = 8; // Saída do Destino
  static const int CHEGADA_BASE = 9; // Chegada à Base
  static const int ENCERRADO = 10; // Encerramento
  static const int NOVO_ATENDIMENTO = 11;
  static const int CANCELADO = 12;

  static int statusApi2StatusApp(int? codStatusApi) {
    if (codStatusApi == SAIDA_BASE)
      return AttendanceTimelineStepsEnum.start.index;
    else if (codStatusApi == CHEGADA_ORIGEM)
      return AttendanceTimelineStepsEnum.originArrival.index;
    else if (codStatusApi == SAIDA_ORIGEM)
      return AttendanceTimelineStepsEnum.originDeparture.index;
    else if (codStatusApi == CHEGADA_DESTINO)
      return AttendanceTimelineStepsEnum.destinationArrival.index;
    else if (codStatusApi == SAIDA_DESTINO)
      return AttendanceTimelineStepsEnum.destinationDeparture.index;
    else if (codStatusApi == CHEGADA_BASE)
      return AttendanceTimelineStepsEnum.baseArrival.index;
    else if (codStatusApi == ENCERRADO)
      return AttendanceTimelineStepsEnum.closure.index;
    else if (codStatusApi == NOVO_ATENDIMENTO)
      return AttendanceTimelineStepsEnum.newAttendance.index;
    else if (codStatusApi == CANCELADO) {
      return AttendanceTimelineStepsEnum.canceled.index;
    } else {
      return -1;
    }
  }

  static int statusApp2StatusApi(int? codStatusApp) {
    if (codStatusApp == AttendanceTimelineStepsEnum.start.index)
      return SAIDA_BASE;
    else if (codStatusApp == AttendanceTimelineStepsEnum.originArrival.index)
      return CHEGADA_ORIGEM;
    else if (codStatusApp == AttendanceTimelineStepsEnum.originDeparture.index)
      return SAIDA_ORIGEM;
    else if (codStatusApp ==
        AttendanceTimelineStepsEnum.destinationArrival.index)
      return CHEGADA_DESTINO;
    else if (codStatusApp ==
        AttendanceTimelineStepsEnum.destinationDeparture.index)
      return SAIDA_DESTINO;
    else if (codStatusApp == AttendanceTimelineStepsEnum.baseArrival.index)
      return CHEGADA_BASE;
    else if (codStatusApp == AttendanceTimelineStepsEnum.closure.index)
      return ENCERRADO;
    else if (codStatusApp == AttendanceTimelineStepsEnum.newAttendance.index)
      return NOVO_ATENDIMENTO;
    else if (codStatusApp == AttendanceTimelineStepsEnum.canceled.index)
      return CANCELADO;
    else {
      return 11;
    }
  }

  static bool verifyInitAttendance(int codStatus, int? statusAttendance) {
    if (statusAttendance == AttendanceStatus.DESPACHO) {
      if (codStatus > AttendanceTimelineStepsEnum.start.index) {
        return false;
      } else {
        return true;
      }
    } else {
      return true;
    }
  }
}

const Map<AttendanceTimelineStepsEnum, String> attendanceTimelineSteps = {
  AttendanceTimelineStepsEnum.start: 'Início do\nAtendimento',
  AttendanceTimelineStepsEnum.originArrival: 'Chegada\nà Origem',
  AttendanceTimelineStepsEnum.originDeparture: 'Saída da\nOrigem',
  AttendanceTimelineStepsEnum.destinationArrival: 'Chegada\nao Destino',
  AttendanceTimelineStepsEnum.destinationDeparture: 'Saída do\nDestino',
  AttendanceTimelineStepsEnum.baseArrival: 'Chegada\nà Base',
};

const Map<AttendanceTimelineStepsEnum, String> attendanceTimelineComplete = {
  AttendanceTimelineStepsEnum.start: 'Início do\nAtendimento',
  AttendanceTimelineStepsEnum.originArrival: 'Chegada\nà Origem',
  AttendanceTimelineStepsEnum.originDeparture: 'Saída da\nOrigem',
  AttendanceTimelineStepsEnum.destinationArrival: 'Chegada\nao Destino',
  AttendanceTimelineStepsEnum.destinationDeparture: 'Saída do\nDestino',
  AttendanceTimelineStepsEnum.baseArrival: 'Chegada\nà Base',
  AttendanceTimelineStepsEnum.closure: 'Atendimento\nEncerrado',
  AttendanceTimelineStepsEnum.newAttendance: 'Novo\nAtendimento',
  AttendanceTimelineStepsEnum.canceled: 'Atendimento\nCancelado',
};
