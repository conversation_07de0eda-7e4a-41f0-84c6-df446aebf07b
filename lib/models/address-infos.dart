import 'package:ambulancia_app/models/adress_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'address-infos.g.dart';

@JsonSerializable(explicitToJson: true)
class AddressInfos {
  final AddressModel? address;
  final createdAt;
  final lat;
  final lng;

  AddressInfos(this.lat, this.lng, this.address, this.createdAt);

  factory AddressInfos.fromJson(Map json) =>
      _$AddressInfosFromJson(json as Map<String, dynamic>);
  Map<String, dynamic> toJson() => _$AddressInfosToJson(this);
}
