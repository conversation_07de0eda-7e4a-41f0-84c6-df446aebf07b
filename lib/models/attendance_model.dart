import 'package:ambulancia_app/models/adress_model.dart';
import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:ambulancia_app/models/supply_model.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'attendance_model.g.dart';

@JsonSerializable(anyMap: true, explicitToJson: true)
class AttendanceModel {
  String nome;
  String rg;
  String? cpf;
  int? idade;
  String dataNascimento;
  int numAtendimento;
  int codUnimed;
  String codVeiculo;
  String codTipoCliente;
  String nomeTipoCliente;
  String codTipoAtendimento;
  int codStatus;
  String nomeStatus;
  int? unimedCarteira;
  int? codigoCarteira;
  String? dvCarteira;
  String sintomaAcontecendo;
  String sintomaComecou;
  String sintomaHistorico;
  String sintomaQuando;
  String descricaoTerapeutica;
  String dataAtendimento;
  List<FuncionarioModel> equipe;
  String observacaoMedicoRegulador;
  String hipoteseDiagnostica;
  String observacaoProcedimento;
  String descricaoDestinoPaciente;
  String? codigoDestinoPaciente;
  AddressModel? enderecoAtendimento;
  AddressModel? enderecoDestino;
  List<SupplyAttendance> materiais;
  String remocao;

  String observacaoConduta;
  String codDiagnostico;
  String descricaoDiagnostico;

  bool indentificaoAvClinica;
  bool indentificaoConduta;

  bool get getRemocao => remocao == 'N' ? false : true;

  bool isReclassifica;
  String cod_TipoAtend_Reclas;
  bool solicitaReclassifTipoAtend;

  String nomeMae;

  UniurgProtocoloDoencas? uniurgProtocoloDoencas;
  String obsProtocoloDoenca;

  String _addressFormatted(endereco) {
    if (endereco?.tipoLogradouro == null) return 'Selecione o Destino';
    return '${endereco?.tipoLogradouro ?? ''} ' +
        '${endereco?.logradouro ?? ''}, ';
  }

  String _addressFormattedComplement(endereco) {
    if (endereco?.tipoLogradouro == null) return 'Selecione o Destino';
    return '${endereco?.numero ?? ''}, ' +
        '${endereco?.complemento ?? ''} - ' +
        '${endereco?.bairro ?? ''}, ' +
        '${endereco?.cidade ?? ''}, ' +
        '${endereco?.cep ?? ''}. ';
  }

  String get addressAttendanceFormatted {
    return _addressFormatted(enderecoAtendimento);
  }

  String get addressAttendanceFormattedComplement {
    return _addressFormattedComplement(enderecoAtendimento);
  }

  String get addressDestinyFormatted {
    return _addressFormatted(enderecoDestino);
  }

  String get carteiraNumero {
    final part1 = '$unimedCarteira'.padLeft(3, '0');
    final part2 = '$codigoCarteira'.padLeft(12, '0');
    return '$part1$part2$dvCarteira';
  }

  AttendanceModel({
    this.nome = "",
    this.rg = "",
    this.cpf,
    this.idade,
    this.dataNascimento = "",
    required this.numAtendimento,
    required this.codUnimed,
    this.codVeiculo = "",
    this.codTipoCliente = "",
    this.nomeTipoCliente = "",
    required this.codTipoAtendimento,
    required this.codStatus,
    required this.nomeStatus,
    this.unimedCarteira,
    this.codigoCarteira,
    this.dvCarteira,
    this.sintomaAcontecendo = "",
    this.sintomaComecou = "",
    this.sintomaHistorico = "",
    this.sintomaQuando = "",
    this.descricaoTerapeutica = "",
    required this.dataAtendimento,
    this.equipe = const <FuncionarioModel>[],
    this.hipoteseDiagnostica = "",
    this.observacaoMedicoRegulador = "",
    this.observacaoProcedimento = "",
    this.enderecoAtendimento,
    this.descricaoDestinoPaciente = "",
    this.codigoDestinoPaciente,
    this.materiais = const <SupplyAttendance>[],
    this.enderecoDestino,
    required this.remocao,
    this.codDiagnostico = "",
    this.descricaoDiagnostico = "",
    this.observacaoConduta = "",
    this.indentificaoAvClinica = false,
    this.indentificaoConduta = true,
    this.isReclassifica = false,
    this.cod_TipoAtend_Reclas = "",
    this.solicitaReclassifTipoAtend = false,
    this.nomeMae = '',
    required this.uniurgProtocoloDoencas,
    this.obsProtocoloDoenca = "",
  });

  factory AttendanceModel.fromJson(Map json) => _$AttendanceModelFromJson(json);
  Map<String, dynamic> toJson() => _$AttendanceModelToJson(this);

  AttendanceModel copyWith({
    String? nome,
    String? rg,
    String? cpf,
    int? idade,
    String? dataNascimento,
    int? numAtendimento,
    int? codUnimed,
    String? codVeiculo,
    String? codTipoCliente,
    String? nomeTipoCliente,
    String? codTipoAtendimento,
    int? codStatus,
    String? nomeStatus,
    int? unimedCarteira,
    int? codigoCarteira,
    String? dvCarteira,
    String? sintomaAcontecendo,
    String? sintomaComecou,
    String? sintomaHistorico,
    String? sintomaQuando,
    String? descricaoTerapeutica,
    String? dataAtendimento,
    List<FuncionarioModel>? equipe,
    String? observacaoMedicoRegulador,
    String? hipoteseDiagnostica,
    String? observacaoProcedimento,
    String? descricaoDestinoPaciente,
    String? codigoDestinoPaciente,
    AddressModel? enderecoAtendimento,
    AddressModel? enderecoDestino,
    List<SupplyAttendance>? materiais,
    String? remocao,
    String? observacaoConduta,
    String? codDiagnostico,
    String? descricaoDiagnostico,
    bool? indentificaoAvClinica,
    bool? indentificaoConduta,
    bool? isReclassifica,
    String? cod_TipoAtend_Reclas,
    bool? solicitaReclassifTipoAtend,
    String? nomeMae,
    UniurgProtocoloDoencas? uniurgProtocoloDoencas,
    String? obsProtocoloDoenca,
  }) {
    return AttendanceModel(
      nome: nome ?? this.nome,
      rg: rg ?? this.rg,
      cpf: cpf ?? this.cpf,
      idade: idade ?? this.idade,
      dataNascimento: dataNascimento ?? this.dataNascimento,
      numAtendimento: numAtendimento ?? this.numAtendimento,
      codUnimed: codUnimed ?? this.codUnimed,
      codVeiculo: codVeiculo ?? this.codVeiculo,
      codTipoCliente: codTipoCliente ?? this.codTipoCliente,
      nomeTipoCliente: nomeTipoCliente ?? this.nomeTipoCliente,
      codTipoAtendimento: codTipoAtendimento ?? this.codTipoAtendimento,
      codStatus: codStatus ?? this.codStatus,
      nomeStatus: nomeStatus ?? this.nomeStatus,
      unimedCarteira: unimedCarteira ?? this.unimedCarteira,
      codigoCarteira: codigoCarteira ?? this.codigoCarteira,
      dvCarteira: dvCarteira ?? this.dvCarteira,
      sintomaAcontecendo: sintomaAcontecendo ?? this.sintomaAcontecendo,
      sintomaComecou: sintomaComecou ?? this.sintomaComecou,
      sintomaHistorico: sintomaHistorico ?? this.sintomaHistorico,
      sintomaQuando: sintomaQuando ?? this.sintomaQuando,
      descricaoTerapeutica: descricaoTerapeutica ?? this.descricaoTerapeutica,
      dataAtendimento: dataAtendimento ?? this.dataAtendimento,
      equipe: equipe ?? this.equipe,
      observacaoMedicoRegulador:
          observacaoMedicoRegulador ?? this.observacaoMedicoRegulador,
      hipoteseDiagnostica: hipoteseDiagnostica ?? this.hipoteseDiagnostica,
      observacaoProcedimento:
          observacaoProcedimento ?? this.observacaoProcedimento,
      descricaoDestinoPaciente:
          descricaoDestinoPaciente ?? this.descricaoDestinoPaciente,
      codigoDestinoPaciente:
          codigoDestinoPaciente ?? this.codigoDestinoPaciente,
      enderecoAtendimento: enderecoAtendimento ?? this.enderecoAtendimento,
      enderecoDestino: enderecoDestino ?? this.enderecoDestino,
      materiais: materiais ?? this.materiais,
      remocao: remocao ?? this.remocao,
      observacaoConduta: observacaoConduta ?? this.observacaoConduta,
      codDiagnostico: codDiagnostico ?? this.codDiagnostico,
      descricaoDiagnostico: descricaoDiagnostico ?? this.descricaoDiagnostico,
      indentificaoAvClinica:
          indentificaoAvClinica ?? this.indentificaoAvClinica,
      indentificaoConduta: indentificaoConduta ?? this.indentificaoConduta,
      isReclassifica: isReclassifica ?? this.isReclassifica,
      cod_TipoAtend_Reclas: cod_TipoAtend_Reclas ?? this.cod_TipoAtend_Reclas,
      solicitaReclassifTipoAtend:
          solicitaReclassifTipoAtend ?? this.solicitaReclassifTipoAtend,
      nomeMae: nomeMae ?? this.nomeMae,
      uniurgProtocoloDoencas:
          uniurgProtocoloDoencas ?? this.uniurgProtocoloDoencas,
      obsProtocoloDoenca: obsProtocoloDoenca ?? this.obsProtocoloDoenca,
    );
  }
}

class UniurgProtocoloDoencas {
  int codProtocoloDoenca;
  String? nomeProtocoloDoenca;

  UniurgProtocoloDoencas({
    required this.codProtocoloDoenca,
    this.nomeProtocoloDoenca,
  });

  factory UniurgProtocoloDoencas.fromJson(Map<String, dynamic> json) {
    return UniurgProtocoloDoencas(
      codProtocoloDoenca: json['codProtocoloDoenca'],
      nomeProtocoloDoenca: json['nomeProtocoloDoenca'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'codProtocoloDoenca': codProtocoloDoenca,
      'nomeProtocoloDoenca': nomeProtocoloDoenca,
    };
  }
}

const Map<String, Color> attendanceColor = {
  'C3': AmbulanceColors.error,
  'C2': AmbulanceColors.orange,
  'C1': AmbulanceColors.green,
};

class DropdownAttendanceModel {
  final int? id;
  final String? name;

  DropdownAttendanceModel({this.name, this.id});
}
