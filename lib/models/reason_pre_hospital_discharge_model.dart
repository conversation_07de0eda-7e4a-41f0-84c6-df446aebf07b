class ReasonPreHospitalDischargeModel {
  final int codeReason;
  final String nameReason;

  ReasonPreHospitalDischargeModel({
    required this.codeReason,
    required this.nameReason,
  });

  factory ReasonPreHospitalDischargeModel.fromJson(Map<String, dynamic> json) {
    return ReasonPreHospitalDischargeModel(
      codeReason: json['codeReason'],
      nameReason: json['nameReason'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'codeReason': codeReason,
      'nameReason': nameReason,
    };
  }
}
