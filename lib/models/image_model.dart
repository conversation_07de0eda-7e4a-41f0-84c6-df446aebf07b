class ImageModel {
  final int uniurgMotivoAnexoEnum;
  final String fileName;
  final String fileBase64;
  final int imgNumber;
  bool isNewAttachment;
  bool isCache;

  ImageModel({
    required this.uniurgMotivoAnexoEnum,
    required this.fileName,
    required this.fileBase64,
    required this.imgNumber,
    this.isNewAttachment = false,
    this.isCache = false,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['uniurgMotivoAnexoEnum'] = this.uniurgMotivoAnexoEnum;
    data['fileName'] = this.fileName;
    data['fileBase64'] = this.fileBase64;
    return data;
  }

  factory ImageModel.fromJson(Map<String, dynamic> json) {
    return ImageModel(
      uniurgMotivoAnexoEnum: json['uniurgMotivoAnexoEnum'] as int,
      fileName: json['fileName'],
      fileBase64: json['fileBase64'],
      imgNumber: json['imgNumber'] ?? 0,
      isNewAttachment: json['isNewAttachment'] ?? false,
    );
  }
}
