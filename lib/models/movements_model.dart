import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';

part 'movements_model.g.dart';

@JsonSerializable(anyMap: true, explicitToJson: true)
class MovementsModel {
  int attendance;
  List<MovementObject> moviments;

  MovementsModel({
    required this.attendance,
    required this.moviments,
  });

  factory MovementsModel.fromJson(Map json) {
    return MovementsModel(
      attendance: json['attendance'] as int,
      moviments: (json['moviments'] ?? json['moviments'] ?? [])
          .map<MovementObject>((e) => MovementObject.fromJson(e as Map))
          .toList(),
    );
  }
}

@JsonSerializable(anyMap: true)
class MovementObject {
  String? movimentDate;
  int? statusCode;
  String? statusName;

  MovementObject({
    this.movimentDate,
    this.statusCode,
    this.statusName,
  });

  String get dataMovimentacaoFormated {
    try {
      final df = new DateFormat('HH:mm\ndd/MM/yyyy');

      final inputSplit = movimentDate!.split(' ');
      final dateSplit = inputSplit[0].split('-');
      final timeSplit = inputSplit[1].split(':');
      final DateTime date = DateTime(
        int.parse(dateSplit[0]),
        int.parse(dateSplit[1]),
        int.parse(dateSplit[2]),
        int.parse(timeSplit[0]),
        int.parse(timeSplit[1]),
        int.parse(timeSplit[2]),
      );
      return df.format(date);
    } catch (ex) {
      return '';
    }
  }

  factory MovementObject.fromJson(Map json) => _$MovementObjectFromJson(json);
  Map<String, dynamic> toJson() => _$MovementObjectToJson(this);
}
