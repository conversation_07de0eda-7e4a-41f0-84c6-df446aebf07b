import 'package:json_annotation/json_annotation.dart';
part 'material_expenses_model.g.dart';

@JsonSerializable(anyMap: true, explicitToJson: true)
class MaterialExpensesModel {
  String? id;
  String? item;
  int? quantity;

  MaterialExpensesModel({
    this.id,
    this.item,
    this.quantity,
  });

  factory MaterialExpensesModel.fromJson(Map json) =>
      _$MaterialExpensesModelFromJson(json);
  Map<String, dynamic> toJson() => _$MaterialExpensesModelToJson(this);
}
