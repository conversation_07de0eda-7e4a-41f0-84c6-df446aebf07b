// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'clinic-evaluation.model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ResponseClinicEvaluation _$ResponseClinicEvaluationFromJson(Map json) =>
    ResponseClinicEvaluation(
      acessoVenoso: (json['acessoVenoso'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map))
          .toList(),
      circulacao: (json['circulacao'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map))
          .toList(),
      funacaoRenal: (json['funacaoRenal'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map))
          .toList(),
      pele: (json['pele'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map))
          .toList(),
      pupilas: (json['pupilas'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map))
          .toList(),
      ritmos: (json['ritmos'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map))
          .toList(),
      sinaisLaterizacao: (json['sinaisLaterizacao'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map))
          .toList(),
      sistemaGastrointestinal:
          (json['sistemaGastrointestinal'] as List<dynamic>)
              .map((e) => Activity.fromJson(e as Map))
              .toList(),
      tipoMarcaPasso: (json['tipoMarcaPasso'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map))
          .toList(),
      viasAereasVentilacao: (json['viasAereasVentilacao'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map))
          .toList(),
      informacaoCirurgica: (json['informacaoCirurgica'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map))
          .toList(),
      glasgow: (json['glasgow'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map))
          .toList(),
      sistemaVenosoCentral: (json['sistemaVenosoCentral'] as List<dynamic>)
          .map((e) => Activity.fromJson(e as Map))
          .toList(),
      intensidadeDor: (json['intensidadeDor'] as num?)?.toInt(),
      dorCabeca: json['dorCabeca'] as String?,
      dorBarriga: json['dorBarriga'] as String?,
      dorMembrosSup: json['dorMembrosSup'] as String?,
      dorMembrosInf: json['dorMembrosInf'] as String?,
      dorLombar: json['dorLombar'] as String?,
      dorToraxPeito: json['dorToraxPeito'] as String?,
      dorDorso: json['dorDorso'] as String?,
      dorPescocoCervical: json['dorPescocoCervical'] as String?,
      dorOutros: json['dorOutros'] as String?,
      obsDorOutros: json['obsDorOutros'] as String?,
    );

Map<String, dynamic> _$ResponseClinicEvaluationToJson(
        ResponseClinicEvaluation instance) =>
    <String, dynamic>{
      'acessoVenoso': instance.acessoVenoso.map((e) => e.toJson()).toList(),
      'circulacao': instance.circulacao.map((e) => e.toJson()).toList(),
      'funacaoRenal': instance.funacaoRenal.map((e) => e.toJson()).toList(),
      'pele': instance.pele.map((e) => e.toJson()).toList(),
      'pupilas': instance.pupilas.map((e) => e.toJson()).toList(),
      'ritmos': instance.ritmos.map((e) => e.toJson()).toList(),
      'sinaisLaterizacao':
          instance.sinaisLaterizacao.map((e) => e.toJson()).toList(),
      'sistemaGastrointestinal':
          instance.sistemaGastrointestinal.map((e) => e.toJson()).toList(),
      'tipoMarcaPasso': instance.tipoMarcaPasso.map((e) => e.toJson()).toList(),
      'viasAereasVentilacao':
          instance.viasAereasVentilacao.map((e) => e.toJson()).toList(),
      'informacaoCirurgica':
          instance.informacaoCirurgica.map((e) => e.toJson()).toList(),
      'glasgow': instance.glasgow.map((e) => e.toJson()).toList(),
      'sistemaVenosoCentral':
          instance.sistemaVenosoCentral.map((e) => e.toJson()).toList(),
      'intensidadeDor': instance.intensidadeDor,
      'dorCabeca': instance.dorCabeca,
      'dorMembrosSup': instance.dorMembrosSup,
      'dorMembrosInf': instance.dorMembrosInf,
      'dorLombar': instance.dorLombar,
      'dorToraxPeito': instance.dorToraxPeito,
      'dorDorso': instance.dorDorso,
      'dorPescocoCervical': instance.dorPescocoCervical,
      'dorOutros': instance.dorOutros,
      'obsDorOutros': instance.obsDorOutros,
    };

Activity _$ActivityFromJson(Map json) => Activity(
      codigo: Activity._toString(json['codigo']),
      descricao: json['descricao'] as String?,
    );

Map<String, dynamic> _$ActivityToJson(Activity instance) => <String, dynamic>{
      'codigo': instance.codigo,
      'descricao': instance.descricao,
    };

RequestClinicEvaluation _$RequestClinicEvaluationFromJson(Map json) =>
    RequestClinicEvaluation(
      numAtendimento: (json['numAtendimento'] as num?)?.toInt(),
      frequenciaCardiaca: (json['frequenciaCardiaca'] as num?)?.toInt(),
      frequenciaRespiratoria: (json['frequenciaRespiratoria'] as num?)?.toInt(),
      pressaoArterialAlta: (json['pressaoArterialAlta'] as num?)?.toInt(),
      pressaoArterialBaixa: (json['pressaoArterialBaixa'] as num?)?.toInt(),
      saturacao: (json['saturacao'] as num?)?.toInt(),
      ritmos: json['ritmos'] as String?,
      circulacao: json['circulacao'] as String?,
      tipoMarcaPasso: json['tipoMarcaPasso'] as String?,
      acessoVenoso: json['acessoVenoso'] as String?,
      diluicaoVelocidadeInfusao:
          (json['diluicaoVelocidadeInfusao'] as num?)?.toInt(),
      aminasVasopressorasQuais: json['aminasVasopressorasQuais'] as String?,
      pupilas: json['pupilas'] as String?,
      glasgow: (json['glasgow'] as num?)?.toInt(),
      sinaisLaterizacao: json['sinaisLaterizacao'] as String?,
      pele: json['pele'] as String?,
      posOperatorio: json['posOperatorio'] as String?,
      feridaOperatoria: json['feridaOperatoria'] as String?,
      infeccaoFeridaOperatoria: json['infeccaoFeridaOperatoria'] as String?,
      drenos: json['drenos'] as String?,
      estomas: json['estomas'] as String?,
      examesLaboratoriais: json['examesLaboratoriais'] as String?,
      examesImagem: json['examesImagem'] as String?,
      sistemaGastrointestinal: json['sistemaGastrointestinal'] as String?,
      duracaoVentilacaoQtdeDias:
          (json['duracaoVentilacaoQtdeDias'] as num?)?.toInt(),
      duracaoVentilacaoQtdeHorasDia:
          (json['duracaoVentilacaoQtdeHorasDia'] as num?)?.toInt(),
      observacaoAvaliacaoClinica: json['observacaoAvaliacaoClinica'] as String?,
      funcaoRenalHemodialise: json['funcaoRenalHemodialise'] as String?,
      funcaoRenalDiurese: json['funcaoRenalDiurese'] as String?,
      funcaoRenalNormal: json['funcaoRenalNormal'] as String?,
      diurese: json['diurese'] as String?,
      intubacaoTraqueal: json['intubacaoTraqueal'] as String?,
      mascaraVenturi: json['mascaraVenturi'] as String?,
      traqueostomia: json['traqueostomia'] as String?,
      cateterNasal: json['cateterNasal'] as String?,
      oxigenioPorMascara: json['oxigenioPorMascara'] as String?,
      ventilacaoMecanica: json['ventilacaoMecanica'] as String?,
      arAmbiente: json['arAmbiente'] as String?,
      escalaDor: json['escalaDor'] as String?,
      intensidadeDor: (json['intensidadeDor'] as num?)?.toInt(),
      dorCabeca: json['dorCabeca'] as String?,
      dorBarriga: json['dorBarriga'] as String?,
      dorMembrosSup: json['dorMembrosSup'] as String?,
      dorMembrosInf: json['dorMembrosInf'] as String?,
      dorLombar: json['dorLombar'] as String?,
      dorToraxPeito: json['dorToraxPeito'] as String?,
      dorDorso: json['dorDorso'] as String?,
      dorPescocoCervical: json['dorPescocoCervical'] as String?,
      dorOutros: json['dorOutros'] as String?,
      obsDorOutros: json['obsDorOutros'] as String?,
    )
      ..temperatura = json['temperatura'] as String?
      ..dx = (json['dx'] as num?)?.toInt();

Map<String, dynamic> _$RequestClinicEvaluationToJson(
        RequestClinicEvaluation instance) =>
    <String, dynamic>{
      'temperatura': instance.temperatura,
      'dx': instance.dx,
      'numAtendimento': instance.numAtendimento,
      'frequenciaCardiaca': instance.frequenciaCardiaca,
      'frequenciaRespiratoria': instance.frequenciaRespiratoria,
      'pressaoArterialAlta': instance.pressaoArterialAlta,
      'pressaoArterialBaixa': instance.pressaoArterialBaixa,
      'saturacao': instance.saturacao,
      'ritmos': instance.ritmos,
      'circulacao': instance.circulacao,
      'tipoMarcaPasso': instance.tipoMarcaPasso,
      'acessoVenoso': instance.acessoVenoso,
      'diluicaoVelocidadeInfusao': instance.diluicaoVelocidadeInfusao,
      'aminasVasopressorasQuais': instance.aminasVasopressorasQuais,
      'pupilas': instance.pupilas,
      'glasgow': instance.glasgow,
      'sinaisLaterizacao': instance.sinaisLaterizacao,
      'pele': instance.pele,
      'posOperatorio': instance.posOperatorio,
      'feridaOperatoria': instance.feridaOperatoria,
      'infeccaoFeridaOperatoria': instance.infeccaoFeridaOperatoria,
      'drenos': instance.drenos,
      'estomas': instance.estomas,
      'examesLaboratoriais': instance.examesLaboratoriais,
      'examesImagem': instance.examesImagem,
      'sistemaGastrointestinal': instance.sistemaGastrointestinal,
      'duracaoVentilacaoQtdeDias': instance.duracaoVentilacaoQtdeDias,
      'duracaoVentilacaoQtdeHorasDia': instance.duracaoVentilacaoQtdeHorasDia,
      'observacaoAvaliacaoClinica': instance.observacaoAvaliacaoClinica,
      'funcaoRenalHemodialise': instance.funcaoRenalHemodialise,
      'funcaoRenalDiurese': instance.funcaoRenalDiurese,
      'funcaoRenalNormal': instance.funcaoRenalNormal,
      'diurese': instance.diurese,
      'intubacaoTraqueal': instance.intubacaoTraqueal,
      'mascaraVenturi': instance.mascaraVenturi,
      'traqueostomia': instance.traqueostomia,
      'cateterNasal': instance.cateterNasal,
      'oxigenioPorMascara': instance.oxigenioPorMascara,
      'ventilacaoMecanica': instance.ventilacaoMecanica,
      'arAmbiente': instance.arAmbiente,
      'escalaDor': instance.escalaDor,
      'intensidadeDor': instance.intensidadeDor,
      'dorCabeca': instance.dorCabeca,
      'dorBarriga': instance.dorBarriga,
      'dorMembrosSup': instance.dorMembrosSup,
      'dorMembrosInf': instance.dorMembrosInf,
      'dorLombar': instance.dorLombar,
      'dorToraxPeito': instance.dorToraxPeito,
      'dorDorso': instance.dorDorso,
      'dorPescocoCervical': instance.dorPescocoCervical,
      'dorOutros': instance.dorOutros,
      'obsDorOutros': instance.obsDorOutros,
    };
