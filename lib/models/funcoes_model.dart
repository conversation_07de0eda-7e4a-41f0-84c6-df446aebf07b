import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'funcoes_model.g.dart';

class CodFuncoes {
  static const int MEDICO_SOCORRISTA = 4;
  static const int ENFERMEIRO = 8;
  static const int TECNICO_ENFERMAGEM = 31;
  static const int AUXILIAR_ENFERMAGEM = 1;
  static const int MOTORISTA_SOCORRISTA = 24;
}

@JsonSerializable(anyMap: true)
class FuncoesModel {
  @JsonKey(defaultValue: 0)
  int codFuncao;

  @JsonKey(defaultValue: '')
  String descFuncao;

  @JsonKey(
    defaultValue: <FuncionarioModel>[],
  )
  List<FuncionarioModel> funcionarios;

  FuncoesModel(
      {required this.codFuncao,
      required this.descFuncao,
      required this.funcionarios});

  factory FuncoesModel.fromJson(Map json) => _$FuncoesModelFromJson(json);
  Map<String, dynamic> toJson() => _$FuncoesModelToJson(this);
}
