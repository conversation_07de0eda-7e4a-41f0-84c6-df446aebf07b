// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'attendance_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AttendanceModel _$AttendanceModelFromJson(Map json) => AttendanceModel(
      nome: json['nome'] as String? ?? "",
      rg: json['rg'] as String? ?? "",
      cpf: json['cpf'] as String?,
      idade: (json['idade'] as num?)?.toInt(),
      dataNascimento: json['dataNascimento'] as String? ?? "",
      numAtendimento: (json['numAtendimento'] as num).toInt(),
      codUnimed: (json['codUnimed'] as num).toInt(),
      codVeiculo: json['codVeiculo'] as String? ?? "",
      codTipoCliente: json['codTipoCliente'] as String? ?? "",
      nomeTipoCliente: json['nomeTipoCliente'] as String? ?? "",
      codTipoAtendimento: json['codTipoAtendimento'] as String,
      codStatus: (json['codStatus'] as num).toInt(),
      nomeStatus: json['nomeStatus'] as String,
      unimedCarteira: (json['unimedCarteira'] as num?)?.toInt(),
      codigoCarteira: (json['codigoCarteira'] as num?)?.toInt(),
      dvCarteira: json['dvCarteira'] as String?,
      sintomaAcontecendo: json['sintomaAcontecendo'] as String? ?? "",
      sintomaComecou: json['sintomaComecou'] as String? ?? "",
      sintomaHistorico: json['sintomaHistorico'] as String? ?? "",
      sintomaQuando: json['sintomaQuando'] as String? ?? "",
      descricaoTerapeutica: json['descricaoTerapeutica'] as String? ?? "",
      dataAtendimento: json['dataAtendimento'] as String,
      equipe: (json['equipe'] as List<dynamic>?)
              ?.map((e) => FuncionarioModel.fromJson(e as Map))
              .toList() ??
          const <FuncionarioModel>[],
      hipoteseDiagnostica: json['hipoteseDiagnostica'] as String? ?? "",
      observacaoMedicoRegulador:
          json['observacaoMedicoRegulador'] as String? ?? "",
      observacaoProcedimento: json['observacaoProcedimento'] as String? ?? "",
      enderecoAtendimento: json['enderecoAtendimento'] == null
          ? null
          : AddressModel.fromJson(
              Map<String, dynamic>.from(json['enderecoAtendimento'] as Map)),
      descricaoDestinoPaciente:
          json['descricaoDestinoPaciente'] as String? ?? "",
      codigoDestinoPaciente: json['codigoDestinoPaciente'] as String?,
      materiais: (json['materiais'] as List<dynamic>?)
              ?.map((e) => SupplyAttendance.fromJson(
                  Map<String, dynamic>.from(e as Map)))
              .toList() ??
          const <SupplyAttendance>[],
      enderecoDestino: json['enderecoDestino'] == null
          ? null
          : AddressModel.fromJson(
              Map<String, dynamic>.from(json['enderecoDestino'] as Map)),
      remocao: json['remocao'] as String,
      codDiagnostico: json['codDiagnostico'] as String? ?? "",
      descricaoDiagnostico: json['descricaoDiagnostico'] as String? ?? "",
      observacaoConduta: json['observacaoConduta'] as String? ?? "",
      indentificaoAvClinica: json['indentificaoAvClinica'] as bool? ?? false,
      indentificaoConduta: json['indentificaoConduta'] as bool? ?? true,
      isReclassifica: json['isReclassifica'] as bool? ?? false,
      cod_TipoAtend_Reclas: json['cod_TipoAtend_Reclas'] as String? ?? "",
      solicitaReclassifTipoAtend:
          json['solicitaReclassifTipoAtend'] as bool? ?? false,
      nomeMae: json['nomeMae'] as String? ?? '',
      uniurgProtocoloDoencas: json['uniurgProtocoloDoencas'] == null
          ? null
          : UniurgProtocoloDoencas.fromJson(
              Map<String, dynamic>.from(json['uniurgProtocoloDoencas'] as Map)),
      obsProtocoloDoenca: json['obsProtocoloDoenca'] as String? ?? "",
    );

Map<String, dynamic> _$AttendanceModelToJson(AttendanceModel instance) =>
    <String, dynamic>{
      'nome': instance.nome,
      'rg': instance.rg,
      'cpf': instance.cpf,
      'idade': instance.idade,
      'dataNascimento': instance.dataNascimento,
      'numAtendimento': instance.numAtendimento,
      'codUnimed': instance.codUnimed,
      'codVeiculo': instance.codVeiculo,
      'codTipoCliente': instance.codTipoCliente,
      'nomeTipoCliente': instance.nomeTipoCliente,
      'codTipoAtendimento': instance.codTipoAtendimento,
      'codStatus': instance.codStatus,
      'nomeStatus': instance.nomeStatus,
      'unimedCarteira': instance.unimedCarteira,
      'codigoCarteira': instance.codigoCarteira,
      'dvCarteira': instance.dvCarteira,
      'sintomaAcontecendo': instance.sintomaAcontecendo,
      'sintomaComecou': instance.sintomaComecou,
      'sintomaHistorico': instance.sintomaHistorico,
      'sintomaQuando': instance.sintomaQuando,
      'descricaoTerapeutica': instance.descricaoTerapeutica,
      'dataAtendimento': instance.dataAtendimento,
      'equipe': instance.equipe.map((e) => e.toJson()).toList(),
      'observacaoMedicoRegulador': instance.observacaoMedicoRegulador,
      'hipoteseDiagnostica': instance.hipoteseDiagnostica,
      'observacaoProcedimento': instance.observacaoProcedimento,
      'descricaoDestinoPaciente': instance.descricaoDestinoPaciente,
      'codigoDestinoPaciente': instance.codigoDestinoPaciente,
      'enderecoAtendimento': instance.enderecoAtendimento?.toJson(),
      'enderecoDestino': instance.enderecoDestino?.toJson(),
      'materiais': instance.materiais.map((e) => e.toJson()).toList(),
      'remocao': instance.remocao,
      'observacaoConduta': instance.observacaoConduta,
      'codDiagnostico': instance.codDiagnostico,
      'descricaoDiagnostico': instance.descricaoDiagnostico,
      'indentificaoAvClinica': instance.indentificaoAvClinica,
      'indentificaoConduta': instance.indentificaoConduta,
      'isReclassifica': instance.isReclassifica,
      'cod_TipoAtend_Reclas': instance.cod_TipoAtend_Reclas,
      'solicitaReclassifTipoAtend': instance.solicitaReclassifTipoAtend,
      'nomeMae': instance.nomeMae,
      'uniurgProtocoloDoencas': instance.uniurgProtocoloDoencas?.toJson(),
      'obsProtocoloDoenca': instance.obsProtocoloDoenca,
    };
