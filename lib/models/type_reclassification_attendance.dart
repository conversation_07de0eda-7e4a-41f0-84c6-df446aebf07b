import 'package:json_annotation/json_annotation.dart';

part 'type_reclassification_attendance.g.dart';

@JsonSerializable(anyMap: true, explicitToJson: true)
class TypeReclassificationAttendanceModel {
  final String codTipoAtend;
  final String nomeTipoAtend;

  TypeReclassificationAttendanceModel({
    required this.codTipoAtend,
    required this.nomeTipoAtend,
  });

  factory TypeReclassificationAttendanceModel.fromJson(Map json) =>
      _$TypeReclassificationAttendanceModelFromJson(json);
  Map<String, dynamic> toJson() =>
      _$TypeReclassificationAttendanceModelToJson(this);
}
