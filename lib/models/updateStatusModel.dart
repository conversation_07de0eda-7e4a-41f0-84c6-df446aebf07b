class UpdateStatusModel {
  String? numAtendimento;
  int? codStatus;
  String? codMotivoNaoAtendimento;
  String? observacaoMotivoNaoAtendimento;

  UpdateStatusModel(
      {this.numAtendimento,
      this.codStatus,
      this.codMotivoNaoAtendimento,
      this.observacaoMotivoNaoAtendimento});

  UpdateStatusModel.fromJson(Map<String, dynamic> json) {
    numAtendimento = json['numAtendimento'];
    codStatus = json['codStatus'];
    codMotivoNaoAtendimento = json['codMotivoNaoAtendimento'];
    observacaoMotivoNaoAtendimento = json['observacaoMotivoNaoAtendimento'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['numAtendimento'] = this.numAtendimento;
    data['codStatus'] = this.codStatus;
    data['codMotivoNaoAtendimento'] = this.codMotivoNaoAtendimento;
    data['observacaoMotivoNaoAtendimento'] =
        this.observacaoMotivoNaoAtendimento;
    return data;
  }
}