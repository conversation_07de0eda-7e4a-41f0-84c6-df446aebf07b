class AttachmentImageModel {
  late String nomeArquivo;
  late String arquivoBase64;

  AttachmentImageModel(
      {required this.nomeArquivo, required this.arquivoBase64});

  AttachmentImageModel.fromJson(Map<String, dynamic> json) {
    nomeArquivo = json['nomeArquivo'];
    arquivoBase64 = json['arquivoBase64'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['nomeArquivo'] = this.nomeArquivo;
    data['arquivoBase64'] = this.arquivoBase64;
    return data;
  }
}
