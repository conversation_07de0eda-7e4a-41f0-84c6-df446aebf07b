class DelayReasonModel {
  int delayReasonCode;
  String delayReasonName;

  DelayReasonModel(
      {required this.delayReasonCode, required this.delayReasonName});

  factory DelayReasonModel.fromJson(Map<String, dynamic> json) {
    return DelayReasonModel(
      delayReasonCode: json['delayReasonCode'],
      delayReasonName: json['delayReasonName'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'delayReasonCode': delayReasonCode,
      'delayReasonName': delayReasonName,
    };
  }
}
