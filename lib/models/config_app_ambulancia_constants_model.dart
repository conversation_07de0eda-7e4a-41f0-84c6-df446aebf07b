class ConfigAppAmbulanciaConstants {
  final Nervous nervous;
  final Cardiovascular cardiovascular;
  final ImageQuality imageQuality;
  final HospitalDischarge? hospitalDischarge;

  ConfigAppAmbulanciaConstants({
    required this.nervous,
    required this.cardiovascular,
    required this.imageQuality,
    this.hospitalDischarge,
  });

  factory ConfigAppAmbulanciaConstants.fromJson(Map<String, dynamic> json) {
    return ConfigAppAmbulanciaConstants(
      nervous: Nervous.fromJson(json['nervous']),
      cardiovascular: Cardiovascular.fromJson(json['cardiovascular']),
      imageQuality: ImageQuality.fromJson(json['imageQuality']),
      hospitalDischarge: json['hospitalDischarge'] != null
          ? HospitalDischarge.fromJson(json['hospitalDischarge'])
          : HospitalDischarge(
              hospitalDischargeButtonVisible: true,
              optionOutherCode: OPTION_OUTHER_CODE,
              optionOutherNewFrameCode: OPTION_OUTHER_NEW_FRAME_CODE,
            ),
    );
  }
}

class Nervous {
  final int glasgowComaScaleMin;
  final int glasgowComaScaleMax;

  Nervous({
    required this.glasgowComaScaleMin,
    required this.glasgowComaScaleMax,
  });

  factory Nervous.fromJson(Map<String, dynamic> json) {
    return Nervous(
      glasgowComaScaleMin: json['glasgowComaScaleMin'],
      glasgowComaScaleMax: json['glasgowComaScaleMax'],
    );
  }
}

class Cardiovascular {
  final int heartRateMin;
  final int heartRateMax;
  final int respiratoryRateMin;
  final int respiratoryRateMax;
  final int bloodPressureAMin;
  final int bloodPressureAMax;
  final int bloodPressureBMin;
  final int bloodPressureBMax;
  final int oxygenSaturationMin;
  final int oxygenSaturationMax;
  final int bloodGlucoseMin;
  final int bloodGlucoseMax;
  final double temperatureMin;
  final double temperatureMax;

  Cardiovascular({
    required this.heartRateMin,
    required this.heartRateMax,
    required this.respiratoryRateMin,
    required this.respiratoryRateMax,
    required this.bloodPressureAMin,
    required this.bloodPressureAMax,
    required this.bloodPressureBMin,
    required this.bloodPressureBMax,
    required this.oxygenSaturationMin,
    required this.oxygenSaturationMax,
    required this.bloodGlucoseMin,
    required this.bloodGlucoseMax,
    required this.temperatureMin,
    required this.temperatureMax,
  });

  factory Cardiovascular.fromJson(Map<String, dynamic> json) {
    return Cardiovascular(
      heartRateMin: json['heartRateMin'],
      heartRateMax: json['heartRateMax'],
      respiratoryRateMin: json['respiratoryRateMin'],
      respiratoryRateMax: json['respiratoryRateMax'],
      bloodPressureAMin: json['bloodPressureAMin'],
      bloodPressureAMax: json['bloodPressureAMax'],
      bloodPressureBMin: json['bloodPressureBMin'],
      bloodPressureBMax: json['bloodPressureBMax'],
      oxygenSaturationMin: json['oxygenSaturationMin'],
      oxygenSaturationMax: json['oxygenSaturationMax'],
      bloodGlucoseMin: json['bloodGlucoseMin'],
      bloodGlucoseMax: json['bloodGlucoseMax'],
      temperatureMin: json['temperatureMin'].toDouble(),
      temperatureMax: json['temperatureMax'].toDouble(),
    );
  }
}

class ImageQuality {
  final int maxHeightDefault;
  final int maxQualityDefault;

  ImageQuality({
    required this.maxHeightDefault,
    required this.maxQualityDefault,
  });

  factory ImageQuality.fromJson(Map<String, dynamic> json) {
    return ImageQuality(
      maxHeightDefault: json['maxHeightDefault'],
      maxQualityDefault: json['maxQualityDefault'],
    );
  }
}

const OPTION_OUTHER_CODE = 9;

const OPTION_OUTHER_NEW_FRAME_CODE = 4;

class HospitalDischarge {
  final bool hospitalDischargeButtonVisible;
  final int optionOutherCode;
  final int optionOutherNewFrameCode;

  HospitalDischarge({
    required this.hospitalDischargeButtonVisible,
    required this.optionOutherCode,
    required this.optionOutherNewFrameCode,
  });

  factory HospitalDischarge.fromJson(Map<String, dynamic> json) {
    return HospitalDischarge(
      hospitalDischargeButtonVisible:
          json['hospitalDischargeButtonVisible'] ?? false,
      optionOutherCode: json['optionOutherCode'] ?? OPTION_OUTHER_CODE,
      optionOutherNewFrameCode:
          json['optionOutherNewFrameCode'] ?? OPTION_OUTHER_NEW_FRAME_CODE,
    );
  }
}
