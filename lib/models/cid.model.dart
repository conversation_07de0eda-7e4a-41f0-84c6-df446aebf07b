import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/cid.table.dart';

class CidModel {
  String? codCid;
  String? dvCid;
  String? descricao;

  CidModel({this.codCid, this.dvCid, this.descricao});

  CidModel.fromJson(Map<String, dynamic> json) {
    codCid = json['codCid'];
    dvCid = json['dvCid'];
    descricao = json['descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['codCid'] = this.codCid;
    data['dvCid'] = this.dvCid;
    data['descricao'] = this.descricao;
    return data;
  }
}

class RequestCidModel {
  String? descTerapeutica;
  String? obsConduta;
  CidRecordSQLite? cidRecordSQLite;

  RequestCidModel(
      {this.descTerapeutica, this.obsConduta, this.cidRecordSQLite});

  RequestCidModel.fromJson(Map<String, dynamic> json) {
    descTerapeutica = json['descTerapeutica'];
    obsConduta = json['obsConduta'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['descTerapeutica'] = this.descTerapeutica;
    data['obsConduta'] = this.obsConduta;
    return data;
  }
}
