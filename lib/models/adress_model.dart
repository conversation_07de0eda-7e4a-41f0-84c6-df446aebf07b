class AddressModel {
  String? numero;
  String? tipoLogradouro;
  String? bairro;
  String? cidade;
  String? cep;
  String? complemento;
  String? logradouro;
  String? uf;
  String? tipoEndereco;

  AddressModel({
    this.numero,
    this.tipoLogradouro,
    this.bairro,
    this.cidade,
    this.cep,
    this.complemento,
    this.logradouro,
    this.uf,
    this.tipoEndereco,
  });
  isValid() => logradouro == null ||
          cidade == null ||
          tipoLogradouro == null ||
          numero == null
      ? false
      : true;

  get isAttendance => tipoEndereco == 'ATTENDANCE';
  get isDestiny => tipoEndereco == 'DESTINY';

  AddressModel.fromJson(Map<String, dynamic> json) {
    numero = json['numero'];
    tipoLogradouro = json['tipoLogradouro'];
    bairro = json['bairro'];
    cidade = json['cidade'];
    cep = json['CEP'];
    complemento = json['complemento'];
    logradouro = json['logradouro'];
    uf = json['uf'];
    tipoEndereco = json['tipoEndereco'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['numero'] = this.numero;
    data['tipoLogradouro'] = this.tipoLogradouro;
    data['bairro'] = this.bairro;
    data['cidade'] = this.cidade;
    data['CEP'] = this.cep;
    data['complemento'] = this.complemento;
    data['logradouro'] = this.logradouro;
    data['uf'] = this.uf;
    data['tipoEndereco'] = this.tipoEndereco;
    return data;
  }
}
