import 'dart:async';

import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/geolocation-config.model.dart';
import 'package:ambulancia_app/shared/api/websocket.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

class GeolocationService {
  final logger = UnimedLogger(className: 'GeolocationService');

  Position? _position;
  /*  late Stream<Position> _positionStream; */
  StreamSubscription<Position>? _positionStreamListen;
  Timer? _timerGeolocationLog;
  GeolocationService();
  final GeolocatorPlatform _geolocatorPlatform = GeolocatorPlatform.instance;

  listenPositionStream({
    required BuildContext context,
    required GeolocationConfigModel geolocationConfigModel,
    AttendanceModel? attendance,
    Function(Position)? onUpdate,
  }) async {
/*     await _positionStreamListen?.cancel(); */
    late LocationSettings locationSettings;

    final hasPermission = await _handlePermission();

    if (defaultTargetPlatform == TargetPlatform.android) {
      locationSettings = AndroidSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: geolocationConfigModel.distanceFilter!,
          forceLocationManager: true,
          intervalDuration: const Duration(minutes: 1),
          foregroundNotificationConfig: const ForegroundNotificationConfig(
            notificationText:
                "O aplicativo ambulancia está usando a localização em segundo plano",
            notificationTitle: "Executando em segundo plano",
            enableWakeLock: true,
          ));
    } else {
      locationSettings = LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: geolocationConfigModel.distanceFilter!,
      );
    }

    if (!hasPermission) {
      return;
    }

    _positionStreamListen =
        Geolocator.getPositionStream(locationSettings: locationSettings)
            .listen((position) async {
      if (_timerGeolocationLog != null && _timerGeolocationLog!.isActive) {
        _timerGeolocationLog!.cancel();
      }

      startTimer(context, () {
        Locator.instance.get<WebSocketApi>().setAmbulancePositionToLog(
              lat: '${position.latitude}',
              long: '${position.longitude}',
              attendance: attendance!,
            );
      });
    });

    /* _positionStream = Geolocator.getPositionStream(
        distanceFilter: geolocationConfigModel.distanceFilter!,
        intervalDuration:
            Duration(seconds: geolocationConfigModel.timeIntervalSec!)); */
    /*    _positionStreamListen = _positionStream.listen((position) {
      Locator.instance!.get<WebSocketApi>().setAmbulancePosition(
            lat: '${position.latitude}',
            long: '${position.longitude}',
            attendance: attendance!,
          );
    }); */
  }

  Future<void> cancel() async {
    _positionStreamListen?.cancel();
  }

  Future<void> requestPermission() async {
    PermissionStatus permission = await Permission.location.status;

    if (permission == PermissionStatus.denied) {
      permission = await Permission.location.request();
      logger.d('statusRequest : $permission');
      if (permission == PermissionStatus.denied)
        logger
            .e('_initSocketGeolocation Permissão não concedida : $permission');
    }
  }

  Future<bool> openAppConfig() async {
    return Geolocator.openAppSettings();
  }

  Future<Position?> getCurrentPosition({bool force = false}) async {
    if (force || _position == null) {
      _position = await Geolocator.getCurrentPosition();
      //.getLastKnownPosition(desiredAccuracy: LocationAccuracy.best);
    }

    logger.d('getCurrentPosition: $_position');
    return _position;
  }

  void cleanData() {
    _position = null;
  }

  void startTimer(BuildContext context, Function callback) {
    const duration = const Duration(minutes: 1);
    _timerGeolocationLog = Timer.periodic(duration, (Timer timer) {
      callback();
    });
  }

  Future<bool> _handlePermission() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await _geolocatorPlatform.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return false;
    }

    permission = await _geolocatorPlatform.checkPermission();
    if (permission == LocationPermission.denied) {
      return false;
    }

    if (permission == LocationPermission.deniedForever) {
      return false;
    }

    return true;
  }
}
