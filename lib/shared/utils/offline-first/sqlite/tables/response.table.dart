import 'dart:convert';

import 'package:ambulancia_app/models/reponse-table.model.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/sqlite_manager.dart';
import 'package:sqflite/sqflite.dart';

class ResponseTableSQLite<T extends ResponseRecordSQLite>
    extends SQLiteTableObject<T> {
  final logger = UnimedLogger(className: 'ResponseTableSQLite');
  ResponseTableSQLite()
      : super(
            tableName:
                'tbResponse${const String.fromEnvironment('environment') == "PROD" ? '_prod' : '_dev'}');

  static String createSql =
      'CREATE TABLE IF NOT EXISTS tbResponse${const String.fromEnvironment('environment') == "PROD" ? '_prod' : '_dev'} ' +
          '(' +
          '${ResponseTableSQLiteColums.ID} INTEGER PRIMARY KEY AUTOINCREMENT,' +
          '${ResponseTableSQLiteColums.CUSTOM_ID} TEXT NOT NULL ,' +
          '${ResponseTableSQLiteColums.VALUE} TEXT NOT NULL,  ' +
          '${ResponseTableSQLiteColums.CATEGORY} TEXT NOT NULL ' +
          ')';

  Future<int?> persistData({required ResponseRecordSQLite dataObject}) async {
    Database _database = await database() as Database;
    try {
      final jsonDataTable = dataObject.toMap();
      var result = _database.insert(
        tableName,
        jsonDataTable,
      );
      logger.i('persistData realizado $jsonDataTable');

      return result;
    } catch (e) {
      logger.e('persistData error $e');
    }
    return null;
  }

  Future<List<ResponseRecordSQLite>?> loadByCategory(
      {required category}) async {
    Database _database = await database() as Database;
    try {
      final query = await _database.query(
        tableName,
        where: '${ResponseTableSQLiteColums.CATEGORY} = ?',
        whereArgs: [category],
      );
      if (query.length <= 0) return null;
      List<ResponseRecordSQLite> results = query
          .map<ResponseRecordSQLite>(
              (request) => ResponseRecordSQLite.fromMap(request))
          .toList();

      logger.i('loadByCategory realizado $query');

      return results;
    } catch (e) {
      logger.e('loadByCategory error $e');
      return null;
    }
  }

  Future<List<ResponseRecordSQLite>?> loadAll() async {
    Database _database = await database() as Database;
    try {
      final query = await _database.query(
        tableName,
      );
      if (query.length <= 0) return null;
      logger.i('loadAll realizado');
      List<ResponseRecordSQLite> results = query
          .map<ResponseRecordSQLite>(
              (request) => ResponseRecordSQLite.fromMap(request))
          .toList();
      return results;
    } catch (e) {
      logger.e('loadAll error $e');
      return null;
    }
  }

  Future<int?> delete({required int id}) async {
    Database _database = await database() as Database;
    try {
      final query = await _database.delete(tableName,
          where: '${ResponseTableSQLiteColums.ID} = ?', whereArgs: [id]);
      logger.i('delete realizado id: $id');
      return query;
    } catch (e) {
      logger.e('delete error $e');
      return null;
    }
  }

  Future<ResponseRecordSQLite?> getByCustomId(
      {required String customId}) async {
    Database _database = await database() as Database;
    try {
      final query = await _database.query(
        tableName,
        where: '${ResponseTableSQLiteColums.CUSTOM_ID} = ?',
        whereArgs: [customId],
      );
      if (query.length <= 0) return null;
      List<ResponseRecordSQLite> results = query
          .map<ResponseRecordSQLite>(
              (request) => ResponseRecordSQLite.fromMap(request))
          .toList();

      ResponseRecordSQLite result = results[0];
      logger.i('getByCustomId realizado $query');

      return result;
    } catch (e) {
      logger.e('getByCustomId error $e');
      return null;
    }
  }

  @override
  Future<void> addOrUpdate(T record) async {
    logger.i('addOrUpdate iniciado');

    try {
      Database _database = await database() as Database;
      final query = await _database.query(
        tableName,
        where: '${ResponseTableSQLiteColums.CUSTOM_ID} = ?',
        whereArgs: [record.customId],
      );

      if (query.length <= 0) {
        final jsonDataTable = record.toMap();
        _database.insert(
          tableName,
          jsonDataTable,
        );
      } else {
        final jsonDataTable = record.toMap();

        _database.update(
          tableName,
          jsonDataTable,
          where: '${ResponseTableSQLiteColums.CUSTOM_ID} = ?',
          whereArgs: [record.customId],
        );
      }
      logger.i('getByCustomId realizado ');
    } catch (e) {
      logger.e('getByCustomId erro $e ');
    }
  }
}

class ResponseRecordSQLite extends SQLiteRecordObject {
  final int? id;
  final String value;
  final String customId;
  final String category;

  ResponseRecordSQLite({
    this.id,
    required this.value,
    required this.customId,
    required this.category,
  });

  Map<String, dynamic> toMap() {
    return {
      '${ResponseTableSQLiteColums.VALUE}': value,
      '${ResponseTableSQLiteColums.CUSTOM_ID}': customId,
      '${ResponseTableSQLiteColums.CATEGORY}': category,
    };
  }

  /// Converte registro sqlite para object
  static ResponseRecordSQLite fromMap(Map<String, dynamic> map) {
    return ResponseRecordSQLite(
        id: map[ResponseTableSQLiteColums.ID],
        value: map[ResponseTableSQLiteColums.VALUE],
        customId: map[ResponseTableSQLiteColums.CUSTOM_ID],
        category: map[ResponseTableSQLiteColums.CATEGORY]);
  }

  @override
  String toString() {
    return jsonEncode(toMap());
  }

  static ResponseRecordSQLite fromSyncModel(ResponseTableModel response) {
    return ResponseRecordSQLite(
        customId: response.customId,
        value: response.value,
        category: response.category);
  }
}

abstract class ResponseTableSQLiteColums {
  static const ID = 'id';
  static const VALUE = 'value';
  static const CUSTOM_ID = 'custom_id';
  static const CATEGORY = 'category';
}
