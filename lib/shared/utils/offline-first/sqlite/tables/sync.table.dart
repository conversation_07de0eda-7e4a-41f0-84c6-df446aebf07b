// TODOsync table

import 'dart:convert';

import 'package:ambulancia_app/models/sync.model.dart';
import 'package:ambulancia_app/shared/services/sync-offline.service.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/sqlite_manager.dart';
import 'package:sqflite/sqflite.dart';

class SyncTableSQLite<T extends SyncRecordSQLite> extends SQLiteTableObject<T> {
  final logger = UnimedLogger(className: 'SyncTableSQLite');
  SyncTableSQLite()
      : super(
            tableName:
                'tbSync${const String.fromEnvironment('environment') == "PROD" ? '_prod' : '_dev'}');

  static String createSql =
      'CREATE TABLE IF NOT EXISTS tbSync${const String.fromEnvironment('environment') == "PROD" ? '_prod' : '_dev'} ' +
          '(' +
          '${SyncTableSQLiteColums.ID} INTEGER PRIMARY KEY AUTOINCREMENT,' +
          '${SyncTableSQLiteColums.CUSTOM_ID} TEXT NOT NULL ,' +
          '${SyncTableSQLiteColums.URL} TEXT NOT NULL, ' +
          '${SyncTableSQLiteColums.HEADER} TEXT ,' +
          '${SyncTableSQLiteColums.TYPE_REQUEST} TEXT NOT NULL, ' +
          '${SyncTableSQLiteColums.JSON} TEXT, ' +
          '${SyncTableSQLiteColums.PATH_FILE} TEXT, ' +
          '${SyncTableSQLiteColums.FILE_NAME} TEXT, ' +
          '${SyncTableSQLiteColums.AUTHORIZATION} TEXT, ' +
          '${SyncTableSQLiteColums.JSON_FIELDS} TEXT ' +
          ')';

  Future<int?> persistData({required SyncRecordSQLite dataObject}) async {
    Database _database = await database() as Database;
    try {
      final jsonDataTable = dataObject.toMap();
      var result = _database.insert(
        tableName,
        jsonDataTable,
      );
      logger.i('persistData realizado $jsonDataTable');
      return result;
    } catch (e) {
      logger.e('persistData error $e');
    }
    return null;
  }

  Future<int?> persistDataConduct(
      {required SyncRecordSQLite dataObject}) async {
    Database _database = await database() as Database;
    try {
      final jsonDataTable = dataObject.toMap();
      var result;
      final query = await _database.query(
        tableName,
        where: '${SyncTableSQLiteColums.CUSTOM_ID} = ?',
        whereArgs: [dataObject.customId],
      );

      if (query.length <= 0) {
        final jsonDataTable = dataObject.toMap();
        result = _database.insert(
          tableName,
          jsonDataTable,
        );
      } else {
        _database.update(
          tableName,
          jsonDataTable,
          where: '${SyncTableSQLiteColums.CUSTOM_ID} = ?',
          whereArgs: [dataObject.customId],
        );
      }

      logger.i('persistDataConduct realizado $jsonDataTable');
      return result;
    } catch (e) {
      logger.e('persistDataConduct error $e');
    }
    return null;
  }

  Future<List<SyncRecordSQLite>?> loadAll() async {
    Database _database = await database() as Database;
    try {
      final query = await _database.query(
        tableName,
      );
      logger.i('loadAll realizado: ' +
          query.length.toString() +
          " registros encontrados");
      if (query.length <= 0) return null;
      List<SyncRecordSQLite> results = query
          .map<SyncRecordSQLite>((request) => SyncRecordSQLite.fromMap(request))
          .toList();
      return results;
    } catch (e) {
      logger.e('loadAll error $e');
      return null;
    }
  }

  Future<int?> delete({required int id}) async {
    Database _database = await database() as Database;
    try {
      final query = await _database.delete(tableName,
          where: '${SyncTableSQLiteColums.ID} = ?', whereArgs: [id]);
      logger.i('delete realizado id:$id');
      return query;
    } catch (e) {
      logger.e('delete error $e');
      return null;
    }
  }

  Future<List<SyncRecordSQLite>?> getByCustomId(
      {required String customId}) async {
    Database _database = await database() as Database;
    try {
      final query = await _database.query(
        tableName,
        where: '${SyncTableSQLiteColums.CUSTOM_ID} = ?',
        whereArgs: [customId],
      );
      if (query.length <= 0) return null;
      List<SyncRecordSQLite> results = query
          .map<SyncRecordSQLite>((request) => SyncRecordSQLite.fromMap(request))
          .toList();

      logger.i('getByCustomId realizado $query');

      return results;
    } catch (e) {
      logger.e('getByCustomId error $e');
      return null;
    }
  }

  @override
  Future<void> addOrUpdate(T record) async {
    Database _database = await database() as Database;
    final query = await _database.query(
      tableName,
      where:
          '${SyncTableSQLiteColums.CUSTOM_ID} = ? AND ${SyncTableSQLiteColums.JSON} = ?',
      whereArgs: [record.customId, record.jsonRequest],
    );

    if (query.length <= 0) {
      final jsonDataTable = record.toMap();
      _database.insert(
        tableName,
        jsonDataTable,
      );
    } else {
      final jsonDataTable = record.toMap();

      _database.update(
        tableName,
        jsonDataTable,
        where:
            '${SyncTableSQLiteColums.CUSTOM_ID} = ? AND ${SyncTableSQLiteColums.JSON} = ?',
        whereArgs: [record.customId, record.jsonRequest],
      );
    }
  }
}

class SyncRecordSQLite extends SQLiteRecordObject {
  final int? id;
  final String customId;
  final String? jsonRequest;
  final Map<String, String>? header;
  final String url;
  final SYNC_TYPE_REQUEST typeRequest;
  final String? pathFile;
  final String? fileName;
  final String? authorization;
  final String? jsonFields;

  SyncRecordSQLite({
    this.id,
    required this.customId,
    this.jsonRequest,
    required this.url,
    this.header,
    required this.typeRequest,
    this.pathFile,
    this.fileName,
    this.authorization,
    this.jsonFields,
  });

  Map<String, dynamic> toMap() {
    return {
      '${SyncTableSQLiteColums.CUSTOM_ID}': customId,
      '${SyncTableSQLiteColums.JSON}': jsonRequest,
      '${SyncTableSQLiteColums.HEADER}':
          header != null ? jsonEncode(header) : null,
      '${SyncTableSQLiteColums.URL}': url,
      '${SyncTableSQLiteColums.TYPE_REQUEST}': typeRequest.name,
      '${SyncTableSQLiteColums.PATH_FILE}': pathFile,
      '${SyncTableSQLiteColums.FILE_NAME}': fileName,
      '${SyncTableSQLiteColums.AUTHORIZATION}': authorization,
      '${SyncTableSQLiteColums.JSON_FIELDS}': jsonFields,
    };
  }

  /// Converte registro sqlite para object
  static SyncRecordSQLite fromMap(Map<String, dynamic> map) {
    return SyncRecordSQLite(
      id: map[SyncTableSQLiteColums.ID],
      customId: map[SyncTableSQLiteColums.CUSTOM_ID],
      jsonRequest: map[SyncTableSQLiteColums.JSON],
      header: decodeStringToMap(map[SyncTableSQLiteColums.HEADER]),
      url: map[SyncTableSQLiteColums.URL],
      typeRequest:
          _convertType(requiredtype: map[SyncTableSQLiteColums.TYPE_REQUEST]),
      pathFile: map[SyncTableSQLiteColums.PATH_FILE] ?? '',
      fileName: map[SyncTableSQLiteColums.FILE_NAME] ?? '',
      authorization: map[SyncTableSQLiteColums.AUTHORIZATION] ?? '',
      jsonFields: map[SyncTableSQLiteColums.JSON_FIELDS] ?? '',
    );
  }

  static Map<String, String> decodeStringToMap(String jsonString) {
    Map<String, dynamic> decoded = jsonDecode(jsonString);
    Map<String, String> map = {};

    decoded.forEach((key, value) {
      map[key] = value.toString();
    });

    return map;
  }

  static SYNC_TYPE_REQUEST _convertType({required requiredtype}) {
    late SYNC_TYPE_REQUEST type;
    switch (requiredtype) {
      case 'POST':
        type = SYNC_TYPE_REQUEST.POST;
        break;
      case 'GET':
        type = SYNC_TYPE_REQUEST.GET;
        break;
      case 'PUT':
        type = SYNC_TYPE_REQUEST.PUT;
        break;
      case 'DELETE':
        type = SYNC_TYPE_REQUEST.DELETE;
        break;
      case 'PATCH':
        type = SYNC_TYPE_REQUEST.PATCH;
        break;
      case 'POST_WITH_FILE':
        type = SYNC_TYPE_REQUEST.POST_WITH_FILE;
        break;
    }
    return type;
  }

  @override
  String toString() {
    return jsonEncode(toMap());
  }

  static SyncRecordSQLite fromSyncModel(SyncModel sync) {
    return SyncRecordSQLite(
        customId: sync.customId,
        jsonRequest: sync.jsonRequest,
        url: sync.url,
        header: sync.header,
        typeRequest: sync.typeRequest,
        authorization: sync.authorization,
        fileName: sync.fileName,
        jsonFields: sync.jsonFields,
        pathFile: sync.pathFile);
  }
}

abstract class SyncTableSQLiteColums {
  static const ID = 'id';
  static const CUSTOM_ID = 'custom_id';
  static const JSON = 'json_request';
  static const HEADER = 'header';
  static const URL = 'url';
  static const TYPE_REQUEST = 'type_request';
  static const PATH_FILE = 'path_file';
  static const FILE_NAME = 'file_name';
  static const AUTHORIZATION = 'authorization';
  static const JSON_FIELDS = 'json_fields';
}
