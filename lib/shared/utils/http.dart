import 'dart:convert';
import 'dart:io';

import 'package:http/io_client.dart';

class HttpUtils {
  /// bypass CERTIFICATE_VERIFY_FAILED in android
  static IOClient bypassInvalidCertificate() {
    final ioc = new HttpClient();
    ioc.badCertificateCallback =
        (X509Certificate cert, String host, int port) => true;
    return IOClient(ioc);
  }

  static String getAuthorizationBasicAmbulanciaOsb() {
    return 'Basic ' +
        base64Encode(utf8.encode(
            '${const String.fromEnvironment('ambulanciaOsbUser')}:${const String.fromEnvironment('ambulanciaOsbPassword')}'));
  }
}
