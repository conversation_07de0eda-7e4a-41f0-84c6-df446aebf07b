import 'package:intl/intl.dart';

class DateTimeUtils {
  static getTimeFromDate(DateTime? dateTime) {
    return dateTime != null
        ? DateFormat('dd/MM/yy HH:mm').format(dateTime)
        : null;
  }

  static getTimeFormat({required DateTime dateTime, bool send = false}) {
    return send
        ? DateFormat('dd/MM/yyyy HH:mm:ss').format(dateTime)
        : DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  static String getTimeConvertMovementsModel({required String date}) {
    var inputFormated = DateFormat('dd/MM/yyyy HH:mm:ss');
    var inputDate = inputFormated.parse(date);

    var outputFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    var outputDate = outputFormat.format(inputDate);
    return outputDate;
  }
}
