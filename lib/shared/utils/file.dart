import 'dart:io';
import 'package:flutter_image_compress/flutter_image_compress.dart';

class FileUtils {
  static void deleteFile(File file) {
    try {
      if (file.existsSync()) {
        print('file path ${file.path}');
        file.delete();
      }
    } catch (e) {
      print('Unable to delete file $e');
    }
  }

  static Future<File?> getCompressedFile(File file) async {
    try {
      final filePath = file.absolute.path;
      final lastIndex = filePath.lastIndexOf('.');
      final splitted = filePath.substring(0, (lastIndex));
      final targetPath = "${splitted}_out${filePath.substring(lastIndex)}";
      final result = await FlutterImageCompress.compressAndGetFile(
        filePath,
        targetPath,
        quality: 10,
        minHeight: 75,
      );

      return File(result!.path);
    } catch (e) {
      print('Unable to compress a photo');

      print(e);
      return file;
    }
  }
}
