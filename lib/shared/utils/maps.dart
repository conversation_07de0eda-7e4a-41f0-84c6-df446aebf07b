// TODO: Temporary disabled

//import 'package:google_maps_flutter/google_maps_flutter.dart';
// import 'package:flutter/services.dart';
// import 'dart:ui' as ui;

// enum ICONS { AMBULANCE, PERSON, HOSPITAL }

// class MapsUtil {
//   static LatLngBounds getLatLngBounds(List<LatLng> points) {
//     final double minLat = points
//         .reduce((curr, next) => curr.latitude < next.latitude ? curr : next)
//         .latitude;
//     final double minLng = points
//         .reduce((curr, next) => curr.longitude < next.longitude ? curr : next)
//         .longitude;

//     final double maxLat = points
//         .reduce((curr, next) => curr.latitude >= next.latitude ? curr : next)
//         .latitude;
//     final double maxLng = points
//         .reduce((curr, next) => curr.longitude >= next.longitude ? curr : next)
//         .longitude;

//     return LatLngBounds(
//       northeast: LatLng(maxLat, maxLng),
//       southwest: LatLng(minLat, minLng),
//     );
//   }

//   static Future<BitmapDescriptor?> getBitmapDescriptorPrestador(
//       String type, int width) async {
//     if (type == 'clinicas')
//       return BitmapDescriptor.fromBytes(await getBytesFromAsset(
//           'assets/images/pin_maps/clinica_pin.png', width));

//     if (type == 'clinicasUnimed' ||
//         type == 'clinicaUnimed' ||
//         type == 'clinicaAis')
//       return BitmapDescriptor.fromBytes(await getBytesFromAsset(
//           'assets/images/pin_maps/clinica_unimed_pin.png', width));

//     if (type == 'hospitais')
//       return BitmapDescriptor.fromBytes(await getBytesFromAsset(
//           'assets/images/pin_maps/hospital_pin.png', width));

//     if (type == 'medicos')
//       return BitmapDescriptor.fromBytes(await getBytesFromAsset(
//           'assets/images/pin_maps/medico_pin.png', width));

//     if (type == 'laboratorios')
//       return BitmapDescriptor.fromBytes(
//           await getBytesFromAsset('assets/images/pin_maps/lab_pin.png', width));

//     if (type == 'banco_sangue')
//       return BitmapDescriptor.fromBytes(await getBytesFromAsset(
//           'assets/images/pin_maps/sangue_pin.png', width));
//     return null;
//   }

//   static Future<BitmapDescriptor?> getBitmapDescriptor(ICONS iconName,
//       {int width = 100}) async {
//     if (iconName == ICONS.AMBULANCE) {
//       return BitmapDescriptor.fromBytes(await getBytesFromAsset(
//           'assets/images/pin_maps/ambulance_pin.png', width));
//     } else if (iconName == ICONS.PERSON) {
//       return BitmapDescriptor.fromBytes(
//           await getBytesFromAsset('assets/images/pin_maps/person.png', width));
//     } else if (iconName == ICONS.HOSPITAL) {
//       return BitmapDescriptor.fromBytes(await getBytesFromAsset(
//           'assets/images/pin_maps/hospital_pin.png', width));
//     }

//     return null;
//   }

//   static Future<Uint8List> getBytesFromAsset(String path, int width) async {
//     ByteData data = await rootBundle.load(path);
//     ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
//         targetWidth: width);
//     ui.FrameInfo fi = await codec.getNextFrame();
//     return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
//         .buffer
//         .asUint8List();
//   }
// }
