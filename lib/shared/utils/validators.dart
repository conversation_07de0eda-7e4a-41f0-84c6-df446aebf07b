import 'package:ambulancia_app/shared/utils/string_utils.dart';

abstract class TextFieldValidators {
  static String? cpf(String? value) {
    Pattern pattern = r'^\d{3}\.\d{3}\.\d{3}\-\d{2}$';
    RegExp regex = new RegExp(pattern as String);

    if (!regex.hasMatch(value!))
      return 'Digite um cpf válido';
    else if (!StringUtils.validateCpf(
        value.replaceAll(new RegExp(r'[^0-9]'), '')))
      return 'Digite um cpf válido';
    else
      return null;
  }
}
