// ignore: camel_case_types
enum SYNC_CATEGORY_API {
  AttendanceApi,
  Supplies<PERSON>pi,
  <PERSON><PERSON><PERSON><PERSON>,
  Conduct<PERSON><PERSON>,
  ClinicEvaluationApi,
  ConductAttachmentApi,
  ConductAttachmentDownloadsImgApi
}

// ignore: camel_case_types
enum SYNC_CONDUCTAPI_REQUEST { sendConduct }

// ignore: camel_case_types
enum SYNC_ATTENDANCEAPI_REQUEST {
  getAttendances,
  getAttendanceDetail,
  getListAttendance,
  updateStatusMovements,
  getAttendanceMovements,
  listDestinies,
  updateDestiny,
  getAttachments,
  getAttachmentsDownloadsImg
}

// ignore: camel_case_types
enum SYNC_CLINICEVALUATIONAPI_REQUEST {
  getClinicEvaluationConfig,
  getClinicEvaluationData,
  sendClinicEvaluation
}
