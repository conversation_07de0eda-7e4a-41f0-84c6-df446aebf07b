import 'dart:convert';

import 'package:ambulancia_app/models/delay_reason_model.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:http/http.dart' as http;

class DelayReasonApi {
  final UnimedHttpClient httpClient;
  final logger = UnimedLogger(className: 'DelayReasonApi');

  DelayReasonApi(this.httpClient);

  Future<List<DelayReasonModel>> getDelayReasons() async {
    final endpoint = 'ambulance/delay-reason';
    final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

    try {
      final String? token =
          await Locator.instance.get<AuthApi>().tokenPerfilApps();

      final Map<String, String> headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };
      http.Response response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final bodyRetorno = jsonDecode(response.body);
        logger.d('getDelayReasons retorno : $bodyRetorno');
        return (bodyRetorno as List)
            .map<DelayReasonModel>((sa) => DelayReasonModel.fromJson(sa))
            .toList();
      } else {
        logger.e('getDelayReasons found');
        throw MotiveException(MessageException.general);
      }
    } on ServiceTimeoutException catch (ex) {
      logger.e(
          'getDelayReasons - Error AttendanceApi ServiceTimeoutException $ex');
      throw ServiceTimeoutException();
    } on MotiveException catch (ex) {
      logger.i('getDelayReasons AutorizacoesException ${ex.message}');
      throw MotiveException(MessageException.general);
    } on NoInternetException catch (ex) {
      logger.i('getDelayReasons NoInternetException ${ex.message}');
      throw NoInternetException();
    } on NotFoundException catch (ex) {
      logger.i('getDelayReasons NotFoundException ${ex.message}');
      throw NotFoundException();
    } catch (ex) {
      logger.i('getDelayReasons exception $ex');
      throw MotiveException(MessageException.general);
    }
  }
}
