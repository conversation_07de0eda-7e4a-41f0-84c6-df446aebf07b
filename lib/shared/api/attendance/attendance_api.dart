import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:ambulancia_app/models/address-infos.dart';
import 'package:ambulancia_app/models/adress_model.dart';
import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:ambulancia_app/models/close-attendanceV2.model.dart';
import 'package:ambulancia_app/models/destiny_model.dart';
import 'package:ambulancia_app/models/movements_model.dart';
import 'package:ambulancia_app/models/service-update.dart';
import 'package:ambulancia_app/shared/api/attendance/notification_of_amounts_of_data_pending_synchronization.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/services/sync-offline.service.dart';
import 'package:ambulancia_app/shared/utils/datetime_utils.dart';
import 'package:ambulancia_app/shared/utils/http.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/offline-first/last-synchronization-dates/last_synchronization_dates.dart';
import 'package:ambulancia_app/shared/utils/offline-first/offline_first.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/sync.table.dart';
import 'package:ambulancia_app/shared/utils/sync.utils.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';

import '../../locator.dart';
import '../auth.api.dart';

const String delayReasonMessage =
    "Informe o motivo pelo qual a ambulância se atrasou para sair da base";
const String otherReasonMessage =
    "Ao selecionar a opção 'OUTROS' deve ser preenchido o campo Observação do Atraso Saída da Base";

const Set<String> delayReasonMessages = {
  delayReasonMessage,
  otherReasonMessage,
};

class AttendanceApi {
  final UnimedHttpClient httpClient;
  final logger = UnimedLogger(className: 'AttendanceApi');
  final totalRecordsPersist = 'totalRecords';

  AttendanceApi(this.httpClient);

  Future<String?> closeAttendance({
    required String numAtendimento,
    required String pathPhotoOperator,
    required String recordType,
    required bool reclassify,
    String? codTypeReclassify,
    int? delayReasonCode,
    String? delayReasonObservation,
    int? codProtocoloDoenca,
    String? obsProtocoloDoenca,
  }) async {
    final url =
        '${const String.fromEnvironment('perfilAppsUrl')}ambulance/attendance/v2/$numAtendimento/close';
    try {
      final String? token =
          await Locator.instance.get<AuthApi>().tokenPerfilApps();

      File imagem = File(pathPhotoOperator);
      List<int> bytesImagem = imagem.readAsBytesSync();
      String imagemBase64 = base64Encode(bytesImagem);

      //TODO: Create model closeAttendance

      final Map<String, dynamic> json = {
        "reclassify": reclassify,
        if (reclassify) "codTypeReclassify": codTypeReclassify,
        "delayReasonCode": delayReasonCode,
        "delayReasonObservation":
            delayReasonCode != null ? delayReasonObservation : null,
        "image": {
          "fileName": _generateFileName(
              recordType: recordType, serviceNumber: numAtendimento),
          "fileBase64": imagemBase64,
        },
        if (codProtocoloDoenca != null) "codProtocolSignal": codProtocoloDoenca,
        if (obsProtocoloDoenca != null && obsProtocoloDoenca.isNotEmpty)
          "obsSignsOfIllness": obsProtocoloDoenca,
      };

      final Map<String, String> headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      final response = await httpClient.patch(Uri.parse(url),
          body: jsonEncode(json), headers: headers);

      if (response.statusCode == 200 || response.statusCode == 201) {
        logger.i('closeAttendance sucess - statusCode: ${response.statusCode}');

        return '';
      } else if (response.statusCode == 500) {
        final bodyDecode = jsonDecode(response.body);
        String message = bodyDecode['message'] ??
            bodyDecode['mensagem'] ??
            MessageException.general;

        //TODO: Comparison with temporary string
        if (delayReasonMessages.contains(message)) {
          throw CloseAttendanceDelayReasonException(
            message: message,
          );
        } else {
          logger.e(
              'closeAttendance error - statusCode ${response.statusCode} "bodyResponse"');

          throw CloseAttendanceException(message: message);
        }
      } else {
        final bodyDecode = jsonDecode(response.body);
        String message = bodyDecode['message'] ??
            bodyDecode['mensagem'] ??
            MessageException.general;

        throw CloseAttendanceException(message: message);
      }
    } on NoInternetException catch (e) {
      logger.e('closeAttendance error NoInternetException $e');
      throw NoInternetException();
    } on SocketException catch (e) {
      logger.e('closeAttendance error SocketException $e');
      throw NoInternetException();
    } on ServiceTimeoutException catch (e) {
      logger.e('closeAttendance error timeout $e');
      throw ServiceTimeoutException();
    } on TimeoutException catch (e) {
      logger.e('closeAttendance error timeout $e');
      throw ServiceTimeoutException();
    } on CloseAttendanceDelayReasonException catch (e) {
      logger.e('CloseAttendanceDelayReasonException error  $e');

      throw CloseAttendanceDelayReasonException(
        message: e.message ?? MessageException.general,
      );
    } on CloseAttendanceException catch (e) {
      throw CloseAttendanceException(
        message: e.message ?? MessageException.general,
      );
    } catch (e) {
      logger.e('closeAttendance error catch exception: $e');
      throw CloseAttendanceException(message: MessageException.general);
    }
  }

  Future<String?> closeAttendanceV2(
      {required CloseAttendanceV2Model closeAttendanceData}) async {
    final url =
        '${const String.fromEnvironment('perfilAppsUrl')}ambulance/attendance/v3/${closeAttendanceData.numAtendimento}/close';
    try {
      final String? token =
          await Locator.instance.get<AuthApi>().tokenPerfilApps();

      final Map<String, String> headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      final response = await httpClient.patch(Uri.parse(url),
          body: jsonEncode(closeAttendanceData.toJson()), headers: headers);

      if (response.statusCode == 200 || response.statusCode == 201) {
        logger.i(
            'closeAttendanceV2 success - statusCode: ${response.statusCode}');
        return '';
      } else if (response.statusCode == 500) {
        final bodyDecode = jsonDecode(response.body);
        String message = bodyDecode['message'] ??
            bodyDecode['mensagem'] ??
            MessageException.general;

        if (delayReasonMessages.contains(message)) {
          throw CloseAttendanceDelayReasonException(
            message: message,
          );
        } else {
          logger.e(
              'closeAttendanceV2 error - statusCode ${response.statusCode} "bodyResponse"');
          throw CloseAttendanceException(message: message);
        }
      } else {
        final bodyDecode = jsonDecode(response.body);
        String message = bodyDecode['message'] ??
            bodyDecode['mensagem'] ??
            MessageException.general;

        throw CloseAttendanceException(message: message);
      }
    } on NoInternetException catch (e) {
      logger.e('closeAttendanceV2 error NoInternetException $e');
      throw NoInternetException();
    } on SocketException catch (e) {
      logger.e('closeAttendanceV2 error SocketException $e');
      throw NoInternetException();
    } on ServiceTimeoutException catch (e) {
      logger.e('closeAttendanceV2 error timeout $e');
      throw ServiceTimeoutException();
    } on TimeoutException catch (e) {
      logger.e('closeAttendanceV2 error timeout $e');
      throw ServiceTimeoutException();
    } on CloseAttendanceDelayReasonException catch (e) {
      logger.e('CloseAttendanceDelayReasonException error $e');
      throw CloseAttendanceDelayReasonException(
        message: e.message ?? MessageException.general,
      );
    } on CloseAttendanceException catch (e) {
      throw CloseAttendanceException(
        message: e.message ?? MessageException.general,
      );
    } catch (e) {
      logger.e('closeAttendanceV2 error catch exception: $e');
      throw CloseAttendanceException(message: MessageException.general);
    }
  }

  String _generateFileName(
      {required String recordType, required String serviceNumber}) {
    String fileName = '';
    switch (recordType) {
      case 'Avaliação Clínica':
        return fileName = 'ac_$serviceNumber.png';
      case 'Conduta':
        return fileName = 'c_$serviceNumber.png';
      case 'Encerramento':
        return fileName = 'en_$serviceNumber.png';
    }
    return fileName;
  }

  Future<List<AttendanceModel>> getAttendances({
    codUnimed,
    codVeiculo,
    paginaAtual,
    quantidadeRegistros,
  }) async {
    try {
      final endpoint =
          'ambulance/attendance/$codVeiculo/vehicle?codUnimed=$codUnimed&registers=$quantidadeRegistros&page=$paginaAtual';
      final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

      final String? token =
          await Locator.instance.get<AuthApi>().tokenPerfilApps();

      final headers = {
        'Authorization': "Bearer $token",
      };
      http.Response response = await this.httpClient.get(
            Uri.parse(url),
            headers: headers,
          );

      if (response.statusCode == 200) {
        final bodyRetorno = jsonDecode(response.body)['lista'];

        final int totalRecords =
            jsonDecode(response.body)['totalRegistros'] ?? 10;
        logger.d('TeamApi getAttendance retorno : $bodyRetorno');
        _setNumberOfServicePages(totalRecords: totalRecords);

        await _persistGetListAttendanceResponse(
          value: response.body,
          numAmbulancia: codVeiculo,
          pageNumber: paginaAtual.toString(),
        );

        fetchDataInBackgroundetListAttendanceOfflinePages(
          codUnimed: codUnimed,
          codVeiculo: codVeiculo,
          paginaAtual: paginaAtual,
          quantidadeRegistros: quantidadeRegistros,
          numberPages: (totalRecords / 10).ceil(),
        );

        return await _getListAttendanceOffline(
          numAmbulancia: codVeiculo,
          pageNumber: paginaAtual.toString(),
          error: UnimedException(MessageException.general),
        );
      } else if (response.statusCode == 500) {
        throw InternalServerError();
      } else if (response.statusCode == 404) {
        throw NotFoundError();
      } else {
        debugPrint("============= BODY ========== ");
        debugPrint("============= ${response.body} ========== ");
        logger.e('Erro ${response.statusCode} found');
        throw AttendanceException(MessageException.general);
      }
    } on ServiceTimeoutException catch (ex) {
      logger.e('Error AttendanceApi getAttendance ServiceTimeoutException $ex');

      return _getListAttendanceOffline(
        numAmbulancia: codVeiculo,
        pageNumber: paginaAtual.toString(),
        error: ServiceTimeoutException(),
      );
    } on InternalServerError catch (ex) {
      logger.e('getAttendances InternalServerError ${ex.message}');
      throw InternalServerError();
    } on NotFoundError catch (ex) {
      logger.i('getAttendances NotFoundError ${ex.message}');
      throw NotFoundError();
    } on AttendanceException catch (ex) {
      logger.i('getAttendance AutorizacoesException ${ex.message}');
      return _getListAttendanceOffline(
        numAmbulancia: codVeiculo,
        pageNumber: paginaAtual.toString(),
        error: ServiceTimeoutException(),
      );
    } on NoInternetException catch (ex) {
      logger.i('getAttendance NoInternetException ${ex.message}');
      return _getListAttendanceOffline(
        numAmbulancia: codVeiculo,
        pageNumber: paginaAtual.toString(),
        error: AttendanceException(ex.message),
      );
    } on NotFoundException catch (ex) {
      logger.i('getAttendance NotFoundException ${ex.message}');
      return _getListAttendanceOffline(
        numAmbulancia: codVeiculo,
        pageNumber: paginaAtual.toString(),
        error: NoInternetException(),
      );
    } on UnimedException catch (ex) {
      logger.e('getAttendance UnimeException ${ex.message}');
      return _getListAttendanceOffline(
        numAmbulancia: codVeiculo,
        pageNumber: paginaAtual.toString(),
        error: UnimedException(MessageException.general),
      );
    } catch (e) {
      logger.e('getAttendance Exception: ${e.toString()}');
      return _getListAttendanceOffline(
        numAmbulancia: codVeiculo,
        pageNumber: paginaAtual.toString(),
        error: UnimedException(MessageException.general),
      );
    }
  }

  Future<AttendanceModel?> getAttendanceDetailsOfflineSync(
      String attendanceId) async {
    final endpoint = 'ambulance/attendance/details/$attendanceId';
    final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

    try {
      final String? token =
          await Locator.instance.get<AuthApi>().tokenPerfilAppsNew();
      final headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      http.Response response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final bodyRetorno = jsonDecode(response.body);
        logger.d('getAttendanceDetailsOfflineSync retorno : $bodyRetorno');
        final result = AttendanceModel.fromJson((bodyRetorno));

        return result;
      } else {
        logger.e('Erro ${response.statusCode} found');
      }
    } catch (ex) {
      logger.e('getAttendanceDetailsOfflineSync catch Exception $ex');
    }
    return null;
  }

  Future<AttendanceModel> getAttendanceDetail(String attendanceId) async {
    final endpoint = 'ambulance/attendance/details/$attendanceId';
    final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

    try {
      final String? token =
          await Locator.instance.get<AuthApi>().tokenPerfilAppsNew();
      final headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      http.Response response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        await _persistGetAttendanceDetailResponse(
            value: response.body, numAtendimento: attendanceId);
        final bodyRetorno = jsonDecode(response.body);
        logger.d('getAttendanceDetail retorno : $bodyRetorno');
        final result = AttendanceModel.fromJson((bodyRetorno));
        //TODO:Verifico o que tem no response e o que tem na lista de requisições e faço um merge nas infomações
        //Para retornar o objeto mais atual
        final offline =
            await _getAttendaceMovementsOfflineMerged(attendance: attendanceId);
        if (offline != null) {
          if (offline.moviments[offline.moviments.length - 1].statusCode! >
              result.codStatus) {
            result.codStatus =
                offline.moviments[offline.moviments.length - 1].statusCode!;
          }
        }

        return result;
      } else if (response.statusCode == 500) {
        if (await _checkIfThereIsDataToBeSynchronized(attendanceId) == true) {
          return _getAttendanceDetail(
            numAtendimento: attendanceId,
            error: InternalServerError(),
          );
        } else {
          throw InternalServerError();
        }
      } else if (response.statusCode == 404) {
        if (await _checkIfThereIsDataToBeSynchronized(attendanceId) == true) {
          return _getAttendanceDetail(
            numAtendimento: attendanceId,
            error: NotFoundError(),
          );
        } else {
          throw NotFoundError();
        }
      } else {
        logger.e('Erro ${response.statusCode} found');
        throw AttendanceException(MessageException.general);
      }
    } on NotFoundException catch (ex) {
      logger.e('getAttendanceDetail NotFoundException: $ex');
      return _getAttendanceDetail(
        numAtendimento: attendanceId,
        error: NotFoundException(),
      );
    } on ServiceTimeoutException catch (ex) {
      logger.e('getAttendanceDetail ServiceTimeoutException $ex');
      return _getAttendanceDetail(
        numAtendimento: attendanceId,
        error: ServiceTimeoutException(),
      );
    } on AttendanceException catch (ex) {
      logger.i('getAttendanceDetail AutorizacoesException ${ex.message}');

      return _getAttendanceDetail(
        numAtendimento: attendanceId,
        error: ServiceTimeoutException(),
      );
    } on NoInternetException catch (ex) {
      logger.i('getAttendanceDetail NoInternetException ${ex.message}');
      return _getAttendanceDetail(
        numAtendimento: attendanceId,
        error: NoInternetException(),
      );
    } on InternalServerError catch (ex) {
      logger.i('getAttendanceDetail InternalServerError ${ex.message}');
      throw InternalServerError();
    } on NotFoundError catch (ex) {
      logger.i('getAttendanceDetail NotFoundError ${ex.message}');
      throw NotFoundError();
    } catch (ex) {
      logger.e('getAttendanceDetail catch Exception $ex');
      return _getAttendanceDetail(
        numAtendimento: attendanceId,
        error: UnimedException(MessageException.general),
      );
    }
  }

  Future updateStatusMovements({
    required attendance,
    required codStatus,
    int? previousStatusCode,
    int? indexClicked,
    int? currentStep,
    codMotivo,
    observacaoMotivo,
    bool isNewAttendance = false,
    String numNewAttendance = '',
  }) async {
    late String url;
    late Map<String, String> headers;
    late String body;

    final endpoint = 'ambulance/attendance/moviment';
    url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

    if (previousStatusCode != null &&
        previousStatusCode == AttendanceStatus.NOVO_ATENDIMENTO) {
      throw AttendanceException(
          'Atendimento não pode ser cancelado, pois o status de novo atendimento está atribuido a esse atendimento.');
    }

    try {
      final String? token =
          await Locator.instance.get<AuthApi>().tokenPerfilAppsNew();
      headers = {
        'Authorization': 'Bearer $token',
        "Content-Type": "application/json"
      };

      Map<String, dynamic> jsonMap = {
        "attendance": '$attendance',
        "codStatus": '$codStatus',
        'codMotivoNaoAtendimento': codMotivo ?? "",
        'observacaoMotivoNaoAtendimento': observacaoMotivo ?? "",
        if (isNewAttendance) "codNovoAtendimento": numNewAttendance,
        "dataHora":
            '${(DateTimeUtils.getTimeFormat(dateTime: DateTime.now(), send: true))}'
      };
      body = jsonEncode(jsonMap);

      if (await _checkIfThereIsAStatusUpdateToBeSynchronized(
              attendance.toString()) ==
          true) {
        await _persistUpdateMovement(
          status: '$codStatus',
          attendance: attendance,
          currentStep: currentStep,
          indexClicked: indexClicked,
          body: body,
          url: url,
          headers: headers,
          exception: NotFoundException(),
        );
      } else {
        http.Response response = await this
            .httpClient
            .post(Uri.parse(url), headers: headers, body: body)
            .timeout(Duration(seconds: 30));

        if (response.statusCode == 200 || response.statusCode == 201) {
          return response.body;
        } else if (response.statusCode == 500) {
          final message = jsonDecode(response.body);
          throw InternalServerError(message: message);
        } else if (response.statusCode == 404) {
          throw NotFoundError();
        } else {
          // final message = response.body ?? 'Não foi possível a atualização';
          final message = response.body;
          logger.e(
              'updateStatusMovements ${response.statusCode} found; message:$message');
          await _persistUpdateMovement(
              status: '$codStatus',
              currentStep: currentStep,
              indexClicked: indexClicked,
              attendance: attendance,
              body: body,
              url: url,
              headers: headers,
              exception: NotFoundException());
        }
      }
    } on InternalServerError catch (ex) {
      logger.i('getAttendances InternalServerError ${ex.message}');

      throw ex;
    } on NotFoundError catch (ex) {
      logger.i('getAttendances NotFoundError ${ex.message}');
      throw NotFoundError();
    } on NotFoundException catch (ex) {
      logger.e('updateStatusMovements NotFoundException: $ex');
      await _persistUpdateMovement(
          status: '$codStatus',
          currentStep: currentStep,
          indexClicked: indexClicked,
          attendance: attendance,
          body: body,
          url: url,
          headers: headers,
          exception: NotFoundException());
    } on NoInternetException catch (ex) {
      logger.e('updateStatusMovements NoInternetException $ex');
      //TODO:Persisitido os dados de requisição do Status da movimentação para ser usado no sincronismo
      await _persistUpdateMovement(
          status: '$codStatus',
          currentStep: currentStep,
          indexClicked: indexClicked,
          attendance: attendance,
          body: body,
          url: url,
          headers: headers,
          exception: NoInternetException());
    } on ServiceTimeoutException catch (ex) {
      logger.e('updateStatusMovements ServiceTimeoutException $ex');
      await _persistUpdateMovement(
          status: '$codStatus',
          currentStep: currentStep,
          indexClicked: indexClicked,
          attendance: attendance,
          body: body,
          url: url,
          headers: headers,
          exception: NoInternetException());
    } on AttendanceException catch (ex) {
      logger.e('updateStatusMovements AttendanceException $ex');
      await _persistUpdateMovement(
          status: '$codStatus',
          currentStep: currentStep,
          indexClicked: indexClicked,
          attendance: attendance,
          body: body,
          url: url,
          headers: headers,
          exception: NoInternetException());
    } on TimeoutException catch (ex) {
      logger.e('updateStatusMovements error timeout $ex');
      await _persistUpdateMovement(
          status: '$codStatus',
          currentStep: currentStep,
          indexClicked: indexClicked,
          attendance: attendance,
          body: body,
          url: url,
          headers: headers,
          exception: NoInternetException());
    } catch (ex) {
      logger.e('updateStatusMovements catch Exception $ex');
      await _persistUpdateMovement(
          status: '$codStatus',
          currentStep: currentStep,
          indexClicked: indexClicked,
          attendance: attendance,
          body: body,
          url: url,
          headers: headers,
          exception: NoInternetException());
    }
  }

  Future<DestinyModel> listDestinies(codUnimed) async {
    final endpoint = '/unimedurgente/destino-paciente/listar';
    final url = '${const String.fromEnvironment('ambulanciaOsbUrl')}$endpoint';
    final headers = {
      'Authorization': HttpUtils.getAuthorizationBasicAmbulanciaOsb()
    };
    final body = jsonEncode({'codUnimed': '$codUnimed'});

    try {
      http.Response response = await this
          .httpClient
          .post(Uri.parse(url), headers: headers, body: body);

      if (response.statusCode == 200) {
        final bodyDecoded = jsonDecode(response.body);
        return DestinyModel(bodyDecoded);
      } else {
        logger.e('listDestinies statusCode found: ${response.statusCode}');
        throw AttendanceException(MessageException.general);
      }
    } on NotFoundException catch (ex) {
      logger.e('listDestinies NotFoundException: $ex');
      throw NotFoundException();
    } on AttendanceException catch (ex) {
      logger.e('listDestinies AttendanceException: $ex');
      throw AttendanceException(MessageException.general);
    } on ServiceTimeoutException catch (ex) {
      logger.e('listDestinies ServiceTimeoutException: $ex');
      throw ServiceTimeoutException();
    } on NoInternetException catch (ex) {
      logger.e('listDestinies NoInternetException: $ex');
      throw NoInternetException();
    } on UnimedException catch (ex) {
      logger.e('listDestinies UnimedException ${ex.runtimeType}: $ex');
      throw UnimedException(MessageException.general);
    } catch (ex) {
      logger.e('listDestinies catch exception: $ex');
      throw AttendanceException(MessageException.general);
    }
  }

  Future updateDestiny({numAttendance, codigoDestinoPaciente}) async {
    final endpoint = '/unimedurgente/atendimento/atualiza/destino-paciente';
    final url = '${const String.fromEnvironment('ambulanciaOsbUrl')}$endpoint';
    final headers = {
      'Authorization': HttpUtils.getAuthorizationBasicAmbulanciaOsb()
    };
    final body = jsonEncode({
      "numAtendimento": '$numAttendance',
      "codigoDestinoPaciente": "$codigoDestinoPaciente"
    });
    try {
      http.Response response = await this
          .httpClient
          .patch(Uri.parse(url), headers: headers, body: body);
      if (response.statusCode == 200) {
        logger.d('updateDestiny sucesfully.');
        return true;
      } else {
        logger.e('updateDestiny ${response.statusCode} found;');
        throw AttendanceException(MessageException.general);
      }
    } on AttendanceException catch (ex) {
      logger.e('updateDestiny AttendanceException: $ex');
      throw AttendanceException(MessageException.general);
    } on NotFoundException catch (ex) {
      logger.e('updateDestiny NotFoundException: $ex');
      throw NotFoundException();
    } on NoInternetException catch (ex) {
      logger.e('updateDestiny NoInternetException: $ex');
      throw NoInternetException();
    } on ServiceTimeoutException catch (ex) {
      logger.e('updateDestiny ServiceTimeoutException: $ex');
      throw ServiceTimeoutException();
    } on UnimedException catch (ex) {
      logger.e('updateDestiny ${ex.runtimeType} - $ex');
      throw UnimedException(MessageException.general);
    } catch (ex) {
      logger.e('updateDestiny catch Exception $ex');
      throw AttendanceException(MessageException.general);
    }
  }

  Future<List<AddressInfos>> getDataAddress(
      {required AttendanceModel attendanceModel,
      String? token,
      waitGeocode = false}) async {
    final endpoint =
        'ambulance/attendance/${attendanceModel.numAtendimento}/addresses?waitGeocode=$waitGeocode';
    final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';
    final headers = {
      'Authorization': 'Bearer $token',
      "Content-Type": "application/json"
    };
    try {
      http.Response response =
          await this.httpClient.get(Uri.parse(url), headers: headers);
      if (response.statusCode == 200) {
        logger.d('geDataAddress: ${response.body}');
        final data = jsonDecode(response.body);

        final addresses = (data as List)
            .map<AddressInfos>((address) => AddressInfos.fromJson(address))
            .toList();

        return addresses;
      } else if (response.statusCode == 406) {
        logger.i(
            'getLatLongFromAddress response.statusCode 406: ${response.body}');
        return [];
      } else {
        logger.e(
            'getLatLongFromAddress statusCode found: ${response.statusCode} - reasonPhrase: ${response.reasonPhrase}');
        throw AttendanceException(MessageException.general);
      }
    } on AttendanceException catch (ex) {
      logger.e('getLatLongFromAddress AttendanceException: $ex');
      throw AttendanceException(MessageException.general);
    } on NotFoundException catch (ex) {
      logger.e('getLatLongFromAddress NotFoundException: $ex');
      throw NotFoundException();
    } on NoInternetException catch (ex) {
      logger.e('getLatLongFromAddress NoInternetException: $ex');
      throw NoInternetException();
    } on ServiceTimeoutException catch (ex) {
      logger.e('getLatLongFromAddress ServiceTimeoutException: $ex');
      throw ServiceTimeoutException();
    } on UnimedException catch (ex) {
      logger.e('getLatLongFromAddress ${ex.runtimeType}: ${ex.message}');
      throw UnimedException(MessageException.general);
    } catch (ex) {
      logger.e('getLatLongFromAddress catch Exception $ex');
      throw AttendanceException(MessageException.general);
    }
  }

  Future<void> sendDataAddress(
      {required AttendanceModel attendanceModel,
      String? token,
      isUpdate = false}) async {
    try {
      final endpoint =
          'ambulance/attendance/${attendanceModel.numAtendimento}/addresses';
      final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';
      final headers = {
        'Authorization': 'Bearer $token',
        "Content-Type": "application/json"
      };

      Map<String, dynamic>? attendanceAddress;
      if (attendanceModel.enderecoAtendimento != null)
        attendanceAddress = addressJson(
          address: attendanceModel.enderecoAtendimento,
          tipoEndereco: 'ATTENDANCE',
        );

      Map<String, dynamic>? destinyAddress;
      if (attendanceModel.enderecoDestino != null)
        destinyAddress = addressJson(
          address: attendanceModel.enderecoDestino,
          tipoEndereco: 'DESTINY',
        );
      final body = jsonEncode({
        'addresses': [
          if (attendanceModel.enderecoAtendimento != null &&
              attendanceModel.enderecoAtendimento!.isValid())
            attendanceAddress,
          if (attendanceModel.enderecoDestino != null &&
              attendanceModel.enderecoDestino!.isValid())
            destinyAddress,
        ],
        'card': attendanceModel.carteiraNumero
      });

      http.Response response;
      response =
          await httpClient.post(Uri.parse(url), headers: headers, body: body);

      if (response.statusCode == 200) {
        logger.d('sendDataAddress response.body: ${response.body}');
      } else {
        final message = jsonDecode(response.body)['message'];
        logger.e(
            'sendDataAddress statusCode found: ${response.statusCode} - reasonPhrase: ${response.reasonPhrase} - response.body: ${response.body}');
        throw AttendanceException(message);
      }
    } on AttendanceException catch (ex) {
      logger.e('sendDataAddress AttendanceException: $ex');
      throw AttendanceException(MessageException.general);
    } on NotFoundException catch (ex) {
      logger.e('sendDataAddress NotFoundException: $ex');
      throw NotFoundException();
    } on NoInternetException catch (ex) {
      logger.e('sendDataAddress NoInternetException: $ex');
      throw NoInternetException();
    } on ServiceTimeoutException catch (ex) {
      logger.e('sendDataAddress ServiceTimeoutException: $ex');
      throw ServiceTimeoutException();
    } on UnimedException catch (ex) {
      logger.e('sendDataAddress ${ex.runtimeType}: ${ex.message}');
      throw UnimedException(MessageException.general);
    } catch (ex) {
      logger.e('sendDataAddress catch Exception $ex');
      throw AttendanceException(MessageException.general);
    }
  }

  Future<void> updateDestinyGeolocalization(
      {required AttendanceModel attendanceModel, String? token}) async {
    try {
      final endpoint =
          'ambulance/attendance/${attendanceModel.numAtendimento}/address/destiny';
      final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';
      final headers = {
        'Authorization': 'Bearer $token',
        "Content-Type": "application/json"
      };

      String body = "";
      if (attendanceModel.enderecoDestino != null &&
          attendanceModel.enderecoDestino!.isValid()) {
        try {
          Map<String, dynamic> destinyAddress;

          destinyAddress = addressJson(
            address: attendanceModel.enderecoDestino,
            tipoEndereco: 'DESTINY',
          );
          body = jsonEncode({
            'address': destinyAddress,
            'card': attendanceModel.carteiraNumero
          });
          logger.d('updateDestinyGeolocalization  válido');
          http.Response response;
          response = await httpClient.patch(Uri.parse(url),
              headers: headers, body: body);

          if (response.statusCode == 200) {
            logger.d(
                'updateDestinyGeolocalization response.body: ${response.body}');
          } else {
            final json = jsonDecode(response.body);
            logger.e(
                'updateDestinyGeolocalization statusCode found: ${response.statusCode} - reasonPhrase: ${response.reasonPhrase} - response.body: $json');
            final _message = json['message'] ?? MessageException.general;
            throw AttendanceException(_message);
          }
        } catch (ex) {
          logger.e('updateDestinyGeolocalization error json ${ex.toString()}');
          throw AttendanceException(MessageException.general);
        }
      } else {
        logger.e(
            'updateDestinyGeolocalization endereco destino é invalido ${attendanceModel.enderecoDestino}');
        throw AttendanceException(MessageException.general);
      }
    } on AttendanceException catch (ex) {
      logger.e('updateDestinyGeolocalization AttendanceException: $ex');
      throw AttendanceException(MessageException.general);
    } on NotFoundException catch (ex) {
      logger.e('updateDestinyGeolocalization NotFoundException: $ex');
      throw NotFoundException();
    } on NoInternetException catch (ex) {
      logger.e('updateDestinyGeolocalization NoInternetException: $ex');
      throw NoInternetException();
    } on ServiceTimeoutException catch (ex) {
      logger.e('updateDestinyGeolocalization ServiceTimeoutException: $ex');
      throw ServiceTimeoutException();
    } on UnimedException catch (ex) {
      logger.e('updateDestinyGeolocalization ${ex.runtimeType}: ${ex.message}');
      throw UnimedException(MessageException.general);
    } catch (ex) {
      logger.e('updateDestinyGeolocalization catch Exception $ex');
      throw AttendanceException(MessageException.general);
    }
  }

  Future<void> _persistUpdateMovement(
      {required String status,
      int? indexClicked,
      int? currentStep,
      required attendance,
      required String body,
      required String url,
      required Map<String, String> headers,
      required exception}) async {
    try {
      if (indexClicked != null && currentStep != null) {
        int currentStatus = AttendanceStatus.statusApp2StatusApi(currentStep);
        int clickedStatus = AttendanceStatus.statusApp2StatusApi(indexClicked);

        if (currentStatus == AttendanceStatus.SAIDA_BASE) {
          if (clickedStatus != AttendanceStatus.CHEGADA_ORIGEM &&
              clickedStatus != AttendanceStatus.CHEGADA_BASE) {
            throw InternalServerError(
              message:
                  "Atendimento não passou pelo status de ${_serviceStatusTitle(indexStatus: (indexClicked - 1))}.",
            );
          }
        } else if (currentStatus == AttendanceStatus.CHEGADA_ORIGEM) {
          if (clickedStatus != AttendanceStatus.SAIDA_ORIGEM) {
            if (clickedStatus == AttendanceStatus.CHEGADA_BASE) {
              throw InternalServerError(
                message:
                    "Se o campo CHEGADA for preenchido é obrigatório o preenchimento do campo SAÍDA correspondente.",
              );
            }
            throw InternalServerError(
              message:
                  "Atendimento não passou pelo status de ${_serviceStatusTitle(indexStatus: (indexClicked - 1))}.",
            );
          }
        } else if (currentStatus == AttendanceStatus.SAIDA_ORIGEM) {
          if (clickedStatus != AttendanceStatus.CHEGADA_DESTINO &&
              clickedStatus != AttendanceStatus.CHEGADA_BASE) {
            throw InternalServerError(
              message:
                  "Atendimento não passou pelo status de ${_serviceStatusTitle(indexStatus: (indexClicked - 1))}.",
            );
          }
        } else if (currentStatus == AttendanceStatus.CHEGADA_DESTINO) {
          if (clickedStatus != AttendanceStatus.SAIDA_DESTINO) {
            throw InternalServerError(
              message:
                  "Se o campo CHEGADA for preenchido é obrigatório o preenchimento do campo SAÍDA correspondente.",
            );
          }
        }
      }
      final MovementsModel? registersMerged =
          await _getAttendaceMovementsOfflineMerged(attendance: attendance);
      bool addElement = _compareStatusMovementAdd(registersMerged, status);
      if (addElement) {
        await OfflineFirst.persistRequest(
          customId:
              '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.updateStatusMovements.name}$attendance',
          body: body,
          url: url,
          headers: headers,
          typeRequest: SYNC_TYPE_REQUEST.POST,
        );

        Locator.instance
            .get<NotificationOfAmountsOfDataPendingSynchronizationApi>()
            .addNotificationOfAmountsOfDataPendingSynchronization();
      }
    } on InternalServerError catch (e) {
      logger.e('_persistUpdateMovement catch Exception $e');
      throw e;
    } catch (e) {
      logger.e('_persistUpdateMovement catch Exception $e');
      throw exception;
    }
  }

  String _serviceStatusTitle({required int indexStatus}) {
    switch (indexStatus) {
      case 0:
        return _formatedTextStatusAlert(title: "Início do Atendimento");
      case 1:
        return _formatedTextStatusAlert(title: "Chegada na Origem");
      case 2:
        return _formatedTextStatusAlert(title: "Saída da Origem");
      case 3:
        return _formatedTextStatusAlert(title: "Chegada ao Destino");
      case 4:
        return _formatedTextStatusAlert(title: "Saída do Destino");
      default:
        return "";
    }
  }

  String _formatedTextStatusAlert({required String title}) {
    return '$title';
  }

  Future<MovementsModel?> _getAttendaceMovementsOfflineMerged(
      {required attendance, UnimedException? exception}) async {
    try {
      MovementsModel? valuesCurrent =
          await _getResponseOfflineToIdMovements(attendance);
      final listRequest = await OfflineFirst.getCacheRequest(
          customID:
              '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.updateStatusMovements.name}$attendance');

      if (listRequest != null) {
        listRequest.forEach(
          (element) {
            final value = jsonDecode(element.jsonRequest!);
            if (valuesCurrent != null) {
              valuesCurrent!.moviments.add(MovementObject(
                  statusCode: int.parse(value['codStatus']),
                  movimentDate: DateTimeUtils.getTimeConvertMovementsModel(
                      date: value['dataHora'])));
            } else {
              valuesCurrent = MovementsModel(
                attendance: int.parse(attendance),
                moviments: [
                  MovementObject(
                      statusCode: int.parse(value['codStatus']),
                      movimentDate: DateTimeUtils.getTimeConvertMovementsModel(
                          date: value['dataHora']))
                ],
              );
            }
          },
        );
      }

      if (valuesCurrent != null) {
        return valuesCurrent;
      } else {
        if (exception != null) throw exception;
      }
    } catch (e) {
      logger.e('_getAttendaceMovementsOffline error $e');
      if (exception != null) throw exception;
    }
    return null;
  }

  Map<String, dynamic> addressJson(
      {AddressModel? address, String? tipoEndereco}) {
    return {
      'tipoEndereco': tipoEndereco,
      'numero': address?.numero ?? '',
      'tipoLogradouro': address?.tipoLogradouro ?? '',
      'logradouro': address?.logradouro ?? '',
      'bairro': address?.bairro ?? '',
      'cidade': address?.cidade ?? '',
      'cep': address?.cep ?? '',
      'uf': address?.uf ?? 'CE',
    };
  }

  Future<MovementsModel?> _getResponseOfflineToIdMovements(
      numAtendimento) async {
    final valuesCurrentJson = await OfflineFirst.getCacheResponse(
        customID:
            '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.getAttendanceMovements.name}$numAtendimento');
    MovementsModel? valuesCurrent;
    if (valuesCurrentJson != null) {
      valuesCurrent = MovementsModel.fromJson(jsonDecode(valuesCurrentJson));
    }
    return valuesCurrent;
  }

  bool _compareStatusMovementAdd(MovementsModel? registerNow, String status) {
    bool addElement = false;
    if (registerNow != null) {
      if (registerNow.moviments[registerNow.moviments.length - 1].statusCode! <
          int.parse(status)) {
        addElement = true;
      }
    } else {
      addElement = true;
    }
    return addElement;
  }

  _persistGetAttendanceDetailResponse(
      {required value, required numAtendimento}) async {
    try {
      await OfflineFirst.persistResponse(
        customID:
            '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.getAttendanceDetail.name}$numAtendimento',
        category: '$numAtendimento',
        body: value,
      );
    } catch (e) {
      logger.e('_persistGetAttendanceDetailResponse catch Exception $e');
    }
  }

  Future<void> _persistGetListAttendanceResponse(
      {required value,
      required String numAmbulancia,
      required String pageNumber}) async {
    String key =
        '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.getListAttendance.name}${numAmbulancia}_${pageNumber}';

    await Locator.instance
        .get<LastSynchronizationDates>()
        .saveServiceDateTimeUpdate(
          serviceUpdate: RequestServiceDataTime(
            serviceName: key,
            lastUpdate: DateTime.now(),
          ),
        );
    try {
      await OfflineFirst.persistResponse(
        customID: key,
        category: '$numAmbulancia',
        body: value,
      );

      Locator.instance
          .get<NotificationOfAmountsOfDataPendingSynchronizationApi>()
          .addNotificationOfAmountsOfDataPendingSynchronization();
    } catch (e) {
      logger.e('_persistGetListAttendanceResponse catch Exception $e');
    }
  }

  Future<List<AttendanceModel>> _getListAttendanceOffline({
    required String numAmbulancia,
    required String pageNumber,
    required UnimedException error,
  }) async {
    try {
      final valuesCurrentJson = await OfflineFirst.getCacheResponse(
          customID:
              '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.getListAttendance.name}${numAmbulancia}_${pageNumber}');

      if (valuesCurrentJson != null) {
        final bodyRetorno = jsonDecode(valuesCurrentJson)['lista'];

        final totalRecords =
            jsonDecode(valuesCurrentJson)['totalRegistros'] ?? 10;
        logger.d('_getListAttendanceOffline retorno : $bodyRetorno');
        _setNumberOfServicePages(totalRecords: totalRecords);

        final attendances = (bodyRetorno as List).map<AttendanceModel>((sa) {
          return AttendanceModel.fromJson(sa);
        }).toList();

        for (var attendance in attendances) {
          final listRequest = await OfflineFirst.getCacheRequest(
              customID:
                  '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.updateStatusMovements.name}${attendance.numAtendimento.toString()}');

          if (listRequest != null) {
            final jsonData = jsonDecode(listRequest.last.jsonRequest!);

            attendance.codStatus = int.parse(jsonData["codStatus"]);
            attendance.nomeStatus =
                _getNameStatus(codStatus: attendance.codStatus);
          }
        }

        return attendances;
      } else {
        logger.d(
            '_getListAttendanceOffline Nenhum dado encontrado salvo offline');
        throw error;
      }
    } catch (e) {
      logger.d('_getListAttendanceOffline exception catch:$e');
      throw error;
    }
  }

  Future<AttendanceModel> _getAttendanceDetail({
    required numAtendimento,
    required UnimedException error,
  }) async {
    try {
      final valuesCurrentJson = await OfflineFirst.getCacheResponse(
          customID:
              '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.getAttendanceDetail.name}$numAtendimento');

      AttendanceModel? valuesCurrent;
      if (valuesCurrentJson != null) {
        valuesCurrent = AttendanceModel.fromJson(jsonDecode(valuesCurrentJson));
        final offlinegetPersistConduct =
            await _getPersistConduct(numAtendimento: numAtendimento);
        final offlineMovements = await _getAttendaceMovementsOfflineMerged(
            attendance: numAtendimento);
        if (offlineMovements != null) {
          if (offlineMovements.moviments[offlineMovements.moviments.length - 1]
                  .statusCode! >
              valuesCurrent.codStatus) {
            valuesCurrent.codStatus = offlineMovements
                .moviments[offlineMovements.moviments.length - 1].statusCode!;
          }
        }
        if (offlinegetPersistConduct != null) {
          valuesCurrent.observacaoConduta =
              offlinegetPersistConduct['observacaoConduta'] ??
                  valuesCurrent.observacaoConduta;
          valuesCurrent.descricaoTerapeutica =
              offlinegetPersistConduct['descricaoTerapeutica'] ??
                  valuesCurrent.descricaoTerapeutica;
          valuesCurrent.codDiagnostico =
              offlinegetPersistConduct['codDiagnostico'] ??
                  valuesCurrent.codDiagnostico;
          valuesCurrent.descricaoDiagnostico =
              offlinegetPersistConduct['descricaoDiagnostico'] ??
                  valuesCurrent.descricaoDiagnostico;
        }

        return valuesCurrent;
      } else {
        logger.d('_getAttendanceDetail exception');
        throw error;
      }
    } catch (e) {
      logger.d('_getAttendanceDetail exception catch:$e');
      throw error;
    }
  }

  _getPersistConduct({required numAtendimento}) async {
    final valuesCurrentJson = await OfflineFirst.getCacheResponse(
        customID:
            '${SYNC_CATEGORY_API.ConductApi.name}${SYNC_CONDUCTAPI_REQUEST.sendConduct.name}$numAtendimento');

    var valuesCurrent;
    try {
      if (valuesCurrentJson != null) {
        valuesCurrent = jsonDecode(valuesCurrentJson);
      } else {
        valuesCurrent = {};
      }
    } catch (_) {
      return {};
    }

    return valuesCurrent;
  }

  Future<void> _setNumberOfServicePages({required int totalRecords}) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    await prefs.setInt(totalRecordsPersist, totalRecords);
  }

  Future<void> fetchDataInBackgroundetListAttendanceOfflinePages({
    required String codUnimed,
    required String codVeiculo,
    required int paginaAtual,
    required String quantidadeRegistros,
    required int numberPages,
  }) async {
    for (int i = 2; i <= numberPages; i++) {
      try {
        final endpoint =
            'ambulance/attendance/$codVeiculo/vehicle?codUnimed=$codUnimed&registers=$quantidadeRegistros&page=$i';
        final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

        final String? token =
            await Locator.instance.get<AuthApi>().tokenPerfilApps();

        final headers = {
          'Authorization': "Bearer $token",
        };

        http.Response response = await this.httpClient.get(
              Uri.parse(url),
              headers: headers,
            );
        if (response.statusCode == 200) {
          _persistGetListAttendanceResponse(
            value: response.body,
            numAmbulancia: codVeiculo,
            pageNumber: i.toString(),
          );
        } else {
          debugPrint("============= BODY ========== ");
          debugPrint("============= ${response.body} ========== ");
          logger.e(
              'Erro fetchDataInBackgroundetListAttendanceOfflinePages ${response.statusCode} found');
        }
      } catch (e) {
        logger.e(
            'Erro fetchDataInBackgroundetListAttendanceOfflinePages erro: ${e.toString()}');
      }
    }
  }

  Future<bool> _checkIfThereIsDataToBeSynchronized(
    String numAtendimento,
  ) async {
    String path = join(await getDatabasesPath(), 'ambulancia.db');
    Database _database = await openDatabase(path);
    String _tableName =
        'tbSync${const String.fromEnvironment('environment') == "PROD" ? '_prod' : '_dev'}';

    final queryResult = await _database.rawQuery(
      'SELECT * FROM $_tableName WHERE ${SyncTableSQLiteColums.TYPE_REQUEST} IN (?, ?, ?, ?, ?) AND ${SyncTableSQLiteColums.JSON_FIELDS} LIKE ?',
      [
        'POST',
        'DELETE',
        'PUT',
        'PATCH',
        'POST_WITH_FILE',
        '%"numAtendimento":"$numAtendimento"%'
      ],
    );

    return queryResult.isNotEmpty;
  }

  Future<bool> _checkIfThereIsAStatusUpdateToBeSynchronized(
    String attendance,
  ) async {
    String path = join(await getDatabasesPath(), 'ambulancia.db');
    Database _database = await openDatabase(path);
    String _tableName =
        'tbSync${const String.fromEnvironment('environment') == "PROD" ? '_prod' : '_dev'}';
    final endpoint = '/ambulance/attendance/moviment';
    String url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

    final queryResult = await _database.rawQuery(
      'SELECT * FROM $_tableName WHERE ${SyncTableSQLiteColums.TYPE_REQUEST} = ? AND ${SyncTableSQLiteColums.JSON} LIKE ? AND ${SyncTableSQLiteColums.URL} = ?',
      ['POST', '%"attendance":"$attendance"%', '$url'],
    );

    return queryResult.isNotEmpty;
  }

  Future<int> AmountsOfDataToBeSynchronized() async {
    String path = join(await getDatabasesPath(), 'ambulancia.db');
    Database _database = await openDatabase(path);
    String _tableName =
        'tbSync${const String.fromEnvironment('environment') == "PROD" ? '_prod' : '_dev'}';

    final queryResult = await _database.rawQuery(
      'SELECT * FROM $_tableName WHERE ${SyncTableSQLiteColums.TYPE_REQUEST} IN (?, ?, ?, ?, ?)',
      ['POST', 'DELETE', 'PUT', 'PATCH', 'POST_WITH_FILE'],
    );

    return queryResult.length;
  }

  String _getNameStatus({required int codStatus}) {
    if (codStatus == AttendanceStatus.CANCELADO) {
      return "CANCELADO";
    } else if (codStatus == AttendanceStatus.ENCERRADO) {
      return "ENCERRADO";
    } else {
      return "INDEFINIDO";
    }
  }
}
