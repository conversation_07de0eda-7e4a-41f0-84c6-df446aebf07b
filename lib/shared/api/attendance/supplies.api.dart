import 'dart:convert';

import 'package:ambulancia_app/models/supply_model.dart';
import 'package:ambulancia_app/shared/api/graphql.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/http.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:graphql/client.dart';
import 'package:http/http.dart' as http;

class SuppliesApi {
  final UnimedHttpClient httpClient;
  SuppliesApi({
    required this.httpClient,
  });
  final logger = UnimedLogger(className: 'SuppliesApi');

  Future<List<SupplyModel>> listAvailableSupplies(
      {required attendanceNumber}) async {
    try {
      final GraphQLClient client =
          await Locator.instance.get<GraphQlApi>().getGithubGraphQLClient();

      String query = '''
       query AmbulanceAvaliableSupply {
          ambulanceAvaliableSupply {
              code
              description
          }
      }
      ''';

      logger.d('listAvailableSupplies query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
        queryRequestTimeout: DEFAULT_TIMEOUT,
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('listAvailableSupplies exception : ${result.exception}');
        throw UnimedException(MessageException.general);
      } else {
        final List<dynamic> supplies =
            result.data!["ambulanceAvaliableSupply"] as List<dynamic>;
        logger.d('listAvailableSupplies success : $supplies');

        return supplies.map((supply) => SupplyModel.fromJson(supply)).toList();
      }
    } on NoInternetException catch (ex) {
      logger.e('listAvailableSupplies NoInternetException : $ex');
      throw NoInternetException();
    } on UnimedException catch (ex) {
      logger.e('listAvailableSupplies UnimedException : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('listAvailableSupplies exception : $ex');
      throw UnimedException(MessageException.general);
    }
  }

  Future<void> saveSupply({
    required int? attendanceNumber,
    required int supplyCode,
    required int? quantity,
  }) async {
    final endpoint = '/unimedurgente/gasto-material';
    final String url =
        '${const String.fromEnvironment('ambulanciaOsbUrl')}$endpoint';
    final body = {
      'numAtendimento': attendanceNumber,
      'codigoMaterial': supplyCode,
      'quantidade': quantity,
    };
    final headers = {
      'Authorization': HttpUtils.getAuthorizationBasicAmbulanciaOsb(),
    };
    try {
      http.Response response = await this
          .httpClient
          .post(Uri.parse(url), body: jsonEncode(body), headers: headers);

      if (response.statusCode != 201 && response.statusCode != 200) {
        logger.e('saveSupply statusCode found: ${response.statusCode}');
        throw SuppliesException(message: jsonDecode(response.body));
      }
    } on NotFoundException catch (ex) {
      logger.i('saveSupply NotFoundException ${ex.message}');
      throw NotFoundException();
    } on NoInternetException catch (ex) {
      logger.i('saveSupply NoInternetException ${ex.message}');
      throw NoInternetException();
    } on ServiceTimeoutException catch (ex) {
      logger.e('saveSupply ServiceTimeoutException $ex');
      throw ServiceTimeoutException();
    } on SuppliesException catch (ex) {
      logger.e('saveSupply SuppliesException ${ex.message}');
      throw SuppliesException(message: ex.message);
    } catch (ex) {
      logger.e('saveSupply Exception $ex');
      throw SuppliesException();
    }
  }

  Future<void> updateSupply({
    required int? attendanceNumber,
    required int? supplyCode,
    required int? quantity,
  }) async {
    try {
      final endpoint = '/unimedurgente/gasto-material/quantidade';
      final String url =
          '${const String.fromEnvironment('ambulanciaOsbUrl')}$endpoint';
      final Map<String, Object?> body = {
        'numAtendimento': attendanceNumber,
        'codigoMaterial': supplyCode,
        'quantidade': quantity,
      };
      final headers = {
        'Authorization': HttpUtils.getAuthorizationBasicAmbulanciaOsb(),
      };

      http.Response response = await this
          .httpClient
          .patch(Uri.parse(url), body: jsonEncode(body), headers: headers);
      if (response.statusCode != 204 && response.statusCode != 200) {
        throw SuppliesException();
      }
    } on NotFoundException catch (ex) {
      logger.i('Error updateSupply NotFoundException ${ex.message}');
      throw NotFoundException();
    } on NoInternetException catch (ex) {
      logger.i('Error updateSupply NoInternetException ${ex.message}');
      throw NoInternetException();
    } on ServiceTimeoutException catch (ex) {
      logger.e('Error updateSupply ServiceTimeoutException $ex');
      throw ServiceTimeoutException();
    } on SuppliesException catch (ex) {
      logger.i('Error updateSupply SuppliesException ${ex.message}');
      throw SuppliesException();
    } catch (ex) {
      logger.i('Error updateSupply Exception $ex');
      throw SuppliesException();
    }
  }
}
