import 'dart:convert';
import 'dart:io';

import 'package:ambulancia_app/models/attachment_img_model.dart';
import 'package:ambulancia_app/models/attachment_model.dart';
import 'package:ambulancia_app/models/conduct_model.dart';
import 'package:ambulancia_app/models/image_model.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/utils/http.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/offline-first/offline_first.dart';
import 'package:ambulancia_app/shared/utils/photo.constants.dart';
import 'package:ambulancia_app/shared/utils/sync.utils.dart';
import 'package:http/http.dart' as http;

class ConductAttachmentAPI {
  final UnimedHttpClient httpClient;

  final logger = UnimedLogger(className: 'ConductAnnexAPI');

  ConductAttachmentAPI(this.httpClient);

  Future<List<AttachmentModel>> getAttachments(
      {required int serviceNumber}) async {
    final endpoint = '/unimedurgente/anexos/atendimento/$serviceNumber';
    final url = '${const String.fromEnvironment('ambulanciaOsbUrl')}$endpoint';
    final headers = {
      'Authorization': HttpUtils.getAuthorizationBasicAmbulanciaOsb(),
    };

    try {
      http.Response response =
          await this.httpClient.get(Uri.parse(url), headers: headers);
      if (response.statusCode == 200) {
        await _persistGetAttachmentsResponse(
          value: response.body,
          numAtendimento: serviceNumber,
        );
        final bodyRetorno = jsonDecode(response.body);

        final List<AttachmentModel> listAttachment = bodyRetorno
            .map<AttachmentModel>((json) => AttachmentModel.fromJson(json))
            .toList();
        List<AttachmentModel> _filterList = listAttachment
            .where(
              (attachment) =>
                  attachment.motivoAnexo ==
                  int.parse(photoReasonAnnexEnum.anexo_conduta.value),
            )
            .toList();

        return _filterList;
      } else {
        throw AttendanceException(MessageException.general);
      }
    } on ServiceTimeoutException catch (ex) {
      logger
          .e('Error AttendanceApi getAttachments ServiceTimeoutException $ex');
      final persitData = await _getAttachmentsUpdateCacheResponse(
          numAtendimento: serviceNumber);
      return persitData;
    } on AttendanceException catch (ex) {
      logger.i('getAttachments AutorizacoesException ${ex.message}');
      final persitData = await _getAttachmentsUpdateCacheResponse(
          numAtendimento: serviceNumber);
      return persitData;
    } on NoInternetException catch (ex) {
      logger.i('getAttachments NoInternetException ${ex.message}');
      final persitData = await _getAttachmentsUpdateCacheResponse(
          numAtendimento: serviceNumber);
      return persitData;
    } on NotFoundException catch (ex) {
      logger.i('getAttachments NotFoundException ${ex.message}');
      final persitData = await _getAttachmentsUpdateCacheResponse(
          numAtendimento: serviceNumber);
      return persitData;
    } on UnimedException catch (ex) {
      logger.e('getAttachments UnimeException ${ex.message}');
      final persitData = await _getAttachmentsUpdateCacheResponse(
          numAtendimento: serviceNumber);
      return persitData;
    } catch (e) {
      logger.e('getAttachments Exception: ${e.toString()}');
      final persitData = await _getAttachmentsUpdateCacheResponse(
          numAtendimento: serviceNumber);
      return persitData;
    }
  }

  Future<AttachmentImageModel> getDownloadsImg(
      {required int imgNumber, required String numAtendimento}) async {
    final endpoint = '/unimedurgente/download-anexo/$imgNumber';
    final url = '${const String.fromEnvironment('ambulanciaOsbUrl')}$endpoint';
    final headers = {
      'Authorization': HttpUtils.getAuthorizationBasicAmbulanciaOsb(),
    };

    try {
      http.Response response =
          await this.httpClient.get(Uri.parse(url), headers: headers);
      if (response.statusCode == 200) {
        _persistGetDownloadsImgResponse(
          value: response.body,
          imgNumber: imgNumber,
        );
        final bodyRetorno = jsonDecode(response.body);

        final AttachmentImageModel attachmentImg =
            AttachmentImageModel.fromJson(bodyRetorno);

        return attachmentImg;
      } else {
        final persitData =
            await _getDownloadsImgAttachmentsUpdateCacheResponseAndRequest(
                imgNumber: imgNumber, numAtendimento: numAtendimento);
        if (persitData != null) {
          return persitData;
        } else {
          throw AttendanceException(MessageException.general);
        }
      }
    } on ServiceTimeoutException catch (ex) {
      logger
          .e('Error AttendanceApi getAttachments ServiceTimeoutException $ex');
      final persitData =
          await _getDownloadsImgAttachmentsUpdateCacheResponseAndRequest(
              imgNumber: imgNumber, numAtendimento: numAtendimento);
      if (persitData != null) {
        return persitData;
      } else {
        throw ServiceTimeoutException();
      }
    } on AttendanceException catch (ex) {
      logger.i('getAttachments AutorizacoesException ${ex.message}');
      final persitData =
          await _getDownloadsImgAttachmentsUpdateCacheResponseAndRequest(
              imgNumber: imgNumber, numAtendimento: numAtendimento);
      if (persitData != null) {
        return persitData;
      } else {
        throw AttendanceException(ex.message);
      }
    } on NoInternetException catch (ex) {
      logger.i('getAttachments NoInternetException ${ex.message}');
      final persitData =
          await _getDownloadsImgAttachmentsUpdateCacheResponseAndRequest(
              imgNumber: imgNumber, numAtendimento: numAtendimento);
      if (persitData != null) {
        return persitData;
      } else {
        throw NoInternetException();
      }
    } on NotFoundException catch (ex) {
      logger.i('getAttachments NotFoundException ${ex.message}');
      final persitData =
          await _getDownloadsImgAttachmentsUpdateCacheResponseAndRequest(
              imgNumber: imgNumber, numAtendimento: numAtendimento);
      if (persitData != null) {
        return persitData;
      } else {
        throw NotFoundException();
      }
    } on UnimedException catch (ex) {
      logger.e('getAttachments UnimeException ${ex.message}');
      final persitData =
          await _getDownloadsImgAttachmentsUpdateCacheResponseAndRequest(
              imgNumber: imgNumber, numAtendimento: numAtendimento);
      if (persitData != null) {
        return persitData;
      } else {
        throw UnimedException(MessageException.general);
      }
    } catch (e) {
      logger.e('getAttachments Exception: ${e.toString()}');
      final persitData =
          await _getDownloadsImgAttachmentsUpdateCacheResponseAndRequest(
              imgNumber: imgNumber, numAtendimento: numAtendimento);
      if (persitData != null) {
        return persitData;
      } else {
        throw UnimedException(MessageException.general);
      }
    }
  }

  Future<void> deleteAttachments({required int attachmentsNumber}) async {
    final endpoint = '/unimedurgente/anexo/remover/$attachmentsNumber';
    final url = '${const String.fromEnvironment('ambulanciaOsbUrl')}$endpoint';
    final headers = {
      'Authorization': HttpUtils.getAuthorizationBasicAmbulanciaOsb(),
    };

    try {
      http.Response response =
          await this.httpClient.delete(Uri.parse(url), headers: headers);
      if (response.statusCode != 200 && response.statusCode != 204) {
        final message = jsonDecode(response.body);
        throw SuppliesException(message: message ?? MessageException.general);
      }
    } on ServiceTimeoutException catch (ex) {
      logger.e(
          'Error AttendanceApi deleteAttachments ServiceTimeoutException $ex');
      throw ServiceTimeoutException();
    } on AttendanceException catch (ex) {
      logger.i('deleteAttachments AutorizacoesException ${ex.message}');
      throw AttendanceException(ex.message);
    } on NoInternetException catch (ex) {
      logger.i('deleteAttachments NoInternetException ${ex.message}');
      throw NoInternetException();
    } on NotFoundException catch (ex) {
      logger.i('deleteAttachments NotFoundException ${ex.message}');
      throw NotFoundException();
    } on UnimedException catch (ex) {
      logger.e('deleteAttachments UnimeException ${ex.message}');
      throw UnimedException(MessageException.general);
    } catch (e) {
      logger.e('deleteAttachments Exception: ${e.toString()}');
      throw TeamException(MessageException.general);
    }
  }

  Future<void> _persistGetAttachmentsResponse(
      {required value, required numAtendimento}) async {
    try {
      await OfflineFirst.persistResponse(
        customID:
            '${SYNC_CATEGORY_API.ConductAttachmentApi.name}${SYNC_ATTENDANCEAPI_REQUEST.getAttachments.name}$numAtendimento',
        category: '$numAtendimento',
        body: value,
      );
    } catch (e) {
      logger.e('_persistGetAttachmentsResponse catch Exception $e');
    }
  }

  Future<void> _persistGetDownloadsImgResponse(
      {required value, required imgNumber}) async {
    try {
      await OfflineFirst.persistResponse(
        customID:
            '${SYNC_CATEGORY_API.ConductAttachmentDownloadsImgApi.name}${SYNC_ATTENDANCEAPI_REQUEST.getAttachmentsDownloadsImg.name}$imgNumber',
        category: '$imgNumber',
        body: value,
      );
    } catch (e) {
      logger.e('_persistGetAttachmentsDownloadsImgResponse catch Exception $e');
    }
  }

  Future<List<AttachmentModel>> _getCacheAttachmentsResponse(
      {required numAtendimento}) async {
    List<AttachmentModel> valuesCurrent = [];
    try {
      final _getCacheResponseValuesCurrentJson =
          await OfflineFirst.getCacheResponse(
              customID:
                  '${SYNC_CATEGORY_API.ConductAttachmentApi.name}${SYNC_ATTENDANCEAPI_REQUEST.getAttachments.name}$numAtendimento');

      if (_getCacheResponseValuesCurrentJson != null) {
        final bodyRetorno = jsonDecode(_getCacheResponseValuesCurrentJson);

        valuesCurrent = bodyRetorno
            .map<AttachmentModel>((json) => AttachmentModel.fromJson(json))
            .toList();
      }
    } catch (e) {
      logger.e('_getCacheAttachmentsResponse error $e');
    }
    return valuesCurrent;
  }

  Future<AttachmentImageModel?>
      _getDownloadsImgAttachmentsUpdateCacheResponseAndRequest(
          {required imgNumber, required String numAtendimento}) async {
    AttachmentImageModel? attachmentImg;
    try {
      final _getCacheResponseValuesCurrentJson =
          await OfflineFirst.getCacheResponse(
              customID:
                  '${SYNC_CATEGORY_API.ConductAttachmentDownloadsImgApi.name}${SYNC_ATTENDANCEAPI_REQUEST.getAttachmentsDownloadsImg.name}$imgNumber');

      if (_getCacheResponseValuesCurrentJson != null &&
          _getCacheResponseValuesCurrentJson.length > 0) {
        final bodyRetorno = jsonDecode(_getCacheResponseValuesCurrentJson);

        attachmentImg = AttachmentImageModel.fromJson(bodyRetorno);
      } else {
        final listRequest = await OfflineFirst.getCacheRequest(
            customID:
                '${SYNC_CATEGORY_API.ClinicEvaluationApi.name}${SYNC_CLINICEVALUATIONAPI_REQUEST.sendClinicEvaluation.name}$numAtendimento');

        if (listRequest != null) {
          for (var element in listRequest) {
            if (element.id == imgNumber) {
              List<int> bytes = await File(element.pathFile!).readAsBytes();
              String base64 = base64Encode(bytes);
              attachmentImg = AttachmentImageModel(
                nomeArquivo: element.fileName.toString(),
                arquivoBase64: base64,
              );
            }
          }
        }
      }
    } catch (e) {
      logger.e(
          '_getDownloadsImgAttachmentsUpdateCacheResponseAndRequest error $e');
    }

    return attachmentImg;
  }

  Future<List<AttachmentModel>> _getAttachmentsUpdateCacheResponse(
      {required numAtendimento}) async {
    List<AttachmentModel> _listAttachment = [];
    try {
      List<AttachmentModel> _listFiles =
          await _getCacheAttachmentsResponse(numAtendimento: numAtendimento);
      _listAttachment = _listFiles
          .where(
            (attachment) =>
                attachment.motivoAnexo ==
                int.parse(photoReasonAnnexEnum.anexo_conduta.value),
          )
          .toList();

      return _listAttachment;
    } catch (e) {
      logger.e('_getAttachmentsDownloadsImgOfflineMerged error $e');
    }
    return _listAttachment;
  }

  Future<List<ImageModel>> getAttachmentsUpdateCacheRequest(
      {required numAtendimento}) async {
    List<ImageModel> _listAttachment = [];
    try {
      final listRequest = await OfflineFirst.getCacheRequest(
          customID:
              '${SYNC_CATEGORY_API.ConductApi.name}${SYNC_CONDUCTAPI_REQUEST.sendConduct.name}$numAtendimento');

      if (listRequest != null) {
        final ConductModel conductModel = ConductModel.fromJson(
            jsonDecode(listRequest[listRequest.length - 1].jsonRequest!));

        for (var attachment in conductModel.attachments) {
          _listAttachment.add(attachment);
        }
      }

      return _listAttachment;
    } catch (e) {
      logger.e('getAttachmentsUpdateCacheRequest error $e');
    }
    return _listAttachment;
  }
}
