import 'dart:convert';
import 'dart:io';

import 'package:ambulancia_app/models/conduct_model.dart';
import 'package:ambulancia_app/shared/api/attendance/notification_of_amounts_of_data_pending_synchronization.api.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/environment_helper.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/services/sync-offline.service.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/offline-first/offline_first.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/cid.table.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/sync.table.dart';
import 'package:ambulancia_app/shared/utils/sync.utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:path/path.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';

class ConductApi {
  final UnimedHttpClient? httpClient;
  final logger = UnimedLogger(className: 'ConductApi');

  ConductApi({this.httpClient});

  Future<String> sendConduct({required ConductModel conductModel}) async {
    final endpoint =
        'ambulance/attendance/${conductModel.numAtendimento}/conduct';
    int numAtendimento = conductModel.numAtendimento;
    final url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';
    String _body = jsonEncode(conductModel.toJson());
    final String token =
        await Locator.instance.get<AuthApi>().tokenPerfilAppsNew();
    try {
      await _persistSendConductResponse(
        value: _body,
        numAtendimento: numAtendimento,
      );
      if (await _checkIfThereIsAPipelineToBeSynchronized(numAtendimento) ==
          true) {
        await _persistSendConduct(
          numAtendimento: numAtendimento,
          body: _body,
          url: url,
          headers: {
            "Authorization": "Bearer $token",
            "Content-Type": "application/json",
          },
          typeRequest: SYNC_TYPE_REQUEST.POST,
          exception: NoInternetException(),
        );
        return MessageException.sucessConduta;
      } else {
        final response =
            await this.httpClient!.post(Uri.parse(url), body: _body, headers: {
          "Authorization": "Bearer $token",
          "Content-Type": "application/json",
        });

        if (response.statusCode == 201) {
          //response no content sucesso
          logger.d('sendConduct success: statusCode: ${response.statusCode}');
          return MessageException.sucessConduta;
        } else {
          logger.e(
              'sendConduct error - statusCode: ${response.statusCode} body: ${response.body}');
          await _persistSendConduct(
            numAtendimento: numAtendimento,
            body: _body,
            url: url,
            headers: {
              "Authorization": "Bearer $token",
              "Content-Type": "application/json",
            },
            typeRequest: SYNC_TYPE_REQUEST.POST,
            exception: NoInternetException(),
          );
          return MessageException.sucessConduta;
        }
      }
    } on ServiceTimeoutException catch (ex) {
      logger.e('sendConduct ServiceTimeoutException: $ex');
      await _persistSendConduct(
        numAtendimento: numAtendimento,
        body: _body,
        url: url,
        headers: {
          "Authorization": "Bearer $token",
          "Content-Type": "application/json",
        },
        typeRequest: SYNC_TYPE_REQUEST.POST,
        exception: NoInternetException(),
      );
      return MessageException.sucessConduta;
    } on NoInternetException catch (ex) {
      logger.e('sendConduct NoInternetException: $ex');

      await _persistSendConduct(
        numAtendimento: numAtendimento,
        body: _body,
        url: url,
        headers: {
          "Authorization": "Bearer $token",
          "Content-Type": "application/json",
        },
        typeRequest: SYNC_TYPE_REQUEST.POST,
        exception: NoInternetException(),
      );
      return MessageException.sucessConduta;
    } on NotFoundException catch (ex) {
      logger.e('sendConduct NotFoundException: $ex');
      await _persistSendConduct(
        numAtendimento: numAtendimento,
        body: _body,
        url: url,
        headers: {
          "Authorization": "Bearer $token",
          "Content-Type": "application/json",
        },
        typeRequest: SYNC_TYPE_REQUEST.POST,
        exception: NoInternetException(),
      );
      return MessageException.sucessConduta;
    } on UnimedException catch (ex) {
      logger.e('sendConduct ${ex.runtimeType}: $ex');
      await _persistSendConduct(
        numAtendimento: numAtendimento,
        body: _body,
        url: url,
        headers: {
          "Authorization": "Bearer $token",
          "Content-Type": "application/json",
        },
        typeRequest: SYNC_TYPE_REQUEST.POST,
        exception: NoInternetException(),
      );
      return MessageException.sucessConduta;
    } on SocketException catch (e) {
      logger.e('sendConduct SocketException: $e');
      await _persistSendConduct(
        numAtendimento: numAtendimento,
        body: _body,
        url: url,
        headers: {
          "Authorization": "Bearer $token",
          "Content-Type": "application/json",
        },
        typeRequest: SYNC_TYPE_REQUEST.POST,
        exception: NoInternetException(),
      );
      return MessageException.sucessConduta;
    } catch (ex) {
      logger.e('sendConduct catch exception: $ex');
      await _persistSendConduct(
        numAtendimento: numAtendimento,
        body: _body,
        url: url,
        headers: {
          "Authorization": "Bearer $token",
          "Content-Type": "application/json",
        },
        typeRequest: SYNC_TYPE_REQUEST.POST,
        exception: NoInternetException(),
      );
      return MessageException.sucessConduta;
    }
  }

  Future<bool> _checkIfThereIsAPipelineToBeSynchronized(
    int numAtendimento,
  ) async {
    String path = join(await getDatabasesPath(), 'ambulancia.db');
    Database _database = await openDatabase(path);
    String _tableName =
        'tbSync${const String.fromEnvironment('environment') == "PROD" ? '_prod' : '_dev'}';

    final endpoint = '/unimedurgente/atendimento/observacao-conduta';
    String url = '${const String.fromEnvironment('perfilAppsUrl')}$endpoint';

    final queryResult = await _database.rawQuery(
      'SELECT * FROM $_tableName WHERE ${SyncTableSQLiteColums.TYPE_REQUEST} = ? AND ${SyncTableSQLiteColums.JSON} LIKE ? AND ${SyncTableSQLiteColums.URL} = ?',
      ['PATCH', '%"numAtendimento":$numAtendimento%', '$url'],
    );

    return queryResult.isNotEmpty;
  }

  Future<void> _persistSendConductResponse(
      {required value, required numAtendimento}) async {
    try {
      await OfflineFirst.persistResponse(
        customID:
            '${SYNC_CATEGORY_API.ConductApi.name}${SYNC_CONDUCTAPI_REQUEST.sendConduct.name}$numAtendimento',
        category: '$numAtendimento',
        body: value,
      );

      Locator.instance
          .get<NotificationOfAmountsOfDataPendingSynchronizationApi>()
          .addNotificationOfAmountsOfDataPendingSynchronization();
    } catch (e) {
      logger.e('_persistSendConductResponse catch Exception $e');
    }
  }

  Future<void> _persistSendConduct(
      {required numAtendimento,
      required String body,
      required String url,
      required Map<String, String> headers,
      required SYNC_TYPE_REQUEST typeRequest,
      required exception}) async {
    try {
      await OfflineFirst.persistRequest(
        customId:
            '${SYNC_CATEGORY_API.ConductApi.name}${SYNC_CONDUCTAPI_REQUEST.sendConduct.name}$numAtendimento',
        body: body,
        url: url,
        headers: headers,
        typeRequest: typeRequest,
        isPersistRequestConduct: true,
      );

      Locator.instance
          .get<NotificationOfAmountsOfDataPendingSynchronizationApi>()
          .addNotificationOfAmountsOfDataPendingSynchronization();
    } catch (e) {
      logger.e('_persistSendClinicEvaluation catch Exception $e');
      throw exception;
    }
  }

  Future<void> loadCIDFromCSV({bool forceCidFromCsv = false}) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      if (forceCidFromCsv) {
        logger.i("loadCIDFromCSV => forçando a criação da base...");
        await prefs.remove(EnvironmentHelper.getCidTableSharedPreferences());
      }
      final String? _pathPref =
          prefs.getString(EnvironmentHelper.getCidTableSharedPreferences());
      if (_pathPref == null || _pathPref != EnvironmentHelper.getPathCsv()) {
        logger.i("loadCIDFromCSV => criando base...");
        String data =
            await rootBundle.loadString(EnvironmentHelper.getPathCsv());
        List<String> result = data.split("\n");

        await addLinesToDatabase(result);
      } else {
        List<CidRecordSQLite> getList = await CidTableSQLite().listAll();

        if (getList.isEmpty) {
          logger.i(
              "loadCIDFromCSV => base já existente, porém sem dados... Forçando a criação da base...");
          await loadCIDFromCSV(
            forceCidFromCsv: true,
          );
        }
      }
    } catch (e) {
      logger.e("loadCIDFromCSV => erro ao carregar CID do CSV: $e");
      throw UnimedException(MessageException.syncErrorListDiagnoses);
    }
  }

  // TODO: externalizate table name and colums names
  // Lines in format CSV with separator |
  Future<void> addLinesToDatabase(List<String> lines) async {
    try {
      final Stopwatch stopwatch = Stopwatch()..start();

      final sqliteTable = CidTableSQLite();

      String auxSQL =
          "INSERT INTO ${sqliteTable.tableName} ('${CidTableSQLiteColums.COLUMN_COD_CID}', '${CidTableSQLiteColums.COLUMN_DV_CID}', '${CidTableSQLiteColums.COLUMN_DESCRICAO}') VALUES ";

      List<String> linesArray = [];

      for (String line in lines) {
        final reg = line.split('|');

        String insertLine = "('${reg[0]}', '${reg[1]}', '${reg[2]}') ";

        linesArray.add(insertLine);
      }

      // Insert expected
      // INSERT INTO 'tablename' ('column1', 'column2') VALUES
      // ('data1', 'data2'),
      // ('data1', 'data2'),
      // ('data1', 'data2'),
      // ('data1', 'data2');

      final insertLinesSQL = auxSQL + linesArray.join(",") + ";";

      bool success =
          await sqliteTable.addByInsert(insertLinesSQL, linesArray.length);

      debugPrint('load to sql success? $success');

      if (success) {
        //  save in shared preferences, PATH_CSV
        SharedPreferences prefs = await SharedPreferences.getInstance();
        prefs.setString(EnvironmentHelper.getCidTableSharedPreferences(),
            EnvironmentHelper.getPathCsv());
      } else {
        throw ConductException(MessageException.syncErrorListDiagnoses);
      }
      logger.d(
          'add inserted - time: ${stopwatch.elapsed.inSeconds} in Seconds to add to database');
      stopwatch.stop();
    } catch (e) {
      throw UnimedException(MessageException.syncErrorListDiagnoses);
    }
  }

  Future<List<CidRecordSQLite>> getListDb(String text) async {
    final Stopwatch stopwatch = Stopwatch()..start();
    final _listCidDb = await CidTableSQLite().getCidByDesc(text);
    logger.i(
        'getListDb - time: ${stopwatch.elapsed.inSeconds} in Seconds to list from database');

    stopwatch.stop();

    return _listCidDb;
  }
}
