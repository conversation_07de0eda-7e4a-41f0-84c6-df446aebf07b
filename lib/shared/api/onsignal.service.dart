// import 'package:ambulancia_app/shared/utils/logger_print.dart';
// import 'package:intl/intl.dart';
// import 'package:onesignal_flutter/onesignal_flutter.dart';

// import '../flavor-config.dart';

// class OneSignalService {
//   final logger = UnimedLogger(className: 'OneSignalService');

//   String? _deviceId;
//   bool? isPermitted;

//   OneSignalService() {
//     this.isPermitted = false;
//   }

//   String? get deviceId {
//     if (FlavorConfig.isTest() || _deviceId == null || _deviceId!.isEmpty) {
//       return 'device-id-test';
//     } else {
//       return _deviceId;
//     }
//   }

//   Future<void> init() async {
//     if (!FlavorConfig.isTest()) {
//       OneSignal.shared
//           .setSubscriptionObserver((OSSubscriptionStateChanges changes) {
//         logger.d(
//             '${DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now())} playerId OneSignal subscriptionObserver => ${changes.to.userId}');
//         _deviceId = changes.to.userId;
//       });
//       // OneSignal.shared.setInFocusDisplayType(OSNotificationDisplayType.none);
//       OneSignal.shared.setAppId(
//         FlavorConfig.instance!.values.oneSignalId,
//       );

//       OneSignal.shared.requiresUserPrivacyConsent().then((isRequired) {
//         isPermitted = !isRequired;
//       });

//       OneSignal.shared.getDeviceState().then((value) {
//         _deviceId = value!.userId;
//       });

//       // OneSignal.shared
//       //     .getPermissionSubscriptionState()
//       //     .then((OSPermissionSubscriptionState status) {
//       //   // print('status ${status.subscriptionStatus.userId}');
//       //   _deviceId = status.subscriptionStatus.userId;
//       // });

//       // OneSignal.shared.setSubscription(true);

//       OneSignal.shared.disablePush(false);

//       // final status = await OneSignal.shared.getPermissionSubscriptionState();
//       // _deviceId = status.subscriptionStatus.userId;

//       logger.d(
//           '${DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now())} playerId OneSignal after subscribed => $_deviceId');
//     }
//   }

//   // Future<bool> promptForPermission() async {
//   //   if (FlavorConfig.isTest()) {
//   //     return true;
//   //   } else {
//   //     if (isPermitted) {
//   //       print('Permission to notification already accept');
//   //       OneSignal.shared.setSubscription(true);
//   //       return isPermitted;
//   //     } else {
//   //       return await OneSignal.shared
//   //           .promptUserForPushNotificationPermission(fallbackToSettings: true);
//   //     }
//   //   }
//   // }

//   Future<void> forceUpdateDeviceId() async {
//     OneSignal.shared.setSubscriptionObserver((changes) {
//       _deviceId = changes.from.userId;
//     });
//     // final status = await OneSignal.shared.getPermissionSubscriptionState();
//     // _deviceId = status.subscriptionStatus.userId;

//     logger.d(
//         '${DateFormat("yyyy-MM-dd HH:mm:ss").format(DateTime.now())} playerId OneSignal forceUpdateDeviceId => $_deviceId');
//   }

//   void logout() async {
//     if (!FlavorConfig.isTest()) {
//       await OneSignal.shared.disablePush(true);
//     }
//   }

//   void setEmail(String email) {
//     OneSignal.shared.setEmail(email: email);
//   }

//   /// Salva todas as notificações localmente
//   void notificationReceived() async {
//     // var deviceState = await OneSignal.shared.getDeviceState();

//     OneSignal().setNotificationWillShowInForegroundHandler((event) {
//       logger.d('notificationReceived => ${event.notification}');
//       print('notification received body ${event.notification.body}');
//       // print('notification received app ${event.notification.appInFocus}');
//       print('notification received ${event.notification.buttons!.first.text}');
//     });
//     // OneSignal.shared
//     //     .setNotificationReceivedHandler((OSNotification notification) {
//     //   logger.d('notificationReceived => $notification');
//     //   print('notification received body ${notification.payload.body}');
//     //   print('notification received app ${notification.appInFocus}');
//     //   print('notification received ${notification.payload.buttons.first.text}');

//     // Salvando ou editando notificação recebida
//     // NotificacaoTableSQLite()
//     //   ..addOrUpdate(NotificacaoRecordSQLite.fromOSNotification(notification));
//     //});
//   }

//   // void notificationOpened() {
//   //   OneSignal.shared.setNotificationOpenedHandler(
//   //       (OSNotificationOpenedResult notification) {
//   //     logger.d('notificationOpened => $notification');
//   //     // Salvando ou editando notificação aberta
//   //     // NotificacaoTableSQLite()
//   //     //   ..addOrUpdate(
//   //     //       NotificacaoRecordSQLite.fromOSNotificationResult(notification));
//   //   });
//   // }
// }
