import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:ambulancia_app/models/clinic-evaluation.model.dart';
import 'package:ambulancia_app/shared/api/attendance/notification_of_amounts_of_data_pending_synchronization.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/http-client.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:ambulancia_app/shared/services/sync-offline.service.dart';
import 'package:ambulancia_app/shared/utils/http.dart';
import 'package:ambulancia_app/shared/utils/logger_print.dart';
import 'package:ambulancia_app/shared/utils/offline-first/offline_first.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/sync.table.dart';
import 'package:ambulancia_app/shared/utils/sync.utils.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

class ClinicEvaluationApi {
  final UnimedHttpClient? httpClient;
  final logger = UnimedLogger(className: 'ClinicEvaluationApi');

  ClinicEvaluationApi({this.httpClient});

  Future<ResponseClinicEvaluation> getClinicEvaluationConfig(
      String numAttendance) async {
    try {
      final url =
          '${const String.fromEnvironment('ambulanciaOsbUrl')}/unimedurgente/avaliacao-clinica/configuracao';
      final response = await this.httpClient!.get(Uri.parse(url), headers: {
        "Authorization": HttpUtils.getAuthorizationBasicAmbulanciaOsb(),
        "Content-Type": "application/json",
      });

      if (response.statusCode == 200) {
        await _persistConfigEvaluationResponse(
            value: response.body, numAtendimento: numAttendance);
        logger.i(
            'getClinicEvaluationConfig success: statusCode: ${response.statusCode} body: ${response.body}');

        return ResponseClinicEvaluation.fromJson(jsonDecode(response.body));
      } else {
        logger.e(
            'getClinicEvaluationConfig error - statusCode: ${response.statusCode} body: ${response.body}');
        throw ClinicEvaluation(MessageException.general);
      }
    } on NotFoundException catch (ex) {
      logger.e('getClinicEvaluationConfig NotFound ${ex.message}');
      final persitData = await _getPersistConfigEvaluation(numAttendance);
      if (persitData != null) {
        return persitData;
      }
      throw NotFoundException();
    } on NoInternetException catch (ex) {
      logger.e('getClinicEvaluationConfig NoInternet ${ex.message}');

      final persitData = await _getPersistConfigEvaluation(numAttendance);
      if (persitData != null) {
        return persitData;
      }
      throw NoInternetException();
    } on ServiceTimeoutException catch (ex) {
      logger.e('getClinicEvaluationConfig Timeout ${ex.message}');

      final persitData = await _getPersistConfigEvaluation(numAttendance);
      if (persitData != null) {
        return persitData;
      }
      throw ServiceTimeoutException();
    } on UnimedException catch (ex) {
      logger.e('getClinicEvaluationConfig ${ex.runtimeType}: $ex');
      final persitData = await _getPersistConfigEvaluation(numAttendance);
      if (persitData != null) {
        return persitData;
      }
      throw UnimedException(MessageException.general);
    } catch (ex) {
      logger.e('getClinicEvaluationConfig error exception: $ex');
      final persitData = await _getPersistConfigEvaluation(numAttendance);
      if (persitData != null) {
        return persitData;
      }
      throw ClinicEvaluation(MessageException.general);
    }
  }

  Future<RequestClinicEvaluation?> getClinicEvaluationData(
      String numAttendance) async {
    try {
      final url =
          '${const String.fromEnvironment('ambulanciaOsbUrl')}/unimedurgente/avaliacao-clinica/atendimento/$numAttendance';
      final response = await this.httpClient!.get(Uri.parse(url), headers: {
        "Authorization": HttpUtils.getAuthorizationBasicAmbulanciaOsb(),
        "Content-Type": "application/json",
      });
      logger.i('getClinicEvaluationData $url');
      if (response.statusCode == 200) {
        await _persistDataEvaluationResponse(
            value: response.body, numAtendimento: numAttendance);
        final result =
            RequestClinicEvaluation.fromJson(jsonDecode(response.body));

        logger.i(
            'getClinicEvaluationData success: statusCode: ${response.statusCode}, body:${response.body}');
        return result;
      } else if (response.statusCode == 500) {
        logger.e('getClinicEvaluationData error 500 - body: ${response.body}');
        return RequestClinicEvaluation();
        // final result = await _getDataEvaluation(numAtendimento: numAttendance);

        // return result;
      } else {
        logger.e(
            'getClinicEvaluationData error - statusCode: ${response.statusCode} body: ${response.body}');
        return null;
      }
    } on NotFoundException catch (_) {
      final persitData =
          await _getDataEvaluation(numAtendimento: numAttendance);
      return persitData;
    } on NoInternetException catch (ex) {
      logger.e('getClinicEvaluationData NoInternet ${ex.message}');

      final persitData =
          await _getDataEvaluation(numAtendimento: numAttendance);
      if (persitData != null) {
        return persitData;
      }
      throw NoInternetException();
    } on ServiceTimeoutException catch (ex) {
      logger.e('getClinicEvaluationData Timeout ${ex.message}');
      final persitData =
          await _getDataEvaluation(numAtendimento: numAttendance);
      if (persitData != null) {
        return persitData;
      }
      throw ServiceTimeoutException();
    } on UnimedException catch (ex) {
      logger.e('getClinicEvaluationData ${ex.runtimeType}: $ex');
      final persitData =
          await _getDataEvaluation(numAtendimento: numAttendance);
      if (persitData != null) {
        return persitData;
      }
      throw UnimedException(MessageException.general);
    } catch (ex) {
      logger.e('getClinicEvaluationData error exception: $ex');
      final persitData =
          await _getDataEvaluation(numAtendimento: numAttendance);
      if (persitData != null) {
        return persitData;
      }
      throw ClinicEvaluation(MessageException.general);
    }
  }

  _persistSendClinicEvaluation(
      {required numAtendimento,
      required String body,
      required String url,
      required Map<String, String> headers,
      required SYNC_TYPE_REQUEST typeRequest,
      required exception}) async {
    try {
      await OfflineFirst.persistRequest(
        customId:
            '${SYNC_CATEGORY_API.ClinicEvaluationApi.name}${SYNC_CLINICEVALUATIONAPI_REQUEST.sendClinicEvaluation.name}$numAtendimento',
        body: body,
        url: url,
        headers: headers,
        typeRequest: typeRequest,
      );

      Locator.instance
          .get<NotificationOfAmountsOfDataPendingSynchronizationApi>()
          .addNotificationOfAmountsOfDataPendingSynchronization();
    } catch (e) {
      logger.e('_persistSendClinicEvaluation catch Exception $e');
      throw exception;
    }
  }

  Future<bool> _checkIfThereIsAClinicalAssessmentToBeSynchronized(
    int numAtendimento,
  ) async {
    String path = join(await getDatabasesPath(), 'ambulancia.db');
    Database _database = await openDatabase(path);
    String _tableName =
        'tbSync${const String.fromEnvironment('environment') == "PROD" ? '_prod' : '_dev'}';

    final endpoint = '/unimedurgente/avaliacao-clinica';
    String url = '${const String.fromEnvironment('ambulanciaOsbUrl')}$endpoint';

    final queryResult = await _database.rawQuery(
      'SELECT * FROM $_tableName WHERE ${SyncTableSQLiteColums.TYPE_REQUEST} IN (?, ?) AND ${SyncTableSQLiteColums.JSON} LIKE ? AND ${SyncTableSQLiteColums.URL} = ?',
      ['POST', 'PATCH', '%"numAtendimento":$numAtendimento%', '$url'],
    );

    return queryResult.isNotEmpty;
  }

  Future<String> sendClinicEvaluation(
      {required RequestClinicEvaluation requestClinicEvaluation,
      required bool isUpdate}) async {
    late String url;
    late Map<String, String> headers;
    try {
      if (requestClinicEvaluation.glasgow != null) if (requestClinicEvaluation
                  .glasgow! >
              15 ||
          requestClinicEvaluation.glasgow! < 3) {
        throw ClinicEvaluation(
            'Valores de glasgow não permitidos. Verifique o formulário');
      }

      url =
          '${const String.fromEnvironment('ambulanciaOsbUrl')}/unimedurgente/avaliacao-clinica';
      headers = {
        "Authorization": HttpUtils.getAuthorizationBasicAmbulanciaOsb(),
        "Content-Type": "application/json",
      };
      final body = jsonEncode(requestClinicEvaluation.toJson());

      await _persistSendEvaluationResponse(
          value: body, numAtendimento: requestClinicEvaluation.numAtendimento);

      if (await _checkIfThereIsAClinicalAssessmentToBeSynchronized(
              requestClinicEvaluation.numAtendimento!) ==
          true) {
        _persistSendClinicEvaluation(
          numAtendimento: requestClinicEvaluation.numAtendimento,
          body: jsonEncode(requestClinicEvaluation.toJson()),
          url: url,
          headers: headers,
          typeRequest:
              isUpdate ? SYNC_TYPE_REQUEST.PATCH : SYNC_TYPE_REQUEST.POST,
          exception: NoInternetException(),
        );
        return MessageException.sucessClinicalEvaluation;
      } else {
        var response;
        if (isUpdate)
          response = await this
              .httpClient!
              .patch(Uri.parse(url), body: body, headers: headers);
        else
          response = await this
              .httpClient!
              .post(Uri.parse(url), body: body, headers: headers);
        if (response.statusCode == 200) {
          logger.i(
              'sendClinicEvaluation success: statusCode: ${response.statusCode}');
          return MessageException.sucessClinicalEvaluation;
        } else {
          logger.e(
              'updateClinicEvaluation error - statusCode: ${response.statusCode} body: ${response.body}');
          throw ClinicEvaluation(jsonDecode(response.body));
        }
      }
    } on NotFoundException catch (ex) {
      logger.e('sendClinicEvaluation NotFoundException: $ex');
      await _persistSendClinicEvaluation(
        numAtendimento: requestClinicEvaluation.numAtendimento,
        body: jsonEncode(requestClinicEvaluation.toJson()),
        url: url,
        headers: headers,
        typeRequest:
            isUpdate ? SYNC_TYPE_REQUEST.PATCH : SYNC_TYPE_REQUEST.POST,
        exception: NoInternetException(),
      );
      return MessageException.sucessClinicalEvaluation;
    } on NoInternetException catch (ex) {
      logger.e('sendClinicEvaluation NoInternetException: $ex');
      await _persistSendClinicEvaluation(
        numAtendimento: requestClinicEvaluation.numAtendimento,
        body: jsonEncode(requestClinicEvaluation.toJson()),
        url: url,
        headers: headers,
        typeRequest:
            isUpdate ? SYNC_TYPE_REQUEST.PATCH : SYNC_TYPE_REQUEST.POST,
        exception: NoInternetException(),
      );
      return MessageException.sucessClinicalEvaluation;
    } on SocketException catch (e) {
      logger.e('sendClinicEvaluation SocketException: $e');
      await _persistSendClinicEvaluation(
        numAtendimento: requestClinicEvaluation.numAtendimento,
        body: jsonEncode(requestClinicEvaluation.toJson()),
        url: url,
        headers: headers,
        typeRequest:
            isUpdate ? SYNC_TYPE_REQUEST.PATCH : SYNC_TYPE_REQUEST.POST,
        exception: NoInternetException(),
      );
      return MessageException.sucessClinicalEvaluation;
    } on ServiceTimeoutException catch (ex) {
      logger.e('sendClinicEvaluation ServiceTimeoutException: $ex');
      await _persistSendClinicEvaluation(
        numAtendimento: requestClinicEvaluation.numAtendimento,
        body: jsonEncode(requestClinicEvaluation.toJson()),
        url: url,
        headers: headers,
        typeRequest:
            isUpdate ? SYNC_TYPE_REQUEST.PATCH : SYNC_TYPE_REQUEST.POST,
        exception: NoInternetException(),
      );
      return MessageException.sucessClinicalEvaluation;
    } on UnimedException catch (ex) {
      logger.e('sendClinicEvaluation ${ex.runtimeType}: ${ex.toString()}');
      await _persistSendClinicEvaluation(
        numAtendimento: requestClinicEvaluation.numAtendimento,
        body: jsonEncode(requestClinicEvaluation.toJson()),
        url: url,
        headers: headers,
        typeRequest:
            isUpdate ? SYNC_TYPE_REQUEST.PATCH : SYNC_TYPE_REQUEST.POST,
        exception: NoInternetException(),
      );
      return MessageException.sucessClinicalEvaluation;
    } catch (ex) {
      logger.e('sendClinicEvaluation error exception: $ex');
      await _persistSendClinicEvaluation(
        numAtendimento: requestClinicEvaluation.numAtendimento,
        body: jsonEncode(requestClinicEvaluation.toJson()),
        url: url,
        headers: headers,
        typeRequest:
            isUpdate ? SYNC_TYPE_REQUEST.PATCH : SYNC_TYPE_REQUEST.POST,
        exception: NoInternetException(),
      );
      return MessageException.sucessClinicalEvaluation;
    }
  }

  _persistConfigEvaluationResponse(
      {required value, required numAtendimento}) async {
    try {
      await OfflineFirst.persistResponse(
        customID:
            '${SYNC_CATEGORY_API.ClinicEvaluationApi.name}${SYNC_CLINICEVALUATIONAPI_REQUEST.getClinicEvaluationConfig.name}$numAtendimento',
        category: '$numAtendimento',
        body: value,
      );

      Locator.instance
          .get<NotificationOfAmountsOfDataPendingSynchronizationApi>()
          .addNotificationOfAmountsOfDataPendingSynchronization();
    } catch (e) {
      logger.e('_persistConfigEvaluation catch Exception $e');
    }
  }

  _persistDataEvaluationResponse(
      {required value, required numAtendimento}) async {
    try {
      await OfflineFirst.persistResponse(
        customID:
            '${SYNC_CATEGORY_API.ClinicEvaluationApi.name}${SYNC_CLINICEVALUATIONAPI_REQUEST.getClinicEvaluationData.name}$numAtendimento',
        category: '$numAtendimento',
        body: value,
      );

      Locator.instance
          .get<NotificationOfAmountsOfDataPendingSynchronizationApi>()
          .addNotificationOfAmountsOfDataPendingSynchronization();
    } catch (e) {
      logger.e('_persistDataEvaluationResponse catch Exception $e');
    }
  }

  _persistSendEvaluationResponse(
      {required value, required numAtendimento}) async {
    try {
      await OfflineFirst.persistResponse(
        customID:
            '${SYNC_CATEGORY_API.ClinicEvaluationApi.name}${SYNC_CLINICEVALUATIONAPI_REQUEST.sendClinicEvaluation.name}$numAtendimento',
        category: '$numAtendimento',
        body: value,
      );

      Locator.instance
          .get<NotificationOfAmountsOfDataPendingSynchronizationApi>()
          .addNotificationOfAmountsOfDataPendingSynchronization();
    } catch (e) {
      logger.e('_persistSendEvaluationResponse catch Exception $e');
    }
  }

  Future<RequestClinicEvaluation?> _getPersistDataEvaluation(
      {required numAtendimento}) async {
    final valuesCurrentJson = await OfflineFirst.getCacheResponse(
        customID:
            '${SYNC_CATEGORY_API.ClinicEvaluationApi.name}${SYNC_CLINICEVALUATIONAPI_REQUEST.getClinicEvaluationData.name}$numAtendimento');

    RequestClinicEvaluation? valuesCurrent;
    try {
      if (valuesCurrentJson != null) {
        valuesCurrent =
            RequestClinicEvaluation.fromJson(jsonDecode(valuesCurrentJson));
      } else {
        valuesCurrent = RequestClinicEvaluation();
      }
    } catch (_) {
      return RequestClinicEvaluation();
    }

    return valuesCurrent;
  }

  Future<RequestClinicEvaluation?> _getDataEvaluation(
      {required numAtendimento}) async {
    final valuesCurrentJson = await OfflineFirst.getCacheResponse(
        customID:
            '${SYNC_CATEGORY_API.ClinicEvaluationApi.name}${SYNC_CLINICEVALUATIONAPI_REQUEST.sendClinicEvaluation.name}$numAtendimento');

    RequestClinicEvaluation? valuesCurrent;
    if (valuesCurrentJson != null) {
      valuesCurrent =
          RequestClinicEvaluation.fromJson(jsonDecode(valuesCurrentJson));
    } else {
      valuesCurrent =
          await _getPersistDataEvaluation(numAtendimento: numAtendimento);
    }
    return valuesCurrent;
  }

  Future<ResponseClinicEvaluation?> _getPersistConfigEvaluation(
      numAtendimento) async {
    final valuesCurrentJson = await OfflineFirst.getCacheResponse(
        customID:
            '${SYNC_CATEGORY_API.ClinicEvaluationApi.name}${SYNC_CLINICEVALUATIONAPI_REQUEST.getClinicEvaluationConfig.name}$numAtendimento');
    ResponseClinicEvaluation? valuesCurrent;
    if (valuesCurrentJson != null) {
      valuesCurrent =
          ResponseClinicEvaluation.fromJson(jsonDecode(valuesCurrentJson));
    }
    return valuesCurrent;
  }
}
