
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';


class Snack {
  static SnackBar error(String message) {
    return SnackBar(
      content: Text(
        message,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: Colors.red,
    );
  }

  static SnackBar success(String message, {Duration? duration}) {
    if (duration == null) {
      duration = Duration(seconds: 5);
    }

    return SnackBar(
      duration: duration,
      content: Text(
        message,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: AmbulanceColors.green,
    );
  }

  static SnackBar warning(String message, {Duration? duration}) {
    if (duration == null) {
      duration = Duration(seconds: 5);
    }

    return SnackBar(
      duration: duration,
      content: Text(
        message,
        style: TextStyle(
          color: Colors.black,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: Colors.yellow,
    );
  }
}
