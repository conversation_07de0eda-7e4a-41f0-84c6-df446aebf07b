import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class InifityList extends StatefulWidget {
  const InifityList({
    required this.itemCount,
    required this.itemBuilder,
    required this.onMaxScroll,
    this.loading = false,
  });
  final int itemCount;
  final Widget Function(BuildContext, int) itemBuilder;
  final VoidCallback onMaxScroll;
  final bool loading;

  @override
  _InifityListState createState() => _InifityListState();
}

class _InifityListState extends State<InifityList> {
  final ScrollController _scrollController = ScrollController();

  void initState() {
    _scrollController.addListener(_scrollListener);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.offset >=
            _scrollController.position.maxScrollExtent &&
        !_scrollController.position.outOfRange) {
      widget.onMaxScroll();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            itemCount: widget.itemCount,
            itemBuilder: widget.itemBuilder,
          ),
        ),
        if (widget.loading)
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: SpinKitThreeBounce(
              color: AmbulanceColors.green,
              size: 20,
            ),
          )
      ],
    );
  }
}
