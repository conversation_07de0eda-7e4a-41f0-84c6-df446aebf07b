import 'package:ambulancia_app/bloc/attendance_list/attendance_list_cubit.dart';
import 'package:ambulancia_app/bloc/auth/auth_cubit.dart';
import 'package:ambulancia_app/shared/i18n/i18n_helper.dart';
import 'package:ambulancia_app/shared/widgets/alert.dart';
import 'package:ambulancia_app/theme/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LogoutHandler {
  final _baseTranslate = 'home.header';

  void logout(BuildContext context) {
    Alert.open(
      context,
      title: I18nHelper.translate(context, '$_baseTranslate.logout.title'),
      actions: [
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: AmbulanceColors.greenDark,
            textStyle: TextStyle(color: Colors.white),
          ),
          child: Text(
              I18nHelper.translate(context, '$_baseTranslate.logout.confirm')),
          onPressed: () {
            context.read<AttendanceListCubit>().listattendances.clear();
            context.read<AuthCubit>().signout();
            Navigator.of(context).pop();
          },
        ),
      ],
    );
  }
}
