import 'package:flutter/material.dart';

class AmbulanceToggle extends StatefulWidget {
  final String? label;
  final Color? labelColor;
  final Color? activeTrackColor;
  final Color? inactiveTrackColor;
  final Color? buttonColor;
  final bool? forceValue;
  final bool? value;
  final Function(bool?)? onChanged;

  AmbulanceToggle({
    this.label,
    this.activeTrackColor,
    this.inactiveTrackColor,
    this.buttonColor,
    this.forceValue,
    this.value,
    this.onChanged,
    this.labelColor,
  });

  @override
  _AmbulanceToggleState createState() => _AmbulanceToggleState();
}

class _AmbulanceToggleState extends State<AmbulanceToggle>
    with SingleTickerProviderStateMixin {
  late Animation<Alignment> _animation;
  late AnimationController _animationController;
  bool? value;

  @override
  void initState() {
    value = widget.value ?? false;

    _animationController = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
    );

    _animation = Tween<Alignment>(
      begin: Alignment.centerLeft,
      end: Alignment.centerRight,
    ).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.linear),
    );

    if (value!) _animationController.forward();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.forceValue != null) {
      value = widget.forceValue;
      value! ? _animationController.forward() : _animationController.reverse();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (animation, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.label != null) ..._labelWidget(),
            Row(
              children: [
                GestureDetector(
                  onTap: () => _toggleValue(),
                  child: Container(
                    height: 40,
                    width: 80,
                    decoration: BoxDecoration(
                      color: value!
                          ? widget.activeTrackColor ?? Colors.grey
                          : widget.inactiveTrackColor ?? Colors.grey,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Align(
                      alignment: _animation.value,
                      child: Container(
                        height: 34,
                        width: 34,
                        margin: const EdgeInsets.only(left: 4, right: 4),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: widget.buttonColor ?? Colors.white,
                        ),
                      ),
                    ),
                  ),
                ),
                _valueWidget(),
              ],
            ),
          ],
        );
      },
    );
  }

  void _toggleValue() {
    setState(() {
      value = !value!;
      widget.onChanged!(value);
    });

    _animationController.isCompleted
        ? _animationController.reverse()
        : _animationController.forward();
  }

  List<Widget> _labelWidget() {
    return [
      Text(
        widget.label!,
        style: TextStyle(
            color: widget.labelColor ?? widget.buttonColor, fontSize: 16),
      ),
      SizedBox(height: 4.0),
    ];
  }

  Widget _valueWidget() {
    return Padding(
      padding: const EdgeInsets.only(left: 7.0),
      child: Text(
        value! ? 'Sim' : 'Não',
        style: TextStyle(fontSize: 20, color: widget.buttonColor),
      ),
    );
  }
}
