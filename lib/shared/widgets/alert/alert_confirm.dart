import 'package:flutter/material.dart';

import '../../../theme/colors.dart';

class AlertConfirm {
  static void open(BuildContext context,
      {String? title = '',
      String text = '',
      bool hasButtonClose = true,
      String textButtonClose = 'Fechar',
      List<String>? lines,
      List<Widget>? actions,
      Function? callbackClose}) async {
    List<Widget> _texts = [
      Text(
        text,
        style: const TextStyle(
            color:  UnimedColors.grayDark2 , fontWeight: FontWeight.bold),
      )
    ];
    List<Widget> _actions = [];

    if (text.isNotEmpty && lines != null && lines.isNotEmpty) {
      _texts.addAll(lines.map((l) => Text(
            l,
            style: TextStyle(
              color:unimedGreen.shade900 ,
            ),
          )));
    }

    if (actions != null && actions.isNotEmpty) {
      _actions = actions;
    }

    if (hasButtonClose) {
      _actions.add(
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            foregroundColor: Colors.white,
            backgroundColor: unimedOrange ,
          ),
          child: Text(textButtonClose),
          onPressed: () {
            Navigator.of(context).pop();

            if (callbackClose != null) {
              callbackClose();
            }
          },
        ),
      );
    }

    return showGeneralDialog<void>(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 400),
      pageBuilder: (BuildContext context, Animation animation,
          Animation secondaryAnimation) {
        return Container();
      },
      transitionBuilder: (BuildContext context, a1, a2, widget) {
        final curvedValue = Curves.easeInOutBack.transform(a1.value) - 1.0;

        return Transform(
          transform: Matrix4.translationValues(0.0, curvedValue * 200, 0.0),
          child: Opacity(
            opacity: a1.value,
            child: AlertDialog(
              backgroundColor: unimedGreen.shade50 ,
              title: Text(
                '$title',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: unimedGreen.shade900 ,
                ),
              ),
              content: SingleChildScrollView(
                child: Wrap(
                  alignment: WrapAlignment.center,
                  children: _texts,
                ),
              ),
              actions: _actions,
            ),
          ),
        );
      },
    );
  }
}

class UnimedConfirmDialog extends StatelessWidget {
  const UnimedConfirmDialog(
      {Key? key,
      required this.onPressed,
      required this.onClose,
      required this.textWidget,
      required this.textDescription,
      this.textButton = "Ok",
      this.textClose = "Fechar",
      this.colorIcon = UnimedColors.greenDark ,
      this.colorButton = UnimedColors.orange})
      : super(key: key);  

  final VoidCallback onPressed;
  final VoidCallback onClose;
  final Widget textWidget;
  final Widget textDescription;
  final String textButton;
  final String textClose;
  final Color colorIcon;
  final Color colorButton;

  @override
  Widget build(context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      title: textWidget,
      content: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            textDescription,
            const Divider(
              height: 5.0,
              color: Colors.transparent,
            ),
            const SizedBox(height: 10.0),
            Wrap(
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(left: 1.0),
                  child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.all(10.0),
                        foregroundColor: Colors.white,
                        backgroundColor: colorButton,
                      ),
                      child: Text(textButton),
                      onPressed: onPressed),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.all(10.0),
                        foregroundColor: Colors.white,
                        backgroundColor: unimedOrange ,
                      ),
                      child: Text(textClose),
                      onPressed: onClose),
                ),
              ],
              direction: Axis.horizontal,
              runAlignment: WrapAlignment.center,
              crossAxisAlignment: WrapCrossAlignment.center,
              alignment: WrapAlignment.center,
            )
          ]),
      backgroundColor: Colors.white,
    );
  }
}
