import 'package:flutter/material.dart';

class UnimedErrorDialog extends StatelessWidget {
  const UnimedErrorDialog(
      {Key? key,
      required this.onPressedPositive,
      required this.textWidget,
      required this.textDescription,
      this.textPositiveButton,
      this.textNegativeButton,
      this.servico,
      this.colorIcon = Colors.green/* UnimedColors.greenDark */,
      this.colorButton = Colors.orange/* UnimedColors.orange} */})
      : super(key: key);

  final VoidCallback onPressedPositive;
  final Widget textWidget;
  final Widget textDescription;
  final String? textPositiveButton;
  final String? textNegativeButton;
  final String? servico;
  final Color colorIcon;
  final Color colorButton;

  // static showAlert(context, carteira, message, servico) {
  //   showDialog(
  //     barrierDismissible: false,
  //     context: context,
  //     builder: (BuildContext context) => UnimedErrorDialog(
  //       textWidget: const Text('Falha ao atender sua solicitação'),
  //       textDescription: Text(message),
  //       textPositiveButton: 'Enviar Feedback',
  //       textNegativeButton: 'Fechar',
  //       colorButton: UnimedColors.green,
  //       onPressedPositive: () {
  //         //Navigator.of(context).pop();
  //         showDialog(
  //           barrierDismissible: false,
  //           context: context,
  //           builder: (BuildContext context) {
  //             return AlertEvaluation.exibeAlert(
  //               context: context,
  //               servico: AvaliacaoLabels.AUTORIZACAO,
  //               carteira: carteira,
  //             );
  //           },
  //         );
  //       },
  //     ),
  //   );
  // }

  @override
  Widget build(context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15.0)),
      title: textWidget,
      content: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            textDescription,
            const Divider(
              height: 5.0,
              color: Colors.transparent,
            ),
            const SizedBox(height: 10.0),
            Wrap(
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.all(10.0),
                        foregroundColor: Colors.white,
                        backgroundColor: colorButton,
                      ),
                      child: Text(textPositiveButton!),
                      onPressed: () {
                        Navigator.of(context).pop();
                        onPressedPositive();
                      }),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 8.0),
                  child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.all(10.0),
                        foregroundColor: Colors.white,
                        backgroundColor:Colors.orange   /* unimedOrange */,
                      ),
                      child: Text(textNegativeButton!),
                      onPressed: () => {Navigator.of(context).pop()}),
                ),
              ],
              direction: Axis.horizontal,
              crossAxisAlignment: WrapCrossAlignment.center,
              alignment: WrapAlignment.center,
            )
          ]),
      backgroundColor: Colors.white,
    );
  }
}
