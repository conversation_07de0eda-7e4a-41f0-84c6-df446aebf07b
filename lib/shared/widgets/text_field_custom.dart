import 'package:ambulancia_app/theme/colors.dart';
import 'package:ambulancia_app/theme/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TextFieldCustom extends StatefulWidget {
  TextFieldCustom({
    super.key,
    required this.controller,
    required this.validator,
    required this.title,
    required this.label,
    this.keyboardType,
    this.inputFormatters,
    this.maxLines,
  });

  final TextEditingController controller;
  final String? Function(String?)? validator;
  final String title;
  final String label;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final int? maxLines;

  @override
  State<TextFieldCustom> createState() => _TextFieldCustomState();
}

class _TextFieldCustomState extends State<TextFieldCustom> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(bottom: ConstantsTheme.doublePadding),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.title,
            style: TextStyle(fontSize: 20),
          ),
          SizedBox(height: ConstantsTheme.padding * 0.5),
          TextFormField(
            controller: widget.controller,
            keyboardType: widget.keyboardType,
            decoration: InputDecoration(
              hintText: widget.label,
              focusedBorder: OutlineInputBorder(
                borderSide: BorderSide(color: AmbulanceColors.green),
              ),
              border: OutlineInputBorder(
                borderSide: BorderSide(color: AmbulanceColors.green),
              ),
              contentPadding: EdgeInsets.fromLTRB(15.0, 15.0, 20.0, 15.0),
            ),
            style: TextStyle(
              color: AmbulanceColors.grayDark,
              fontSize: 18,
            ),
            validator: widget.validator,
            inputFormatters: widget.inputFormatters,
            maxLines: widget.maxLines,
          ),
        ],
      ),
    );
  }
}
