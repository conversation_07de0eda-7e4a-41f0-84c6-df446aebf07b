part of 'attendance_route_cubit.dart';

abstract class AttendanceRouteState extends Equatable {
  const AttendanceRouteState();
}

class InitialAttendanceRouteState extends AttendanceRouteState {
  @override
  List<Object> get props => [];
}

class LoadingAttendanceRouteState extends AttendanceRouteState {
  @override
  List<Object> get props => [];
}

class ErrorAttendanceRouteState extends AttendanceRouteState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorAttendanceRouteState(this.message);
}

class LoadedAttendanceListState extends AttendanceRouteState {
  final List<AttendanceModel>? attendances;

  @override
  List<Object?> get props => [attendances];

  LoadedAttendanceListState({this.attendances});
}

class LoadedAttendanceRouteState extends AttendanceRouteState {
  final AttendanceModel? attendance;
  final int? step;
  final List<LatLng>? routeCoords;
  final double? distance;
  final double? duration;
  final String? prevision;
  final Position? currentPosition;

  @override
  List<Object?> get props =>
      [attendance, step, routeCoords, distance, duration];

  LoadedAttendanceRouteState(
      {this.attendance,
      this.step,
      this.routeCoords,
      this.distance,
      this.duration,
      this.prevision,
      this.currentPosition});
}
