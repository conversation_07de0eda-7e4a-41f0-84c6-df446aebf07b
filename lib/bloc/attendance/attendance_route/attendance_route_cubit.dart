import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/services/geolocation.service.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/cupertino.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';

part 'attendance_route_state.dart';

class AttendanceRouteCubit extends Cubit<AttendanceRouteState> {
  AttendanceRouteCubit() : super(InitialAttendanceRouteState());

  List<LatLng>? _routeCoords;
  Position? currentLocation;
  double? _distance;
  double? _duration;
  String? prevision;

  double? get distance => _distance;
  List<LatLng>? get routeCoords => _routeCoords;
  double? get duration => _duration;

  Future<void> searchAttendaceRoute(LatLng destiny) async {
    try {
      emit(LoadingAttendanceRouteState());

      PermissionStatus permission = await Permission.location.status;

      if (permission == PermissionStatus.denied) {
        await Permission.location.request();
      } else {
        currentLocation = await Locator.instance
            .get<GeolocationService>()
            .getCurrentPosition();

        // final GoogleMapPolyline googleMapPolyline = new GoogleMapPolyline(
        //   apiKey: "AteZr6ftFaxXQNLmrOyYhlezL0CFbW5t0pyqdg_kLJo",
        // );

        // _routeCoords = await googleMapPolyline.getCoordinatesWithLocationHere(
        //     origin:
        //         LatLng(currentLocation!.latitude, currentLocation!.longitude),
        //     destination: destiny,
        //     mode: RouteMode.driving);

        // _distance = googleMapPolyline.distance! / 1000;
        // _duration = googleMapPolyline.duration! / 60;

        prevision = DateFormat('HH:mm')
            .format(DateTime.now().add(Duration(minutes: _duration!.round())));
      }

      emit(LoadedAttendanceRouteState(
          currentPosition: currentLocation,
          routeCoords: routeCoords,
          distance: distance,
          duration: duration,
          prevision: prevision));
    } catch (e) {
      debugPrint('Execption $e');
      emit(ErrorAttendanceRouteState(e.toString()));
    }
  }
}
