part of 'pre_hospital_discharge_cubit.dart';

abstract class PreHospitalDischargeState extends Equatable {
  const PreHospitalDischargeState();

  @override
  List<Object> get props => [];
}

class PreHospitalDischargeInitial extends PreHospitalDischargeState {}

class LoadingPreHospitalDischargesState extends PreHospitalDischargeState {}

class ErrorLoadPreHospitalDischargesState extends PreHospitalDischargeState {
  final String message;
  ErrorLoadPreHospitalDischargesState(this.message);
}

class LoadedPreHospitalDischargeState extends PreHospitalDischargeState {
  final List<ReasonPreHospitalDischargeModel>?
      listReasonPreHospitalDischargeModel;
  LoadedPreHospitalDischargeState({this.listReasonPreHospitalDischargeModel});
}
