part of 'subscription_cubit.dart';

abstract class SubscriptionState {
  const SubscriptionState();
}

class SubscriptionInitial extends SubscriptionState {}

class LoadingVerifySubscriptionState extends SubscriptionState {}

class SuccessSendVerifySubscriptionState extends SubscriptionState {
  final bool? signed;
  final int? codStatus;
  SuccessSendVerifySubscriptionState(this.signed, {this.codStatus});
}

class ErrorVerifySubscriptionState extends SubscriptionState {
  final String message;
  ErrorVerifySubscriptionState(this.message);
}
