part of 'delay_reason_cubit.dart';

abstract class DelayReasonState extends Equatable {
  const DelayReasonState();

  @override
  List<Object> get props => [];
}

class DelayReasonInitial extends DelayReasonState {}

class LoadingDelayReasonsState extends DelayReasonState {}

class ErrorLoadDelayReasonsState extends DelayReasonState {
  final String message;
  ErrorLoadDelayReasonsState(this.message);
}

class LoadedDelayReasonsState extends DelayReasonState {
  final List<DelayReasonModel>? delayReasons;
  LoadedDelayReasonsState({this.delayReasons});
}
