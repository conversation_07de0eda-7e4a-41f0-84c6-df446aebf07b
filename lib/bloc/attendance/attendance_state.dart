part of 'attendance_cubit.dart';

abstract class AttendanceState extends Equatable {
  const AttendanceState();
}

class InitialAttendanceState extends AttendanceState {
  @override
  List<Object> get props => [];
}

class LoadingAttendanceState extends AttendanceState {
  @override
  List<Object> get props => [];
}

class ErrorAttendanceState extends AttendanceState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorAttendanceState(this.message);
}

class LoadErrorAttendanceState extends AttendanceState {
  final String message;
  @override
  List<Object> get props => [message];
  LoadErrorAttendanceState(this.message);
}

class LoadedAttendanceListState extends AttendanceState {
  final List<AttendanceModel> attendances;

  @override
  List<Object?> get props => [attendances];

  LoadedAttendanceListState({required this.attendances});
}

class LoadedAttendanceListCacheState extends AttendanceState {
  final List<AttendanceModel> attendances;

  @override
  List<Object> get props => [attendances];

  LoadedAttendanceListCacheState({required this.attendances});
}

class LoadedAttendanceState extends AttendanceState {
  final AttendanceModel attendance;
  final int? step;

  @override
  List<Object?> get props => [attendance, step];

  LoadedAttendanceState({required this.attendance, this.step});
}

class CanceledAttendanceState extends AttendanceState {
  @override
  List<Object> get props => [];
}
