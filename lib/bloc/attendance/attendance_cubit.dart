import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:ambulancia_app/models/funcoes_model.dart';
import 'package:ambulancia_app/shared/api/attendance/attendance_api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'attendance_state.dart';

class AttendanceCubit extends Cubit<AttendanceState> {
  late AttendanceModel _currentAttendance;
  AttendanceModel currentAttendance() => _currentAttendance;

  int _step = 0;
  int get step => _step;

  AttendanceCubit() : super(InitialAttendanceState());

  Future<void> initAttendance({required int numAtendimento}) async {
    try {
      emit(LoadingAttendanceState());

      _currentAttendance = await Locator.instance<AttendanceApi>()
          .getAttendanceDetail('${numAtendimento}');

      emit(LoadedAttendanceState(attendance: _currentAttendance, step: _step));
    } catch (ex) {
      emit(LoadErrorAttendanceState(ex.toString()));
    }
  }

  void setAttendanceStatus(String status) {
    emit(LoadingAttendanceState());
    final _tempAttendance = _currentAttendance.toJson();
    _tempAttendance['status'] = status;
    _currentAttendance = AttendanceModel.fromJson(_tempAttendance);
    emit(LoadedAttendanceState(attendance: _currentAttendance, step: _step));
  }

  void getCurrentAttendance() {
    emit(LoadingAttendanceState());
    emit(LoadedAttendanceState(attendance: _currentAttendance, step: _step));
  }

  void setAttendanceProgress(int index) {
    if (index > _step) {
      try {
        emit(LoadingAttendanceState());
        _step = index;
        final _stepEnum = _getStepByIndex(index);
        final _tempAttendance = _currentAttendance.toJson();
        _tempAttendance['progress'][_stepEnum] =
            DateTime.now().toIso8601String();
        _currentAttendance = AttendanceModel.fromJson(_tempAttendance);
        // _applyAtendance();
        emit(
            LoadedAttendanceState(attendance: _currentAttendance, step: _step));
      } catch (e) {
        emit(ErrorAttendanceState(e.toString()));
      }
    }
  }

  void setPatientRemoval(bool value) {
    emit(LoadingAttendanceState());

    emit(LoadedAttendanceState(attendance: _currentAttendance, step: _step));
  }

  void setClinicalEvaluationParam(String key, dynamic value) {
    emit(LoadingAttendanceState());
    final _tempAttendance = _currentAttendance.toJson();

    if (_tempAttendance['clinicalEvaluation'] == null)
      _tempAttendance['clinicalEvaluation'] = {};

    _tempAttendance['clinicalEvaluation'][key] = value;
    _currentAttendance = AttendanceModel.fromJson(_tempAttendance);

    emit(LoadedAttendanceState(attendance: _currentAttendance, step: _step));
  }

  String _getStepByIndex(int index) {
    return attendanceTimelineSteps.entries
        .toList()[index]
        .key
        .toString()
        .split('.')[1];
  }

  FuncionarioModel getMedAttendance() {
    final FuncionarioModel funcionarioModel = _currentAttendance.equipe
        .where((element) => element.codFuncao == CodFuncoes.MEDICO_SOCORRISTA)
        .toList()
        .first;

    return FuncionarioModel(
        nomeFuncionario: funcionarioModel.nomeFuncionario,
        codFuncionario: funcionarioModel.codFuncionario);
  }

  FuncionarioModel getEnfAttendance() {
    final FuncionarioModel funcionarioModel = _currentAttendance.equipe
        .where((element) => element.codFuncao == CodFuncoes.ENFERMEIRO)
        .toList()
        .first;

    return FuncionarioModel(
        nomeFuncionario: funcionarioModel.nomeFuncionario,
        codFuncionario: funcionarioModel.codFuncionario);
  }
}
