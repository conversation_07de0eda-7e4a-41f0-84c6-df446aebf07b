import 'dart:async';

import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/attendance_status_model.dart';
import 'package:ambulancia_app/models/movements_model.dart';
import 'package:ambulancia_app/shared/api/attendance/attendance_api.dart';
import 'package:ambulancia_app/shared/api/graphql.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart' show IterableExtension;

part 'attendance_movements_state.dart';

class AttendanceMovementsCubit extends Cubit<AttendanceMovementsState> {
  AttendanceMovementsCubit() : super(InitialMovementsState());

  MovementsModel? _movements;
  MovementsModel? getMovements() => _movements;

  int? _currentIndexStatusApp;
  int? getCurrentIndexStatusApp() => _currentIndexStatusApp;

  Future<void> getAttendanceMovements(AttendanceModel attendance) async {
    try {
      emit(LoadingMovementsState());

      _movements = await Locator.instance<GraphQlApi>()
          .getAttendanceMovements(attendance.numAtendimento);

      _currentIndexStatusApp =
          AttendanceStatus.statusApi2StatusApp(attendance.codStatus);

      emit(LoadedMovementsState(movements: _movements));
    } catch (ex) {
      emit(ErrorMovementsState('$ex'));
    }
  }

  Future<void> updateStatusMovement(
      {String? attendance,
      int? indexStatusApp,
      int? currentStep,
      bool isNewAttendance = false,
      String numNewAttendance = ""}) async {
    try {
      emit(UpdatingMovementsState(indexStatusApp));
      final codStatus = AttendanceStatus.statusApp2StatusApi(indexStatusApp);
      await Locator.instance<AttendanceApi>().updateStatusMovements(
        attendance: attendance,
        codStatus: codStatus,
        isNewAttendance: isNewAttendance,
        numNewAttendance: numNewAttendance,
        indexClicked: indexStatusApp,
        currentStep: currentStep,
      );

      _movements = await Locator.instance<GraphQlApi>()
          .getAttendanceMovements(attendance);

      if (indexStatusApp != AttendanceStatus.NOVO_ATENDIMENTO) {
        _currentIndexStatusApp = indexStatusApp;
      }

      emit(UpdatedMovementsState(
          indexStatusApp: indexStatusApp, codeStatus: codStatus));
    } on AttendanceException catch (e) {
      emit(ErrorMovementsState(e.message));
    } catch (e) {
      emit(ErrorMovementsState(e.toString()));
    }
  }

  String? getMovementByIndex(index) {
    if (_movements == null) return null;
    final MovementObject? date = _movements!.moviments.firstWhereOrNull(
        (element) =>
            index == AttendanceStatus.statusApi2StatusApp(element.statusCode));
    return date?.dataMovimentacaoFormated;
  }
}
