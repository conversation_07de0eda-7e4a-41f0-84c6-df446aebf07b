part of 'motive_cubit.dart';

abstract class MotiveState extends Equatable {
  const MotiveState();

  @override
  List<Object> get props => [];
}

class MotiveInitial extends MotiveState {}

class LoadingMotivesState extends MotiveState {}

class ErrorLoadMotivesState extends MotiveState {
  final String message;
  ErrorLoadMotivesState(this.message);
}

class LoadedMotivesState extends MotiveState {
  final List<MotiveModel>? motives;
  LoadedMotivesState({this.motives});
}
