part of 'attendance_movements_cubit.dart';

abstract class AttendanceMovementsState {
  const AttendanceMovementsState();
}

class InitialMovementsState extends AttendanceMovementsState {}

class LoadingMovementsState extends AttendanceMovementsState {}

class UpdatingMovementsState extends AttendanceMovementsState {
  final indexStatusApp;
  UpdatingMovementsState(this.indexStatusApp);
}

class ErrorMovementsState extends AttendanceMovementsState {
  final String? message;
  ErrorMovementsState(this.message);
}

class LoadedMovementsState extends AttendanceMovementsState {
  final MovementsModel? movements;
  LoadedMovementsState({this.movements});
}

class UpdatedMovementsState extends AttendanceMovementsState {
  final indexStatusApp;
  final codeStatus;
  UpdatedMovementsState({this.indexStatusApp, this.codeStatus});
}
