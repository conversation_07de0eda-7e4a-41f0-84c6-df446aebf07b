import 'package:ambulancia_app/models/close-attendanceV2.model.dart';
import 'package:ambulancia_app/shared/api/attendance/attendance_api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/messages.exceptions.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'closse_attendance_state.dart';

class CloseAttendanceCubit extends Cubit<CloseAttendanceState> {
  CloseAttendanceCubit() : super(InitialAttendanceState());

  Future<void> closeAttendance({
    required String pathPhotoOperator,
    required int serviceNumber,
    bool reclassify = false,
    required String recordType,
    String? codTypeReclassify,
    int? delayReasonCode,
    String? delayReasonObservation,
    int? codProtocoloDoenca,
    String? obsProtocoloDoenca,
  }) async {
    try {
      emit(LoadingCloseAttendanceState());

      await Locator.instance<AttendanceApi>().closeAttendance(
        numAtendimento: serviceNumber.toString(),
        pathPhotoOperator: pathPhotoOperator,
        recordType: recordType.toString(),
        reclassify: reclassify,
        codTypeReclassify: codTypeReclassify,
        delayReasonCode: delayReasonCode,
        delayReasonObservation: delayReasonObservation,
        codProtocoloDoenca: codProtocoloDoenca,
        obsProtocoloDoenca: obsProtocoloDoenca,
      );

      emit(CloseAttendanceSuccesState());
    } catch (ex) {
      if (ex is CloseAttendanceDelayReasonException) {
        emit(ErrorCloseAttendanceDelayReasonState(
          ex.message ?? MessageException.errorEndService,
        ));
        return;
      }
      emit(ErrorCloseAttendanceState(ex.toString()));
    }
  }

  Future<void> closeAttendanceV2({
    required CloseAttendanceV2Model closeAttendanceData,
  }) async {
    try {
      emit(LoadingCloseAttendanceState());

      await Locator.instance<AttendanceApi>().closeAttendanceV2(
        closeAttendanceData: closeAttendanceData,
      );

      emit(CloseAttendanceSuccesState());
    } catch (ex) {
      if (ex is CloseAttendanceDelayReasonException) {
        emit(ErrorCloseAttendanceDelayReasonState(
          ex.message ?? MessageException.errorEndService,
        ));
        return;
      }
      emit(ErrorCloseAttendanceState(ex.toString()));
    }
  }
}
