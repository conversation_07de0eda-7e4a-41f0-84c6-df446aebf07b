import 'package:ambulancia_app/bloc/signature_pad/signature_state.dart';
import 'package:ambulancia_app/shared/api/signature.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SignatureCubit extends Cubit<SignaturePadState> {
  final service = Locator.instance.get<SignatureApi>();
  SignatureCubit() : super(SignaturePadInitial());

  void saveSignature({
    required serviceNumber,
    required signatureClient,
    required String reasonAnnexPhoto,
    required cpfOrRgOrCrmClient,
    required String nameClient,
  }) async {
    try {
      emit(LoadingSendSignature());
      final response = await service.sendSignature(
        numAtendimento: serviceNumber,
        pathSignatureClient: signatureClient,
        cpfOrRgOrCrmClient: cpfOrRgOrCrmClient,
        nameClient: nameClient,
      );
      emit(SuccessSendSignature(response));
    } catch (e) {
      emit(ErrorSendSignaturePadState('$e'));
    }
  }

  void verifySubscription(int numAtendimento, int codStatus) async {
    try {
      emit(LoadingVerifySubscription());
      final response =
          await service.verifyExistenceSignature(numAtendimento, codStatus);
      emit(SuccessSendVerifySubscription(response));
    } catch (e) {
      emit(ErrorVerifySubscription('$e'));
    }
  }
}
