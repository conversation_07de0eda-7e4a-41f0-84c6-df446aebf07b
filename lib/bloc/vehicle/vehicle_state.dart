part of 'vehicle_cubit.dart';

abstract class VehicleState extends Equatable {
  const VehicleState();

  @override
  List<Object?> get props => [];
}

class VehicleCubitInitial extends VehicleState {}

class LoadingVehicleState extends VehicleState {}

class LoadedVehicleState extends VehicleState {
  // final List<String> vehicles;
  final List<VehicleModel>? vehicles;
  @override
  List<Object?> get props => [vehicles];

  LoadedVehicleState(this.vehicles);
}

class ErrorVehicleState extends VehicleState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorVehicleState(this.message);
}
