import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:ambulancia_app/models/funcoes_model.dart';
import 'package:ambulancia_app/models/team_vehicle_model.dart';
import 'package:ambulancia_app/shared/api/team.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:equatable/equatable.dart';

part 'team_state.dart';

class TeamCubit extends Cubit<TeamState> {
  TeamCubit() : super(TeamInitial());

  List<FuncionarioModel>? _team;
  List<FuncionarioModel>? get teamModel => _team;

  FuncionarioModel? _motoristaSocrorrista;
  FuncionarioModel? _tecnicoEnfermagem;

  Future<dynamic> getTeamVehicle(
      {String? codUnimed, String? codVeiculo}) async {
    try {
      emit(LoadingTeamState());
      _team = await Locator.instance<TeamApi>()
          .getTeamVehicle(codVeiculo: codVeiculo, codUnimed: codUnimed);

      _motoristaSocrorrista = _team!.firstWhereOrNull(
          (element) => element.codFuncao == CodFuncoes.MOTORISTA_SOCORRISTA);

      _tecnicoEnfermagem = _team!
          .firstWhereOrNull((funcionario) => _isEnfermagemArea(funcionario));

      emit(LoadedTeamState(
          tecnicoEnfermagem: _tecnicoEnfermagem,
          motoristaSocrorrista: _motoristaSocrorrista));
    } on UnimedException catch (e) {
      emit(ErrorTeamState(e.message));
    } catch (e) {
      emit(ErrorTeamState(e));
    }
  }

  bool _isEnfermagemArea(FuncionarioModel funcionario) =>
      funcionario.codFuncao == CodFuncoes.ENFERMEIRO ||
      funcionario.codFuncao == CodFuncoes.TECNICO_ENFERMAGEM ||
      funcionario.codFuncao == CodFuncoes.AUXILIAR_ENFERMAGEM;
}
