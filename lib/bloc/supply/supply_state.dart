import 'package:ambulancia_app/models/supply_model.dart';

abstract class SupplyState {
  const SupplyState();
}

class SupplyInitial extends SupplyState {}

class LoadingSupplyState extends SupplyState {}

class LoadedSupplyState extends SupplyState {
  final List<SupplyModel>? supplies;
  LoadedSupplyState({this.supplies});
}

class ErrorLoadingSupplyState extends SupplyState {
  final message;
  ErrorLoadingSupplyState(this.message);
}

class SavingSupplyState extends SupplyState {}

class RemovingSupplyState extends SupplyState {
  final supplyCode;

  RemovingSupplyState({this.supplyCode});
}

class UpdatingSupplyState extends SupplyState {
  final supplyCode;
  UpdatingSupplyState({this.supplyCode});
}

class CRUDDoneSupplyState extends SupplyState {}

class CRUDErrorSupplyState extends SupplyState {
  final message;
  CRUDErrorSupplyState(this.message);
}
