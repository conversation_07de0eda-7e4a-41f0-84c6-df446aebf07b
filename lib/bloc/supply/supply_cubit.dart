import 'package:ambulancia_app/models/supply_model.dart';
import 'package:ambulancia_app/shared/api/attendance/supplies.api.dart';
import 'package:ambulancia_app/shared/api/graphql.api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';

import 'supply_state.dart';

class SupplyCubit extends Cubit<SupplyState> {
  SupplyCubit() : super(SupplyInitial());

  List<SupplyAttendance> _suppliesAttendance = [];
  List<SupplyAttendance> suppliesAttendance() => _suppliesAttendance;
  setSuppliesAttendance(List<SupplyAttendance> suppliesAttendance) =>
      _suppliesAttendance = suppliesAttendance;

  List<SupplyModel>? _supplies;
  List<SupplyModel>? supplies() => _supplies;

  Future<dynamic> listAvailableSupplies(attendanceNumber) async {
    emit(LoadingSupplyState());

    try {
      _supplies = await Locator.instance
          .get<SuppliesApi>()
          .listAvailableSupplies(attendanceNumber: attendanceNumber);

      emit(LoadedSupplyState(supplies: _supplies));
    } on SuppliesException catch (e) {
      emit(ErrorLoadingSupplyState(e));
    } catch (e) {
      emit(ErrorLoadingSupplyState(e));
    }
  }

  Future<dynamic> saveSupply({
    attendanceNumber,
    quantity,
    required SupplyModel supply,
  }) async {
    emit(SavingSupplyState());
    try {
      // Verifica se o item ja esta na lista
      final idx = _suppliesAttendance
          .indexWhere((e) => e.codigoMaterial == supply.code);
      if (idx >= 0)
        throw CRUDErrorSupplyState('Material já inserido nesse atendimento.')
            .message;

      await Locator.instance.get<SuppliesApi>().saveSupply(
            attendanceNumber: attendanceNumber,
            quantity: quantity,
            supplyCode: supply.code!,
          );

      _suppliesAttendance = [
        SupplyAttendance(
          codigoMaterial: supply.code,
          descricao: supply.description,
          quantidade: quantity,
        ),
        ..._suppliesAttendance,
      ];

      emit(CRUDDoneSupplyState());
    } on SuppliesException catch (e) {
      emit(CRUDErrorSupplyState(e.message));
    } catch (e) {
      emit(CRUDErrorSupplyState('$e'));
    }
  }

  Future<dynamic> removeSupply({attendanceNumber, supplyCode}) async {
    emit(RemovingSupplyState(supplyCode: supplyCode));
    try {
      await Locator.instance.get<GraphQlApi>().removeSupply(
            attendanceNumber: attendanceNumber,
            supplyCode: supplyCode,
          );

      final idx = _suppliesAttendance
          .indexWhere((element) => element.codigoMaterial == supplyCode);
      if (idx >= 0) _suppliesAttendance.removeAt(idx);

      emit(CRUDDoneSupplyState());
    } on SuppliesException catch (e) {
      emit(CRUDErrorSupplyState(e.message));
    } catch (e) {
      emit(CRUDErrorSupplyState('$e'));
    }
  }

  Future<dynamic> updateSupply(
      {attendanceNumber, quantity, required SupplyAttendance supply}) async {
    emit(UpdatingSupplyState(supplyCode: supply.codigoMaterial));
    try {
      await Locator.instance.get<SuppliesApi>().updateSupply(
            attendanceNumber: attendanceNumber,
            quantity: quantity,
            supplyCode: supply.codigoMaterial,
          );

      final idx = _suppliesAttendance.indexWhere(
          (element) => element.codigoMaterial == supply.codigoMaterial);
      if (idx >= 0)
        _suppliesAttendance[idx] = SupplyAttendance(
          codigoMaterial: supply.codigoMaterial,
          descricao: supply.descricao,
          quantidade: quantity,
        );

      emit(CRUDDoneSupplyState());
    } on SuppliesException catch (e) {
      emit(CRUDErrorSupplyState(e.message));
    } catch (e) {
      emit(CRUDErrorSupplyState('$e'));
    }
  }
}
