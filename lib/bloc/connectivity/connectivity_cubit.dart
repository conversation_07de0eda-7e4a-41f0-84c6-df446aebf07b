import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/services/connectivity.service.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'connectivity_state.dart';

class ConnectivityCubit extends Cubit<ConnectivityState> {
  ConnectivityCubit() : super(ConnectivityInitial());

  Future<void> verifyConnection() async {
    try {
      var _connectivityResult =
          await Locator.instance.get<ConnectivityService>().checkConnection();

      (_connectivityResult == true)
          ? emit(ConnectivityOnlineState())
          : emit(ConnectivityOfflineState());
    } catch (ex) {
      emit(ConnectivityOfflineState());
    }
  }
}
