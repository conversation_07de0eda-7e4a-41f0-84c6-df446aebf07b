part of 'auth_cubit.dart';

abstract class AuthState extends Equatable {
  const AuthState();
}

class InitialAuthState extends AuthState {
  @override
  List<Object> get props => [];
}

class LoadingAuthState extends AuthState {
  @override
  List<Object> get props => [];
}

class ErrorAuthState extends AuthState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorAuthState(this.message);
}

class LoadedAuthState extends AuthState {
  final UserModel? user;

  @override
  List<Object?> get props => [user];

  LoadedAuthState(this.user);
}

class LoadingLogoutUserState extends AuthState {
  @override
  List<Object> get props => [];
}

class DoneLogoutUserState extends AuthState {
  @override
  List<Object> get props => [];
}
