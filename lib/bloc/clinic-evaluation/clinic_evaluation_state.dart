part of 'clinic_evaluation_cubit.dart';

abstract class ClinicEvaluationState extends Equatable {
  const ClinicEvaluationState();

  @override
  List<Object?> get props => [];
}

class ClinicEvaluationInitial extends ClinicEvaluationState {}

class LoadingClinicEvaluationState extends ClinicEvaluationState {
  @override
  List<Object> get props => [];
}

class SavingClinicEvaluationState extends ClinicEvaluationState {
  @override
  List<Object> get props => [];
}

class ErrorLoadClinicEvaluationState extends ClinicEvaluationState {
  final String? message;
  @override
  List<Object?> get props => [message];
  ErrorLoadClinicEvaluationState(this.message);
}

class LoadedClinicEvaluationState extends ClinicEvaluationState {
  final ResponseClinicEvaluation? clinicEvaluationData;
  final RequestClinicEvaluation? requestClinicEvaluation;
  @override
  List<Object?> get props => [clinicEvaluationData, requestClinicEvaluation];

  LoadedClinicEvaluationState(
      this.clinicEvaluationData, this.requestClinicEvaluation);
}

class LoadingSendClinicEvaluationState extends ClinicEvaluationState {
  @override
  List<Object> get props => [];
}

class ErrorSendClinicEvaluationState extends ClinicEvaluationState {
  final String message;
  @override
  List<Object> get props => [message];
  ErrorSendClinicEvaluationState(this.message);
}

class SuccessSendClinicEvaluationState extends ClinicEvaluationState {
  final String message;
  @override
  List<Object> get props => [message];

  SuccessSendClinicEvaluationState(this.message);
}
