part of 'update_version_cubit.dart';

abstract class UpdateVersionState extends Equatable {
  const UpdateVersionState();

  @override
  List<Object?> get props => [];
}

class UpdateVersionInitial extends UpdateVersionState {}

class CheckingVersionState extends UpdateVersionState {
  @override
  List<Object> get props => [];
}

class OutOfDateState extends UpdateVersionState {
  final forceUpdate;
  final localVersion;
  final remoteVersion;
  OutOfDateState(
      {required this.localVersion,
      required this.remoteVersion,
      required this.forceUpdate});

  @override
  List<Object?> get props => [localVersion, remoteVersion];
}

class UpdatedState extends UpdateVersionState {
  @override
  List<Object> get props => [];
}

class ErrorCheckingVersionState extends UpdateVersionState {
  final message;
  ErrorCheckingVersionState({this.message});

  @override
  List<Object?> get props => [message];
}
