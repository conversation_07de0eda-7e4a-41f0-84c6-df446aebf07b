import 'dart:convert';
import 'dart:io';

import 'package:ambulancia_app/models/conduct_model.dart';
import 'package:ambulancia_app/models/image_model.dart';
import 'package:ambulancia_app/shared/api/conduct.api.dart';
import 'package:ambulancia_app/shared/api/conduct/conduct_attachment.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/utils/offline-first/sqlite/tables/cid.table.dart';
import 'package:ambulancia_app/shared/utils/photo.constants.dart';
import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'conduct_state.dart';

class ConductCubit extends Cubit<ConductState> {
  ConductCubit() : super(ConductInitial());

  List<CidRecordSQLite> _cidList = [];
  List<CidRecordSQLite> get cidList => _cidList;

  List<ImageModel> _listAttachment = [];
  List<ImageModel> get listAttachment => _listAttachment;

  Future<void> sendConductEvent({required ConductModel conductModel}) async {
    try {
      emit(LoadingSendConductState());

      final _response = await Locator.instance
          .get<ConductApi>()
          .sendConduct(conductModel: conductModel);

      emit(SuccessSendConductState(_response));
    } catch (e) {
      emit(ErrorSendConductState('$e'));
    }
  }

  Future<void> listCidDb(String text) async {
    try {
      emit(LoadingListCidDBState());
      _cidList = await Locator.instance.get<ConductApi>().getListDb(text);
      emit(LoadedListCidDBState(_cidList));
    } catch (e) {
      emit(ErrorLoadListCidDBState('$e'));
    }
  }

  Future<void> sendAttachment({
    required int numAtendimento,
  }) async {
    try {
      emit(LoadingAttachmentState());

      final _response = await Locator.instance
          .get<ConductAttachmentAPI>()
          .getAttachments(serviceNumber: numAtendimento);

      final _responseOflline = await Locator.instance
          .get<ConductAttachmentAPI>()
          .getAttachmentsUpdateCacheRequest(numAtendimento: numAtendimento);

      _response.forEach((element) {
        _listAttachment.add(ImageModel(
          uniurgMotivoAnexoEnum: element.tipoAnexo!,
          fileName: element.nomeArquivo!,
          fileBase64: "",
          imgNumber: element.sequencial!,
        ));
      });

      _responseOflline.forEach((element) {
        _listAttachment.add(ImageModel(
          uniurgMotivoAnexoEnum: element.uniurgMotivoAnexoEnum,
          fileName: element.fileName,
          fileBase64: element.fileBase64,
          imgNumber: generateNumberFile(),
          isNewAttachment: true,
          isCache: false,
        ));
      });

      emit(SuccessAttachmentState(listAttachment: _listAttachment));
    } catch (e) {
      emit(ErrorLoadAttachmentState('$e'));
    }
  }

  Future<void> deleteAttachment({
    required int attachmentsNumber,
    required bool isNewAttachment,
  }) async {
    try {
      emit(LoadingDeleteAttachmentState());

      if (isNewAttachment) {
        _listAttachment
            .removeWhere((element) => element.imgNumber == attachmentsNumber);
        emit(SuccessDeleteAttachmentState());
        return;
      }

      await Locator.instance
          .get<ConductAttachmentAPI>()
          .deleteAttachments(attachmentsNumber: attachmentsNumber);

      _listAttachment
          .removeWhere((element) => element.imgNumber == attachmentsNumber);

      emit(SuccessDeleteAttachmentState());
    } catch (e) {
      emit(ErrorDeleteAttachmentState('$e'));
    }
  }

  Future<void> savePhotoOperator({
    required String pathFile,
    required int serviceNumber,
  }) async {
    try {
      List<int> bytes = await File(pathFile).readAsBytes();
      String base64 = base64Encode(bytes);

      ImageModel _imageOperator;

      emit(LoadingSendOperator());
      _imageOperator = ImageModel(
        uniurgMotivoAnexoEnum: int.parse(photoReasonAnnexEnum.conduta.value),
        fileName: 'c_$serviceNumber.png',
        fileBase64: base64,
        imgNumber: 0,
      );
      emit(SuccessSendPhotoOperator(imageOperator: _imageOperator));
    } catch (e) {
      emit(ErrorSendPhotoOperator(e.toString()));
    }
  }

  void savePhotoInAppDocumentsAttachment(
      {required String pathFile, required int serviceNumber}) async {
    try {
      emit(LoadingSendPhotoAttachment());

      List<int> bytes = await File(pathFile).readAsBytes();
      String base64 = base64Encode(bytes);

      ImageModel _imageModel = ImageModel(
        uniurgMotivoAnexoEnum:
            int.parse(photoReasonAnnexEnum.anexo_conduta.value),
        fileName: _generateFileNameAttachment(serviceNumber: serviceNumber),
        fileBase64: base64,
        imgNumber: generateNumberFile(),
        isNewAttachment: true,
        isCache: true,
      );

      _listAttachment.add(_imageModel);

      emit(
        SuccessAttachmentState(listAttachment: _listAttachment),
      );
    } catch (e) {
      emit(ErrorSendPhotoAttachment(e.toString()));
    }
  }

  String _generateFileNameAttachment({required int serviceNumber}) {
    DateTime now = DateTime.now();
    String timestamp = now.toLocal().millisecondsSinceEpoch.toString();

    return 'anexo_c$serviceNumber$timestamp.png';
  }

  void cleanListAttachment() {
    _listAttachment = [];
  }

  int generateNumberFile() {
    if (_listAttachment.isEmpty) {
      return 1;
    }

    int maiorImgNumber = _listAttachment
        .map((imagem) => imagem.imgNumber)
        .reduce((a, b) => a > b ? a : b);
    return maiorImgNumber + 1;
  }

  void amitListAttachment() {
    emit(SuccessAttachmentState(listAttachment: _listAttachment));
  }

  List<ImageModel> getAttachments() {
    return _listAttachment
        .where((item) => item.isNewAttachment != false)
        .toList();
  }
}
