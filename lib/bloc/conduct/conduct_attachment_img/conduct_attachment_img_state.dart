part of 'conduct_attachment_img_cubit.dart';

abstract class ConductAttachmentImgState extends Equatable {
  const ConductAttachmentImgState();

  @override
  List<Object?> get props => [];
}

class ConductAttachmentImgInit extends ConductAttachmentImgState {}

class LoadingConductAttachmentImg extends ConductAttachmentImgState {
  @override
  List<Object> get props => [];
}

class ErrorConductAttachmentImg extends ConductAttachmentImgState {
  final String message;
  @override
  List<Object> get props => [message];
  ErrorConductAttachmentImg(this.message);
}

class SuccessConductAttachmentImg extends ConductAttachmentImgState {
  final AttachmentImageModel attachmentImg;
  @override
  List<Object?> get props => [attachmentImg];

  SuccessConductAttachmentImg(this.attachmentImg);
}
