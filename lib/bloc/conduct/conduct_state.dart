part of 'conduct_cubit.dart';

abstract class ConductState extends Equatable {
  const ConductState();

  @override
  List<Object?> get props => [];
}

class ConductInitial extends ConductState {}

class LoadingSendConductState extends ConductState {
  @override
  List<Object> get props => [];
}

class LoadingSendConductClosureState extends ConductState {
  @override
  List<Object> get props => [];
}

class ErrorSendConductState extends ConductState {
  final String? message;
  @override
  List<Object?> get props => [message];
  ErrorSendConductState(this.message);
}

class ErrorSendConductClosureState extends ConductState {
  final String? message;
  @override
  List<Object?> get props => [message];
  ErrorSendConductClosureState(this.message);
}

class SuccessSendConductState extends ConductState {
  final String message;
  @override
  List<Object> get props => [message];

  SuccessSendConductState(this.message);
}

class SuccessSendConductClosureState extends ConductState {
  final String message;
  @override
  List<Object> get props => [message];

  SuccessSendConductClosureState(this.message);
}

class LoadingListCidDBState extends ConductState {
  @override
  List<Object> get props => [];
}

class ErrorLoadListCidDBState extends ConductState {
  final String message;
  @override
  List<Object> get props => [message];
  ErrorLoadListCidDBState(this.message);
}

class LoadedListCidDBState extends ConductState {
  final List<CidRecordSQLite> list;
  @override
  List<Object> get props => [list];

  LoadedListCidDBState(this.list);
}

class LoadingListCidState extends ConductState {
  @override
  List<Object> get props => [];
}

class ErrorLoadListCidState extends ConductState {
  final String message;
  @override
  List<Object> get props => [message];
  ErrorLoadListCidState(this.message);
}

class LoadedListCidState extends ConductState {
  final String message;
  @override
  List<Object> get props => [message];

  LoadedListCidState(this.message);
}

class LoadingAttachmentState extends ConductState {
  @override
  List<Object> get props => [];
}

class ErrorLoadAttachmentState extends ConductState {
  final String message;
  @override
  List<Object> get props => [message];
  ErrorLoadAttachmentState(this.message);
}

class LoadedAttachmentState extends ConductState {
  final String message;
  @override
  List<Object> get props => [message];

  LoadedAttachmentState(this.message);
}

class SuccessAttachmentState extends ConductState {
  final List<ImageModel> listAttachment;
  @override
  List<Object?> get props => [listAttachment];

  SuccessAttachmentState({required this.listAttachment});
}

class LoadingDeleteAttachmentState extends ConductState {
  @override
  List<Object> get props => [];
}

class ErrorDeleteAttachmentState extends ConductState {
  final String message;
  @override
  List<Object> get props => [message];
  ErrorDeleteAttachmentState(this.message);
}

class SuccessDeleteAttachmentState extends ConductState {
  @override
  List<Object?> get props => [];
}

class LoadingSendOperator extends ConductState {
  @override
  List<Object> get props => [];
}

class SuccessSendPhotoOperator extends ConductState {
  final ImageModel imageOperator;
  SuccessSendPhotoOperator({required this.imageOperator});
}

class ErrorSendPhotoOperator extends ConductState {
  final String message;

  ErrorSendPhotoOperator(this.message);
}

class LoadingSendPhotoOperator extends ConductState {
  @override
  List<Object> get props => [];
}

class LoadingSendPhotoAttachment extends ConductState {
  @override
  List<Object> get props => [];
}

class SuccessSendPhotoAttachment extends ConductState {
  final List<ImageModel> listAttachment;
  SuccessSendPhotoAttachment({required this.listAttachment});
}

class ErrorSendPhotoAttachment extends ConductState {
  final String message;

  ErrorSendPhotoAttachment(this.message);
}
