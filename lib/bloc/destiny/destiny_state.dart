part of 'destiny_cubit.dart';

abstract class DestinyState extends Equatable {
  const DestinyState();

  @override
  List<Object> get props => [];
}

class DestinyInitial extends DestinyState {}

class LoadingDestiniesState extends DestinyState {}

class LoadedDestiniesState extends DestinyState {
  final DestinyModel? destinyModel;
  LoadedDestiniesState(this.destinyModel);
}

class UpdatingDestiniesState extends DestinyState {
  final DestinyObject selectedDestiny;
  UpdatingDestiniesState(this.selectedDestiny);
}

class UpdatedDestiniesState extends DestinyState {}

class ErrorDestinyState extends DestinyState {
  final message;
  ErrorDestinyState(this.message);
}
