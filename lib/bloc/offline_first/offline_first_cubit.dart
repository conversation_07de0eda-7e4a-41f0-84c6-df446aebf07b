import 'package:ambulancia_app/shared/utils/offline-first/offline_first.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'offline_first_state.dart';

class OfflineFirstCubit extends Cubit<OfflineFirstState> {
  OfflineFirstCubit() : super(OfflineFirstInitial());

  bool initSync = false;
  bool forceSync = false;

  Future<void> syncDatabase({required bool connectedToTheInternet}) async {
    try {
      if (connectedToTheInternet) {
        if (!initSync && !forceSync) {
          initSync = true;
          emit(OfflineFirstStartingSync());

          await OfflineFirst.syncDatabase();
          emit(OfflineFinishingTheSynchronization());
          initSync = false;
        }
      }
    } catch (e) {
      emit(OfflineFinishingTheSynchronization());
    }
  }

  Future<void> forceSyncDatabase({required bool connectedToTheInternet}) async {
    try {
      if (connectedToTheInternet) {
        if (initSync == false && !forceSync) {
          forceSync = true;
          emit(OfflineFirstStartingSync());

          await OfflineFirst.syncDatabase();
          emit(OfflineFinishingTheSynchronization());
          forceSync = false;
        }
      }
    } catch (e) {
      emit(OfflineFinishingTheSynchronization());
    }
  }
}
