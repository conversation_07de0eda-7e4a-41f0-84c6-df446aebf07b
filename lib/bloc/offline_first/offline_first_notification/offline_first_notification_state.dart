part of 'offline_first_notification_cubit.dart';

abstract class OfflineFirstNotificationState extends Equatable {
  const OfflineFirstNotificationState();

  @override
  List<Object?> get props => [];
}

class OfflineFirstNotificationInitial extends OfflineFirstNotificationState {}

class OfflineFirstNotificationStartingSync
    extends OfflineFirstNotificationState {}

class OfflineFirstNotificationFinishing extends OfflineFirstNotificationState {
  final int amountsOfOfflineData;
  OfflineFirstNotificationFinishing({this.amountsOfOfflineData = 0});
}

class OfflineFirstNotificationError extends OfflineFirstNotificationState {}
