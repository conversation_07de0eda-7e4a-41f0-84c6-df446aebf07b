part of 'last_synchronization_dates_cubit.dart';

abstract class LastSynchronizationDatesState extends Equatable {
  const LastSynchronizationDatesState();

  @override
  List<Object?> get props => [];
}

class LastSynchronizationDatesInitial extends LastSynchronizationDatesState {}

class LastSynchronizationDatesLoading extends LastSynchronizationDatesState {}

class LastSynchronizationDatesFinishing extends LastSynchronizationDatesState {}

class LastSynchronizationDatesLoaded extends LastSynchronizationDatesState {
  final String lastSynchronizationDate;

  LastSynchronizationDatesLoaded(this.lastSynchronizationDate);
}
