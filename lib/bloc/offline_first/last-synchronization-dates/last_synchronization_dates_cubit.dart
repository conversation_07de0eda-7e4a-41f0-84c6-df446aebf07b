import 'package:ambulancia_app/models/user_model.dart';
import 'package:ambulancia_app/shared/api/auth.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:ambulancia_app/shared/utils/datetime_utils.dart';
import 'package:ambulancia_app/shared/utils/offline-first/last-synchronization-dates/last_synchronization_dates.dart';
import 'package:ambulancia_app/shared/utils/sync.utils.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'last_synchronization_dates_state.dart';

class LastSynchronizationDatesCubit
    extends Cubit<LastSynchronizationDatesState> {
  LastSynchronizationDatesCubit() : super(LastSynchronizationDatesInitial());

  Future<void> getLastSynchronizationDate({required int? page}) async {
    try {
      emit(LastSynchronizationDatesLoading());

      final LoginDataModel? loginDataModel =
          await Locator.instance.get<AuthApi>().getCredentials();

      if (loginDataModel == null ||
          loginDataModel.vehicleModel == null ||
          loginDataModel.vehicleModel!.codVeiculo == null) {
        emit(LastSynchronizationDatesFinishing());
        return;
      }

      String key =
          '${SYNC_CATEGORY_API.AttendanceApi.name}${SYNC_ATTENDANCEAPI_REQUEST.getListAttendance.name}${loginDataModel.vehicleModel!.codVeiculo}_${page}';

      var dateResult = await Locator.instance.get<LastSynchronizationDates>()
          .getLastDateTimeUpdate(key);

      if (dateResult != null) {
        emit(
          LastSynchronizationDatesLoaded(
            DateTimeUtils.getTimeFormat(dateTime: dateResult, send: true),
          ),
        );
      } else {
        emit(LastSynchronizationDatesFinishing());
      }
    } catch (e) {
      emit(LastSynchronizationDatesFinishing());
    }
  }

  Future<void> removeAllLastSynchronizationDates() async {
    try {
      emit(LastSynchronizationDatesLoading());
      await Locator.instance.get<LastSynchronizationDates>()
          .removeAllLastSynchronizationDates();
      emit(LastSynchronizationDatesFinishing());
    } catch (e) {
      emit(LastSynchronizationDatesFinishing());
    }
  }
}
