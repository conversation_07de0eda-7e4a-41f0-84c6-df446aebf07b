import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/models/funcionario_model.dart';
import 'package:ambulancia_app/models/funcoes_model.dart';
import 'package:ambulancia_app/shared/api/team.api.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:equatable/equatable.dart';

part 'attendance_team_state.dart';

class SelectDoctorModel {
  SelectDoctorModel(
      {required this.selectedDoctor, required this.numbAtendimento});
  FuncionarioModel selectedDoctor;
  String numbAtendimento;
}

class AttendanceTeamCubit extends Cubit<AttendanceTeamState> {
  AttendanceTeamCubit() : super(AttendanceTeamInitial());

  List<FuncoesModel> _funcaoList = [];
  List<FuncoesModel> get funcaoList => _funcaoList;

  FuncoesModel? availableDoctors;
  FuncoesModel? availableNurses;

  SelectDoctorModel? selectDoctorModel;

  Future<dynamic> getAttendanceTeam({required int? codUnimed}) async {
    try {
      emit(LoadingAttendanceTeamState());

      await _getAvailableTeam(codUnimed);
      emit(LoadedAttendanceTeamState(
          availableDoctors: availableDoctors,
          availableNurses: availableNurses));
    } catch (e) {
      emit(ErrorAttendanceTeamState(e.toString()));
    }
  }

  Future<dynamic> updateAttendanceTeam({
    required AttendanceModel attendance,
    int? codMedicoSubstituto,
    int? codEnfermeiroSubstituto,
  }) async {
    try {
      emit(UpdatingAttendanceTeamState());

      final medicoAtendimento = attendance.equipe.firstWhereOrNull(
          (element) => element.codFuncao == CodFuncoes.MEDICO_SOCORRISTA);

      final enferemeiroAtendimento = attendance.equipe.firstWhereOrNull(
          (element) => element.codFuncao == CodFuncoes.ENFERMEIRO);

      await Locator.instance<TeamApi>().updateAttendanceTeam(
          numAtendimento: '${attendance.numAtendimento}',
          codMedico: '${medicoAtendimento?.codFuncionario ?? ''}',
          codMedicoSubstituto: '${codMedicoSubstituto ?? ''}',
          codEnfermeiro: '${enferemeiroAtendimento?.codFuncionario ?? ''}',
          codEnfermeiroSubstituto: '${codEnfermeiroSubstituto ?? ''}');

      for (FuncionarioModel funcionario in availableDoctors!.funcionarios) {
        if (funcionario.codFuncionario == codMedicoSubstituto) {
          selectDoctorModel = SelectDoctorModel(
            selectedDoctor: funcionario,
            numbAtendimento: attendance.numAtendimento.toString(),
          );
        }
      }

      emit(UpdatedAttendanceTeamState(
        lastCodNurse: codEnfermeiroSubstituto,
        lastcodDoctor: codMedicoSubstituto,
        availableDoctors: availableDoctors,
        availableNurses: availableNurses,
      ));
    } catch (e) {
      emit(ErrorUpdatingAttendanceTeamState(e.toString()));
    }
  }

  _getAvailableTeam(codUnimed) async {
    _funcaoList = await Locator.instance<TeamApi>()
        .getAvailableTeam(codUnimed: codUnimed);

    availableDoctors = await funcaoList.firstWhereOrNull(
        (element) => element.codFuncao == CodFuncoes.MEDICO_SOCORRISTA);
    availableNurses = await funcaoList.firstWhereOrNull(
        (element) => element.codFuncao == CodFuncoes.ENFERMEIRO);
  }
}
