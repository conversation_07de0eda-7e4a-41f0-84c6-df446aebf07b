abstract class RegisterPhotoState {
  const RegisterPhotoState();
}

class Register<PERSON>hotoInitial extends RegisterPhotoState {}

class SubimitSignature extends RegisterPhotoState {}

class LoadingSendPhoto extends RegisterPhotoState {}

class LoadingSendPhotoMedic extends RegisterPhotoState {}

class SuccessSendPhoto extends RegisterPhotoState {
  final String? message;
  SuccessSendPhoto(this.message);
}

class SuccessSendPhotoMedic extends RegisterPhotoState {
  final String? message;
  SuccessSendPhotoMedic(this.message);
}

class ErrorSendPhotoState extends RegisterPhotoState {
  final String message;

  ErrorSendPhotoState(this.message);
}
