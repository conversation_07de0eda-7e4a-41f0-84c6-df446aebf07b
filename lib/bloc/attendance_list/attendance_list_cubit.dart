import 'package:ambulancia_app/models/attendance_model.dart';
import 'package:ambulancia_app/shared/api/attendance/attendance_api.dart';
import 'package:ambulancia_app/shared/exceptions.dart';
import 'package:ambulancia_app/shared/locator.dart';
import 'package:bloc/bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

part 'attendance_list_state.dart';

class AttendanceListCubit extends Cubit<AttendanceListState> {
  AttendanceListCubit() : super(AttendanceListInitial());

  List<AttendanceModel> listattendances = [];
  List<AttendanceModel> get attendances => listattendances;

  int get totalRecords => _totalRecords;

  int _totalRecords = 0;

  int numberOfpages = 1;

  int? _currentPage = 1;
  setCurrentPage(currentPage) => _currentPage = currentPage;
  get currentPage => _currentPage;

  Future<void> getAttendances(
      {codUnimed, codVeiculo, paginaAtual, quantidadeRegistros}) async {
    try {
      emit(LoadingAttendancesListState());
      _currentPage = paginaAtual;
      final _listAttendance =
          await Locator.instance<AttendanceApi>().getAttendances(
        codUnimed: codUnimed,
        codVeiculo: codVeiculo,
        paginaAtual: paginaAtual,
        quantidadeRegistros: quantidadeRegistros,
      );

      if (_listAttendance.length > 0) {
        listattendances = _listAttendance;
      }

      if (_listAttendance.length == 0) {
        emit(NoDataListState());
      } else {
        emit(LoadedAttendancesListState(attendances: listattendances));
      }
    } on InternalServerError catch (e) {
      emit(ErrorAttendancesListState(e.message));
    } on NotFoundError catch (e) {
      emit(ErrorAttendancesListState(e.message));
    } on AttendanceException catch (e) {
      if (listattendances.length > 0) {
        emit(LoadedAttendancesListState(attendances: listattendances));
      } else {
        emit(ErrorAttendancesListState(e.message));
      }
    } catch (e) {
      if (listattendances.length > 0) {
        emit(LoadedAttendancesListState(attendances: listattendances));
      } else {
        emit(ErrorAttendancesListState(e.toString()));
      }
    }
  }

  Future<void> updateAttendanceLocalList(
      {required AttendanceModel? attendance}) async {
    try {
      emit(LoadingAttendancesListState());

      if (attendance == null)
        emit(LoadedAttendancesListState(attendances: listattendances));

      AttendanceModel attendanceCloud = await Locator.instance<AttendanceApi>()
          .getAttendanceDetail(attendance!.numAtendimento.toString());

      final idx = listattendances.indexWhere(
          (element) => element.numAtendimento == attendance.numAtendimento);
      if (idx >= 0) {
        listattendances[idx] = listattendances[idx].copyWith(
          numAtendimento: attendanceCloud.numAtendimento,
          codUnimed: attendanceCloud.codUnimed,
          codTipoAtendimento: attendanceCloud.codTipoAtendimento,
          codStatus: attendanceCloud.codStatus,
          nomeStatus: attendanceCloud.nomeStatus,
          dataAtendimento: attendanceCloud.dataAtendimento,
          remocao: attendanceCloud.remocao,
          codDiagnostico: attendanceCloud.codDiagnostico,
          codTipoCliente: attendanceCloud.codTipoCliente,
          codVeiculo: attendanceCloud.codVeiculo,
          cod_TipoAtend_Reclas: attendanceCloud.cod_TipoAtend_Reclas,
          codigoCarteira: attendanceCloud.codigoCarteira,
          codigoDestinoPaciente: attendanceCloud.codigoDestinoPaciente,
          cpf: attendanceCloud.cpf,
          dataNascimento: attendanceCloud.dataNascimento,
          descricaoDestinoPaciente: attendanceCloud.descricaoDestinoPaciente,
          descricaoDiagnostico: attendanceCloud.descricaoDiagnostico,
          descricaoTerapeutica: attendanceCloud.descricaoTerapeutica,
          dvCarteira: attendanceCloud.dvCarteira,
          enderecoAtendimento: attendanceCloud.enderecoAtendimento,
          enderecoDestino: attendanceCloud.enderecoDestino,
          equipe: attendanceCloud.equipe,
          hipoteseDiagnostica: attendanceCloud.hipoteseDiagnostica,
          idade: attendanceCloud.idade,
          indentificaoAvClinica: attendanceCloud.indentificaoAvClinica,
          indentificaoConduta: attendanceCloud.indentificaoConduta,
          isReclassifica: attendanceCloud.isReclassifica,
          nome: attendanceCloud.nome,
          nomeTipoCliente: attendanceCloud.nomeTipoCliente,
          observacaoConduta: attendanceCloud.observacaoConduta,
          observacaoMedicoRegulador: attendanceCloud.observacaoMedicoRegulador,
          observacaoProcedimento: attendanceCloud.observacaoProcedimento,
          materiais: attendanceCloud.materiais,
          rg: attendanceCloud.rg,
          sintomaAcontecendo: attendanceCloud.sintomaAcontecendo,
          sintomaComecou: attendanceCloud.sintomaComecou,
          sintomaHistorico: attendanceCloud.sintomaHistorico,
          sintomaQuando: attendanceCloud.sintomaQuando,
          solicitaReclassifTipoAtend:
              attendanceCloud.solicitaReclassifTipoAtend,
          unimedCarteira: attendanceCloud.unimedCarteira,
        );
      }

      emit(LoadedAttendancesListState(attendances: listattendances));
    } catch (e) {
      emit(ErrorAttendancesListState(e.toString()));
    }
  }

  Future<void> checkPagination() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    final _totalRecords =
        prefs.getInt(Locator.instance<AttendanceApi>().totalRecordsPersist);
    if (_totalRecords! > 10) {
      this._totalRecords = _totalRecords;

      numberOfpages = (_totalRecords / 10).ceil();
    }
  }
}
