


abstract class PermissionCubitState {}

class PermissionInitial extends PermissionCubitState {}

class PermissionLoading extends PermissionCubitState {}

class PermissionLoaded extends PermissionCubitState {

}

class PermissionAppDeniedState extends PermissionCubitState {
  final String message;

  PermissionAppDeniedState(this.message);
}

class GpsDetectorError extends PermissionCubitState {
  final String message;

  GpsDetectorError(this.message);
}

class GPSdeativatedState extends PermissionCubitState{
   final String message;
   GPSdeativatedState(this.message);
}