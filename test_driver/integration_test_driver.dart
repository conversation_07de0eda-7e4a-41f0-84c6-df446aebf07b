import 'dart:io';

import 'package:integration_test/integration_test_driver.dart';

Future<void> main() async {
  await Process.run(
    'adb',
    [
      'shell',
      'pm',
      'grant',
      'br.com.unimedfortaleza.mobile.ambulancia',
      'android.permission.ACCESS_FINE_LOCATION',
    ],
  );

  await Process.run(
    'adb',
    [
      'shell',
      'pm',
      'grant',
      'br.com.unimedfortaleza.mobile.ambulancia',
      'android.permission.INTERNET',
    ],
  );

  await Process.run(
    'adb',
    [
      'shell',
      'pm',
      'grant',
      'br.com.unimedfortaleza.mobile.ambulancia',
      'android.permission.WRITE_EXTERNAL_STORAGE',
    ],
  );

  await Process.run(
    'adb',
    [
      'shell',
      'pm',
      'grant',
      'br.com.unimedfortaleza.mobile.ambulancia',
      'android.permission.READ_EXTERNAL_STORAGE',
    ],
  );

  await Process.run(
    'adb',
    [
      'shell',
      'pm',
      'grant',
      'br.com.unimedfortaleza.mobile.ambulancia',
      'android.permission.READ_MEDIA_IMAGES',
    ],
  );

  await Process.run(
    'adb',
    [
      'shell',
      'pm',
      'grant',
      'br.com.unimedfortaleza.mobile.ambulancia',
      'android.permission.CAMERA',
    ],
  );

  await Process.run(
    'adb',
    [
      'shell',
      'pm',
      'grant',
      'br.com.unimedfortaleza.mobile.ambulancia',
      'android.permission.WAKE_LOCK',
    ],
  );

  await Process.run(
    'adb',
    [
      'shell',
      'pm',
      'grant',
      'br.com.unimedfortaleza.mobile.ambulancia',
      'android.permission.VIBRATE',
    ],
  );

  await Process.run(
    'adb',
    [
      'shell',
      'pm',
      'grant',
      'br.com.unimedfortaleza.mobile.ambulancia',
      'android.permission.POST_NOTIFICATIONS',
    ],
  );

  await integrationDriver();
}
