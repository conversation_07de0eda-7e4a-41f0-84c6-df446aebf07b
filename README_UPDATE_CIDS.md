# ATUALIZAÇÃO DE CIDS DO PROJETO

## Passo a passo

1 - Salve o json dos cids retornado do serviço. curl --location --request GET 'https://osbhom.unimedfortaleza.com.br/unimedurgente/diagnostico/listar/todos';

2 - <PERSON><PERSON><PERSON> convers<PERSON>, ou serviços nodeJS, transforme o arquivo acima de json para csv utf-08, deve se atentar em selecionar a divisão sendo "|". Um exemplo de aplicações online que fazem isso é o [tableconverte](https://tableconvert.com/pt/json-to-csv). 

3 - <PERSON>bra o arquivo, em feramentas apropriadas como o LibreOffice calc ou o Excel da microsoft.

4 - Após finalizar o passo acima, em alguns casos é criado um titulo para as colunas, remova toda a linha de titulo, deixando só os números de cids, dígito e a descrição do mesmo, normalmente fica na celulas A1, B1, e C1.

5 - Depois pode ser notado, que existe um espaço nos dígitos do cids, selecione toda a coluna (normalmente a coluna B) no própria ferramenta vá em formatação, alinhamento e alinhe para a esquerda.

6 - Após o passo acima, existe também, espaço no começo de cada descrição de cids, que também deve ser removido. Existe várias técnicas para isso usando a propria ferramenta LibreOffice calc ou o Excel da microsoft. No caso na descrição desse documento foi usado o LibreOffice calc. Siga estas etapas:

- Abra o arquivo CSV no LibreOffice Calc.
- No menu superior, clique em "Ferramentas".
- No menu suspenso, selecione "Macros" e, em seguida, selecione "Editar Macros".
- Isso abrirá o Editor de Macros do LibreOffice Calc, onde você poderá inserir o código fornecido na resposta anterior. Após inserir o código, feche o Editor de Macros e siga as etapas restantes para executar a macro.

```Basic
Sub RemoverEspacamento()
    Dim oPlan As Object
    Dim oSheet As Object
    Dim oCell As Object
    Dim iRow As Long
    Dim iCol As Long
    
    oPlan = ThisComponent
    oSheet = oPlan.Sheets(0) ' Selecione a planilha correta se houver várias
    
    For iRow = 0 To oSheet.Rows.Count - 1
        For iCol = 0 To oSheet.Columns.Count - 1
            oCell = oSheet.getCellByPosition(iCol, iRow)
            oCell.String = Trim(oCell.String)
        Next iCol
    Next iRow
End Sub 
```
- Após adicionado a função acima, selecione toda as linhas da coluna de descrição de cids, normalmente é a coluna C, depois no menu localizado no topo do software, clique em Ferramentas/Macros/Executar Macros...

- Aparecera um popup com o titulo de seletor macros, selecione em na aba bibliotecas as minhas macros/Standart. Na tabela ao lado chamado Nome de macros, é para aparecer o que você salvou no passo anteriror, clique em RemoverEspacamento e selecione o botão "Executar".

>⚠️ Essa parte pode demorar um pouco, dependendo da máquina onde está sendo executado, pois irar remover de cada célula, selecionadas anteriormente. Aguarde até que o a última célula seja formatada.

- Agora é só esperar ser executado em todas as celulas, assim que finalizar, renomei o arquivo csv para seguir o padrão que é cid_datadeatualização.csv.

7 - Importe para dentro do projeto flutter em assets/files/

8 - Vá em lib/shared/api/conduct.api.dart onde tem onst PATH_CSV = 'assets/files/cid_20230221.csv', altere para o que você acabou de criar.

9 - Agora é só executar o projeto, caso tenha feito todo o passo a passo correto, é para aparecer em conduta no campo de diagnostico a lista de cids atualizadas.

>⚠️ Lembrando que a tecnica usado acima para remover espaçamento das celulas, é apenas uma solução existe várias outras, como por exemplo remover do próprio arquivo json antes de executar o conversor para csv.
