analyzer:
  plugins:
    - dart_code_metrics

dart_code_metrics:
  anti-patterns:
    - long-method
    - long-parameter-list
  metrics:
    cyclomatic-complexity: 20
    maximum-nesting-level: 5
    number-of-parameters: 4
    source-lines-of-code: 100
  technical-debt:
      threshold: 1
      todo-cost: 161
      ignore-cost: 320
      ignore-for-file-cost: 396
      as-dynamic-cost: 322
      deprecated-annotations-cost: 37
      file-nullsafety-migration-cost: 41
  metrics-exclude:
    - test/**
  flutter: 
    - prefer-const-border-radius
    - prefer-using-list-view
    - avoid-expanded-as-spacer
  rules:
    - avoid-duplicate-exports
    - newline-before-return
    - no-boolean-literal-compare
    - no-empty-block
    # - prefer-trailing-comma
    # - prefer-conditional-expressions
    - no-equal-then-else
    - prefer-first
